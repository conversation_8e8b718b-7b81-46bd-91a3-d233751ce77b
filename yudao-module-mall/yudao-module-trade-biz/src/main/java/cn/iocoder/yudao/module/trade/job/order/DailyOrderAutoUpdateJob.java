package cn.iocoder.yudao.module.trade.job.order;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.trade.service.order.TradeOrderUpdateService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 日常活动订单自动更新状态 Job
 * 更新退款完成的订单为取消状态
 *
 * <AUTHOR>
 */
@Component
public class DailyOrderAutoUpdateJob implements JobHandler {

    @Resource
    private TradeOrderUpdateService tradeOrderUpdateService;

    @Override
    @TenantJob
    public String execute(String param) {
        int count = tradeOrderUpdateService.updateDailyOrderStatusWhenRefundComplete();
        return String.format("自动取消活动订单 %s 个", count);
    }

}
