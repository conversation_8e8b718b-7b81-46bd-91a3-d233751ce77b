package cn.iocoder.yudao.module.trade.job.aftersale;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.trade.service.aftersale.AfterSaleService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 联赛订单自动退款 Job
 * <AUTHOR>
 */
@Component
public class LeagueOrderAutoRefundJob implements JobHandler {
    @Resource
    private AfterSaleService afterSaleService;

    @Override
    @TenantJob
    public String execute(String param) {
        //退款有几种情况需要退款
        //1、报名中：垫付人退款，该队所有人订单取消：扫描报名中的联赛的所有球队，当球队没有垫付人，取消该队所有订单
        //2、待生成联赛，超出报名数量的球队：扫描待开始联赛的所有球队，保留满足最少球队人数的球队，如果数量仍超过，则保留最早凑齐人的球队（根据最后报名人的时间），其他球队所有人订单取消
        //3、报名成功，球员AA总和大于总费用，多余费用，除以人头数进行退回：在2的基础上，保留的球队进行退费
        //4、报名球队小于最小队伍数量，全部订单取消：扫描处于待退款状态的联赛，全部退款

        //1、待退款：取消所有订单，但是保留报名记录
        //2、报名中：垫付人退款，该队所有人订单取消：扫描报名中的联赛的所有球队，当球队没有垫付人，取消该队所有订单，删除报名记录
        //3、待开始：扫描待开始状态的联赛，获取报名成功的球队，其他球队所有人订单取消，但是保留报名记录，
        int cancelCount = afterSaleService.afterSaleRefundingLeagueOrderBySystem();
        cancelCount += afterSaleService.afterSaleCoverPayerCancelOrderBySystem();
        cancelCount += afterSaleService.afterSaleRegistrationFailedOrderBySystem();

        //4、待开始：扫描待待开始状态的联赛，获取报名成功的球队，球员AA总和大于总费用，多余费用，除以人头数进行退回。
        int partlyRefundCount = afterSaleService.afterSalePartlyRefundLeagueOrderBySystem();
        return String.format("联赛取消、报名失败全部退款订单 %s 个，报名截止，成功队伍的部分退款订单 %s 个", cancelCount, partlyRefundCount);
    }

}
