# 赛点篮球应用-球员数据生涯模块设计文档

## 1. 模块概述

### 1.1 设计目标
生涯模块旨在为用户提供完整的篮球数据记录、展示和分析服务，包括：
- 个人基础信息管理
- 基于科学算法的能力值评估
- 详细的比赛数据统计和可视化
- 动态的成长轨迹记录

### 1.2 核心特性
- **多维度能力评估**：基于7个维度（效率、得分、篮板、助攻、防守、失误、犯规）的科学评分
- **位置差异化**：针对5个篮球位置的不同权重计算
- **动态能力值**：根据比赛结果实时调整的激励机制
- **数据标准化**：统一不同赛制的数据口径
- **可视化展示**：雷达图、数据海报等多种展示方式
- **连胜数据追踪**：完整的历史连胜数据记录和分析

## 2. 数据库设计

### 2.1 连胜数据存储方案深度分析

#### 2.1.1 方案对比矩阵

| 方案 | 存储位置 | 数据完整性 | 查询性能 | 存储成本 | 维护复杂度 | 扩展性 | 推荐指数 |
|------|----------|------------|----------|----------|------------|---------|----------|
| **方案A：主表存储** | sd_player表 | ❌ 历史丢失 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ❌ 差 | ⭐⭐ |
| **方案B：统计表存储** | sd_player_stats表 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **方案C：专门记录表** | sd_streak_record表 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **方案D：混合存储** | 多表组合 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

#### 2.1.2 方案详细分析

##### 方案A：主表存储（不推荐）
```sql
-- 只在球员主表存储当前连胜
ALTER TABLE sd_player ADD COLUMN current_streak INT DEFAULT 0;
```

**优势**：
- 查询性能最优，单表查询
- 实现简单，无额外复杂度
- 存储成本最低

**劣势**：
- 历史数据完全丢失
- 无法做趋势分析
- 不支持多维度查询（按赛季、比赛类型）
- 缺少关键统计信息（最大连胜、胜率等）

**适用场景**：仅需要展示当前连胜状态的简单应用

##### 方案B：统计表存储（推荐）
```sql
-- 在统计表中存储连胜数据
ALTER TABLE sd_player_stats ADD COLUMN current_streak INT DEFAULT 0;
ALTER TABLE sd_player_stats ADD COLUMN max_win_streak INT DEFAULT 0;
ALTER TABLE sd_player_stats ADD COLUMN max_lose_streak INT DEFAULT 0;
-- ... 其他连胜相关字段
```

**优势**：
- 完整保存历史数据，支持多赛季查询
- 支持按比赛类型分类统计
- 与现有统计系统整合度高
- 查询性能良好，可通过索引优化

**劣势**：
- 查询稍微复杂，需要指定统计类型
- 单条记录字段较多，可能影响查询效率

**适用场景**：需要历史数据分析但对详细记录要求不高

##### 方案C：专门记录表（备选）
```sql
-- 创建专门的连胜记录表
CREATE TABLE sd_player_streak_record (
    id BIGINT PRIMARY KEY,
    player_id BIGINT,
    season VARCHAR(20),
    game_type TINYINT,
    streak_type TINYINT, -- 1:连胜 2:连败
    streak_count INT,
    start_game_id BIGINT,
    end_game_id BIGINT,
    start_date DATE,
    end_date DATE,
    is_current TINYINT,
    is_season_best TINYINT,
    is_career_best TINYINT
);
```

**优势**：
- 数据结构最清晰，每条记录代表一个连胜/连败周期
- 支持详细的连胜历史追踪
- 可以精确记录连胜开始和结束的比赛
- 扩展性最强，支持复杂的数据分析

**劣势**：
- 查询复杂度最高，需要多表关联
- 存储成本相对较高
- 数据更新逻辑最复杂

**适用场景**：需要详细连胜记录分析的专业应用

##### 方案D：混合存储（最优推荐）
```sql
-- 组合方案：统计表 + 主表冗余 + 可选记录表
-- 1. 主表存储当前连胜（性能查询）
ALTER TABLE sd_player ADD COLUMN current_streak INT DEFAULT 0;

-- 2. 统计表存储历史数据（历史分析）
ALTER TABLE sd_player_stats ADD COLUMN current_streak INT DEFAULT 0;
-- ... 其他统计字段

-- 3. 可选：记录表存储详细历史（深度分析）
CREATE TABLE sd_player_streak_record (...);
```

**优势**：
- 兼具高性能查询和完整历史数据
- 支持多层次的数据分析需求
- 数据冗余提供容错能力
- 可根据业务需要选择查询策略

**劣势**：
- 实现复杂度最高
- 需要保证多处数据一致性
- 存储成本相对较高

#### 2.1.3 业务场景需求分析

| 业务场景 | 频率 | 数据需求 | 性能要求 | 推荐方案 |
|----------|------|----------|----------|----------|
| 球员卡片展示当前连胜 | 极高 | 当前连胜数 | <50ms | 主表查询 |
| 生涯页面历史连胜 | 高 | 多赛季连胜统计 | <200ms | 统计表查询 |
| 连胜排行榜 | 中 | 当前连胜排序 | <500ms | 缓存+统计表 |
| 历史连胜对比分析 | 低 | 详细历史数据 | <1s | 统计表聚合 |
| 连胜趋势分析 | 低 | 连胜周期记录 | <2s | 记录表分析 |

#### 2.1.4 最终推荐方案：混合存储策略

基于业务需求分析，采用**混合存储策略**：

```sql
-- 1. 主表冗余存储（高频查询优化）
ALTER TABLE sd_player 
ADD COLUMN current_streak INT DEFAULT 0 COMMENT '当前连胜(正数连胜,负数连败)',
ADD COLUMN current_season_max_win_streak INT DEFAULT 0 COMMENT '本赛季最大连胜',
ADD COLUMN career_max_win_streak INT DEFAULT 0 COMMENT '生涯最大连胜';

-- 2. 统计表完整存储（历史数据分析）
-- stat_type = 5 专用于连胜统计
ALTER TABLE sd_player_stats 
ADD COLUMN current_streak INT DEFAULT 0 COMMENT '当前连胜数',
ADD COLUMN max_win_streak INT DEFAULT 0 COMMENT '最大连胜数',
ADD COLUMN max_lose_streak INT DEFAULT 0 COMMENT '最大连败数',
ADD COLUMN streak_start_date DATE COMMENT '当前连胜开始日期',
ADD COLUMN total_wins INT DEFAULT 0 COMMENT '总胜场数',
ADD COLUMN total_losses INT DEFAULT 0 COMMENT '总负场数',
ADD COLUMN win_rate DECIMAL(5,2) DEFAULT 0 COMMENT '胜率';

-- 3. 索引优化
ALTER TABLE sd_player_stats 
ADD INDEX idx_current_streak (current_streak DESC),
ADD INDEX idx_max_win_streak (max_win_streak DESC),
ADD INDEX idx_player_season_streak (player_id, season, stat_type, game_type);
```

### 2.2 数据一致性保证机制

#### 2.2.1 更新策略

```java
@Service
@Transactional
public class PlayerStreakUpdateService {
    
    /**
     * 原子性更新连胜数据
     */
    public void updatePlayerStreakData(Long playerId, GameResultDTO gameResult) {
        // 1. 更新统计表中的详细数据
        updateStreakStatsTable(playerId, gameResult);
        
        // 2. 同步更新主表中的冗余数据
        syncStreakDataToMainTable(playerId, gameResult.getSeason());
        
        // 3. 清除相关缓存
        clearStreakCache(playerId);
        
        // 4. 发布事件通知其他模块
        publishStreakUpdateEvent(playerId, gameResult);
    }
}
```

#### 2.2.2 数据校验机制

```java
@Component
public class StreakDataValidator {
    
    /**
     * 定期校验数据一致性
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void validateStreakDataConsistency() {
        List<Long> playerIds = playerService.getAllPlayerIds();
        
        for (Long playerId : playerIds) {
            validatePlayerStreakData(playerId);
        }
    }
    
    private void validatePlayerStreakData(Long playerId) {
        // 对比主表和统计表的数据一致性
        PlayerDO player = playerMapper.selectById(playerId);
        PlayerStatsDO currentSeasonStats = playerStatsMapper.selectCurrentSeasonStreakStats(playerId);
        
        if (!Objects.equals(player.getCurrentStreak(), currentSeasonStats.getCurrentStreak())) {
            log.warn("球员连胜数据不一致: playerId={}, 主表={}, 统计表={}", 
                playerId, player.getCurrentStreak(), currentSeasonStats.getCurrentStreak());
            
            // 自动修复：以统计表数据为准
            repairStreakData(playerId);
        }
    }
}
```

### 2.3 平均数据存储策略对比

| 方案 | 描述 | 优势 | 劣势 | 适用场景 |
|------|------|------|------|----------|
| **方案A：预计算存储** | 平均数据预计算并存储 | 查询极快，响应<50ms | 存储冗余，数据一致性风险 | 高频查询场景 |
| **方案B：实时计算** | 只存总计数据，查询时计算 | 数据一致性好，存储节省 | 查询慢，复杂聚合计算 | 低频查询场景 |
| **方案C：混合策略** | 热点数据预计算，冷数据实时算 | 平衡性能和存储 | 逻辑复杂，缓存策略复杂 | 中等频率场景 |

**🎯 推荐方案C（混合策略）**：
- **当前赛季数据**：预计算存储（查询频繁）
- **历史赛季数据**：实时计算（查询较少）
- **排行榜数据**：预计算存储（性能要求高）

### 2.4 排行榜场景优化

**排行榜特点**：读多写少，维度众多，允许短时延迟

**🎯 最终方案：Spring Boot缓存注解 + Redis**
- 使用`@Cacheable`缓存排行榜查询结果
- 数据库实时查询 + 缓存层加速
- 简单、标准、易维护

### 2.5 优化后的设计思路

**统计类型定义**：
- `stat_type = 1`：当前赛季平均数据（预计算）
- `stat_type = 2`：最佳数据记录  
- `stat_type = 3`：总计数据统计
- `stat_type = 4`：能力评分数据
- `stat_type = 5`：连胜统计数据（新增）
- `stat_type = 6`：赛季汇总数据（新增）

**排行榜实现**：使用Spring Boot缓存注解，简化开发和维护

### 2.6 领域模型图

```mermaid
graph TB
    subgraph "球员生涯领域"
        Player[球员]
        Career[生涯档案]
        Ability[能力评估]
        Stats[数据统计]
        Streak[连胜数据]
        Position[位置匹配]
        Poster[数据海报]
    end
    
    subgraph "比赛领域"
        Game[比赛]
        GameStats[比赛统计]
        Team[球队]
    end
    
    subgraph "用户领域"
        User[用户]
        Registration[报名记录]
    end
    
    Player --> Career
    Player --> Ability
    Player --> Stats
    Player --> Streak
    Player --> Position
    Player --> Poster
    
    Game --> GameStats
    Player --> GameStats
    Team --> Player
    
    User --> Player
    User --> Registration
    Registration --> Game
    
    GameStats --> Stats
    GameStats --> Streak
    Stats --> Ability
    Stats --> Position
    GameStats --> Poster
```

### 2.7 数据库ER图

```mermaid
erDiagram
    %% 现有核心表
    member_user {
        bigint id PK
        string username
        string nickname
        string avatar
        datetime create_time
    }
    
    sd_player {
        bigint id PK
        bigint user_id FK
        string real_name
        int jersey_number
        tinyint position
        tinyint gender
        decimal height
        decimal weight
        int birth_year
        decimal real_ability_rating
        decimal display_ability_rating
        int season_games
        int current_streak
        int current_season_max_win_streak
        int career_max_win_streak
        tinyint privacy_level
        datetime career_created_time
    }
    
    sd_team {
        bigint id PK
        string name
        string description
        string logo_url
        datetime create_time
    }
    
    sd_game {
        bigint id PK
        bigint home_team_id FK
        bigint guest_team_id FK
        datetime game_time
        tinyint status
        int home_score
        int guest_score
    }
    
    sd_game_player_stats {
        bigint id PK
        bigint game_id FK
        bigint player_id FK
        decimal minutes_played
        int points
        int rebounds
        int assists
        int steals
        int blocks
        int turnovers
        int fouls
    }
    
    %% 新增生涯模块表
    sd_player_stats {
        bigint id PK
        bigint player_id FK
        tinyint stat_type
        tinyint game_type
        string season
        decimal points
        decimal rebounds
        decimal assists
        decimal efficiency_rating
        decimal scoring_rating
        int current_streak
        int max_win_streak
        int max_lose_streak
        date streak_start_date
        int total_wins
        int total_losses
        decimal win_rate
        decimal pg_fit_percentage
        decimal sg_fit_percentage
        json extended_data_json
        datetime calculation_time
    }
    
    sd_player_poster {
        bigint id PK
        bigint player_id FK
        bigint game_id FK
        json poster_data_json
        json auto_selected_stats
        json custom_stats
        string poster_image_url
        datetime create_time
    }
    
    %% 关系定义
    member_user ||--|| sd_player : "creates"
    sd_player ||--o{ sd_game_player_stats : "plays"
    sd_player ||--o{ sd_player_stats : "has"
    sd_player ||--o{ sd_player_poster : "generates"
    
    sd_team ||--o{ sd_game : "home_team"
    sd_team ||--o{ sd_game : "guest_team"
    sd_game ||--o{ sd_game_player_stats : "contains"
    sd_game ||--o{ sd_player_poster : "generates"
    
         sd_game_player_stats ||--o{ sd_player_stats : "aggregates_to"
```

### 2.8 数据流转关系图
```mermaid
graph TB
    subgraph "球员生涯数据双视图设计"
        A[比赛原始数据] --> B[数据处理引擎]
        
        B --> C[原始数据视图]
        B --> D[标准化数据视图]
        
        C --> C1[全场5v5<br/>平均28分钟<br/>18.5分]
        C --> C2[半场4v4<br/>平均16分钟<br/>12.2分]
        
        D --> D1[全场5v5<br/>标准100分钟<br/>66.1分]
        D --> D2[半场4v4<br/>标准100分钟<br/>76.3分]
    end
    
    subgraph "用户体验"
        E[前端切换器] --> F{用户选择}
        F -->|查看真实表现| G[原始数据展示]
        F -->|跨赛制对比| H[标准化数据展示]
        
        G --> G1[显示真实赛制下的<br/>平均表现数据]
        H --> H1[显示跨赛制对比分析<br/>和优势赛制判断]
    end
    
    subgraph "数据存储"
        I[(sd_player_stats)]
        I --> I1[data_view_type=1<br/>原始数据记录]
        I --> I2[data_view_type=2<br/>标准化数据记录]
    end
    
    C1 -.-> I1
    C2 -.-> I1
    D1 -.-> I2
    D2 -.-> I2
    
    G1 -.-> I1
    H1 -.-> I2
    
    style A fill:#e3f2fd
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style I fill:#ffebee
```
```mermaid
graph TD
    subgraph "数据源层"
        A[比赛结束] --> B[sd_game_player_stats<br/>比赛统计数据]
    end
    
    subgraph "数据聚合层"
        B --> C[数据标准化处理]
        C --> D[sd_player_stats<br/>stat_type=1<br/>平均数据]
        C --> E[sd_player_stats<br/>stat_type=2<br/>最佳数据]
        C --> F[sd_player_stats<br/>stat_type=3<br/>总计数据]
    end
    
    subgraph "算法计算层"
        D --> G[排名计算]
        E --> G
        F --> G
        G --> H[sd_player_stats<br/>stat_type=4<br/>能力评分]
        G --> I[sd_player_stats<br/>stat_type=5<br/>位置匹配度]
    end
    
    subgraph "结果应用层"
        H --> J[更新sd_player<br/>真实能力值]
        H --> K[计算展示能力值变化]
        I --> L[位置推荐]
        D --> M[sd_player_poster<br/>数据海报]
    end
    
    subgraph "前端展示层"
        J --> N[生涯页面展示]
        K --> N
        L --> N
        M --> O[海报分享]
    end
    
    style B fill:#e1f5fe
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#f3e5f5
    style H fill:#e8f5e8
    style I fill:#e8f5e8
    style M fill:#fff3e0
```

### 2.9 核心表结构

基于优化后的设计，生涯模块主要涉及以下核心表：

#### 2.1.1 球员基础信息表 (sd_player)
```sql
-- 现有表结构，新增生涯相关字段（基于PlayerDO实际字段定义）
ALTER TABLE sd_player 
ADD COLUMN `birth_year` INT COMMENT '出生年份',
ADD COLUMN `display_ratings` INT DEFAULT 60 COMMENT '展示能力值',
ADD COLUMN `real_ratings` INT DEFAULT 60 COMMENT '真实能力值',
ADD COLUMN `consecutive_wins` INT DEFAULT 0 COMMENT '连胜场次',
ADD COLUMN `consecutive_losses` INT DEFAULT 0 COMMENT '连败场次',
ADD COLUMN `season_games` INT DEFAULT 0 COMMENT '赛季参赛次数',
ADD COLUMN `privacy_hidden` TINYINT(1) DEFAULT 0 COMMENT '隐私设置：0-公开，1-隐藏',
ADD COLUMN `last_game_time` DATETIME COMMENT '最后比赛时间',
ADD COLUMN `win_rate` DECIMAL(5,2) DEFAULT 0 COMMENT '胜率',
ADD COLUMN `has_professional_avatar` BIT(1) DEFAULT b'0' COMMENT '是否有专业头像',
ADD COLUMN `bmi_warned` BIT(1) DEFAULT b'0' COMMENT '是否已提醒BMI超标',
ADD COLUMN `career_created_time` DATETIME NULL COMMENT '生涯创建时间',
ADD COLUMN `career_updated_time` DATETIME NULL COMMENT '生涯更新时间';

-- 注意：字段命名严格按照PlayerDO中的实际定义和需求文档要求
-- birth_year: 出生年份，用于年龄计算和组别划分
-- display_ratings: 展示能力值，对外显示的能力评分
-- real_ratings: 真实能力值，内部计算使用的准确评分  
-- consecutive_wins: 连胜场次，用于能力值变化计算
-- consecutive_losses: 连败场次，独立字段用于能力值变化计算
-- privacy_hidden: 隐私设置，0-公开展示，1-隐藏基础信息
-- last_game_time: 最后比赛时间，用于活跃度统计
-- win_rate: 胜率，球员总体胜利比例
-- has_professional_avatar: 专业头像标识，管理员设置后显示方形边框
-- bmi_warned: BMI提醒标识，避免重复提醒BMI>30的用户
-- career_created_time: 生涯创建时间，用于事件驱动和数据追踪
-- career_updated_time: 生涯更新时间，用于事件驱动和数据追踪
```

#### 2.1.2 球员生涯统计表 (sd_player_career_stats)
```sql
CREATE TABLE `sd_player_career_stats` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `player_id` BIGINT NOT NULL COMMENT '球员ID',
  `game_type` TINYINT NOT NULL COMMENT '比赛类型：1-排位赛，2-友谊赛，3-联赛',
  `total_games` INT DEFAULT 0 COMMENT '总比赛场次',
  `wins` INT DEFAULT 0 COMMENT '胜场数',
  `losses` INT DEFAULT 0 COMMENT '负场数',
  `win_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '胜率',
  
  -- 基础数据（标准化到对应比赛制式）
  `avg_points` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均得分',
  `avg_rebounds` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均篮板',
  `avg_assists` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均助攻',
  `avg_steals` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均抢断',
  `avg_blocks` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均盖帽',
  `avg_turnovers` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均失误',
  `avg_fouls` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均犯规',
  `avg_playing_time` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均出场时间',
  
  -- 投篮数据
  `fg_attempts_total` INT DEFAULT 0 COMMENT '总投篮出手',
  `fg_makes_total` INT DEFAULT 0 COMMENT '总投篮命中',
  `fg_percentage` DECIMAL(5,2) DEFAULT 0.00 COMMENT '投篮命中率',
  `three_attempts_total` INT DEFAULT 0 COMMENT '总三分出手',
  `three_makes_total` INT DEFAULT 0 COMMENT '总三分命中',
  `three_percentage` DECIMAL(5,2) DEFAULT 0.00 COMMENT '三分命中率',
  `ft_attempts_total` INT DEFAULT 0 COMMENT '总罚球出手',
  `ft_makes_total` INT DEFAULT 0 COMMENT '总罚球命中',
  `ft_percentage` DECIMAL(5,2) DEFAULT 0.00 COMMENT '罚球命中率',
  
  -- 篮板细分
  `avg_offensive_rebounds` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均进攻篮板',
  `avg_defensive_rebounds` DECIMAL(8,2) DEFAULT 0.00 COMMENT '场均防守篮板',
  
  -- 高阶数据
  `efficiency_rating` DECIMAL(8,2) DEFAULT 0.00 COMMENT '效率值',
  `true_shooting_percentage` DECIMAL(5,2) DEFAULT 0.00 COMMENT '真实命中率',
  `player_contribution` DECIMAL(8,2) DEFAULT 0.00 COMMENT '球员贡献度',
  `plus_minus` DECIMAL(8,2) DEFAULT 0.00 COMMENT '正负值',
  
  -- 最佳数据记录
  `best_points` INT DEFAULT 0 COMMENT '单场最高得分',
  `best_rebounds` INT DEFAULT 0 COMMENT '单场最高篮板',
  `best_assists` INT DEFAULT 0 COMMENT '单场最高助攻',
  `best_steals` INT DEFAULT 0 COMMENT '单场最高抢断',
  `best_blocks` INT DEFAULT 0 COMMENT '单场最高盖帽',
  `best_playing_time` INT DEFAULT 0 COMMENT '单场最长出场时间',
  
  -- 最佳数据对应的比赛信息
  `best_points_game_id` BIGINT NULL COMMENT '最高得分对应比赛ID',
  `best_rebounds_game_id` BIGINT NULL COMMENT '最高篮板对应比赛ID',
  `best_assists_game_id` BIGINT NULL COMMENT '最高助攻对应比赛ID',
  
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_player_game_type` (`player_id`, `game_type`),
  KEY `idx_player_id` (`player_id`),
  KEY `idx_game_type` (`game_type`),
  KEY `idx_update_time` (`update_time`)
) COMMENT='球员生涯统计表';
```

#### 2.1.3 球员综合数据表（优化后的统一表）
```sql
CREATE TABLE `sd_player_stats` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `player_id` BIGINT NOT NULL COMMENT '球员ID',
  `stat_type` TINYINT NOT NULL COMMENT '统计类型: 1-当前赛季平均数据, 2-最佳数据, 3-总计数据, 4-能力评分, 5-位置匹配度',
  `game_type` TINYINT NULL COMMENT '比赛类型: 1-排位赛, 2-友谊赛, 3-联赛, 0-全部',
  `game_format` TINYINT NULL COMMENT '比赛赛制: 1-全场5v5, 2-半场4v4, 0-全部赛制',
  `data_view_type` TINYINT DEFAULT 1 COMMENT '数据视图类型: 1-原始数据, 2-标准化数据(100分钟)',
  `season` VARCHAR(20) NULL COMMENT '赛季标识',
  
  -- 🎯 数据完整性控制字段（重要：处理无统计数据比赛）
  `games_played` INT DEFAULT 0 COMMENT '参赛场次（包含所有比赛）',
  `valid_stats_games` INT DEFAULT 0 COMMENT '有效统计场次（仅包含有详细数据的比赛）',
  `no_stats_games` INT DEFAULT 0 COMMENT '无统计数据场次（仅有比分的比赛）',
  `data_completeness_rate` DECIMAL(5,2) DEFAULT 100.00 COMMENT '数据完整性比例(valid_stats_games/games_played*100)',
  
  -- 基础统计数据（基于有效统计场次计算）
  `minutes_played` DECIMAL(8,2) DEFAULT 0 COMMENT '出场时间',
  `points` DECIMAL(8,2) DEFAULT 0 COMMENT '得分',
  `field_goals_made` DECIMAL(8,2) DEFAULT 0 COMMENT '投篮命中',
  `field_goals_attempted` DECIMAL(8,2) DEFAULT 0 COMMENT '投篮出手',
  `field_goal_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '投篮命中率',
  `three_pointers_made` DECIMAL(8,2) DEFAULT 0 COMMENT '三分命中',
  `three_pointers_attempted` DECIMAL(8,2) DEFAULT 0 COMMENT '三分出手',
  `three_point_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '三分命中率',
  `two_pointers_made` DECIMAL(8,2) DEFAULT 0 COMMENT '二分命中',
  `two_pointers_attempted` DECIMAL(8,2) DEFAULT 0 COMMENT '二分出手',
  `two_point_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '二分命中率',
  `free_throws_made` DECIMAL(8,2) DEFAULT 0 COMMENT '罚球命中',
  `free_throws_attempted` DECIMAL(8,2) DEFAULT 0 COMMENT '罚球出手',
  `free_throw_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '罚球命中率',
  `offensive_rebounds` DECIMAL(8,2) DEFAULT 0 COMMENT '进攻篮板',
  `defensive_rebounds` DECIMAL(8,2) DEFAULT 0 COMMENT '防守篮板',
  `total_rebounds` DECIMAL(8,2) DEFAULT 0 COMMENT '总篮板',
  `assists` DECIMAL(8,2) DEFAULT 0 COMMENT '助攻',
  `steals` DECIMAL(8,2) DEFAULT 0 COMMENT '抢断',
  `blocks` DECIMAL(8,2) DEFAULT 0 COMMENT '盖帽',
  `turnovers` DECIMAL(8,2) DEFAULT 0 COMMENT '失误',
  `personal_fouls` DECIMAL(8,2) DEFAULT 0 COMMENT '犯规',
  
  -- 高级统计数据
  `efficiency` DECIMAL(8,2) DEFAULT 0 COMMENT '效率值',
  `true_shooting_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '真实命中率',
  `effective_field_goal_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '有效投篮命中率',
  `player_efficiency_rating` DECIMAL(8,2) DEFAULT 0 COMMENT '球员效率评级',
  `offensive_rating` DECIMAL(8,2) DEFAULT 0 COMMENT '进攻效率',
  `defensive_rating` DECIMAL(8,2) DEFAULT 0 COMMENT '防守效率',
  `net_rating` DECIMAL(8,2) DEFAULT 0 COMMENT '净效率',
  `offensive_rebound_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '进攻篮板率',
  `defensive_rebound_percentage` DECIMAL(5,2) DEFAULT 0 COMMENT '防守篮板率',
  `assist_to_turnover_ratio` DECIMAL(5,2) DEFAULT 0 COMMENT '助攻失误比',
  
  -- 能力评分数据（当stat_type=4时使用）
  `efficiency_rating` DECIMAL(5,2) NULL COMMENT '效率评分',
  `scoring_rating` DECIMAL(5,2) NULL COMMENT '得分评分',
  `rebounding_rating` DECIMAL(5,2) NULL COMMENT '篮板评分',
  `assisting_rating` DECIMAL(5,2) NULL COMMENT '助攻评分',
  `defense_rating` DECIMAL(5,2) NULL COMMENT '防守评分',
  `turnover_rating` DECIMAL(5,2) NULL COMMENT '失误评分',
  `foul_rating` DECIMAL(5,2) NULL COMMENT '犯规评分',
  
  -- 位置匹配度数据（当stat_type=5时使用）
  `pg_fit_percentage` DECIMAL(5,2) NULL COMMENT '控球后卫匹配度',
  `sg_fit_percentage` DECIMAL(5,2) NULL COMMENT '得分后卫匹配度',
  `sf_fit_percentage` DECIMAL(5,2) NULL COMMENT '小前锋匹配度',
  `pf_fit_percentage` DECIMAL(5,2) NULL COMMENT '大前锋匹配度',
  `c_fit_percentage` DECIMAL(5,2) NULL COMMENT '中锋匹配度',
  `recommended_position` TINYINT NULL COMMENT '推荐位置: 1-PG, 2-SG, 3-SF, 4-PF, 5-C',
  
  -- 扩展字段
  `extended_data_json` JSON NULL COMMENT '扩展数据JSON（位置权重配置、计算参数等）',
  `data_pool_size` INT NULL COMMENT '数据池大小（能力评分时使用）',
  `calculation_time` DATETIME NULL COMMENT '计算时间',
  `data_source` VARCHAR(50) NULL COMMENT '数据来源',
  `is_standardized` BIT(1) DEFAULT b'0' COMMENT '是否已标准化',
  `standardization_minutes` INT NULL COMMENT '标准化分钟数',
  `last_updated_game_id` BIGINT NULL COMMENT '最后更新的比赛ID',
  
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_player_stats_unique` (`player_id`, `stat_type`, `game_type`, `game_format`, `data_view_type`, `season`),
  KEY `idx_player_season` (`player_id`, `season`),
  KEY `idx_stat_type` (`stat_type`),
  KEY `idx_update_time` (`update_time`),
  KEY `idx_calculation_time` (`calculation_time`)
) COMMENT='球员综合数据统计表';
```

#### 2.1.4 球员能力值历史记录表
```sql
CREATE TABLE `sd_player_ability_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `player_id` BIGINT NOT NULL COMMENT '球员ID',
  `game_id` BIGINT NULL COMMENT '关联比赛ID（如果是因比赛触发的变化）',
  `old_ability_rating` DECIMAL(5,2) NOT NULL COMMENT '变化前能力值',
  `new_ability_rating` DECIMAL(5,2) NOT NULL COMMENT '变化后能力值',
  `rating_change` DECIMAL(5,2) NOT NULL COMMENT '能力值变化量',
  `change_reason` TINYINT NOT NULL COMMENT '变化原因: 1-比赛胜利, 2-比赛失败, 3-手动调整, 4-系统重置',
  `season` VARCHAR(20) NOT NULL COMMENT '赛季标识',
  `game_type` TINYINT NULL COMMENT '比赛类型: 1-排位赛, 2-友谊赛, 3-联赛',
  `consecutive_wins` INT DEFAULT 0 COMMENT '连胜场次（记录时的状态）',
  `consecutive_losses` INT DEFAULT 0 COMMENT '连败场次（记录时的状态）',
  `season_games_played` INT DEFAULT 0 COMMENT '赛季参赛场次（记录时的状态）',
  `calculation_details` JSON NULL COMMENT '计算详情JSON（包含计算参数、权重等）',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_player_season` (`player_id`, `season`),
  KEY `idx_player_create_time` (`player_id`, `create_time`),
  KEY `idx_game_id` (`game_id`),
  KEY `idx_change_reason` (`change_reason`)
) COMMENT='球员能力值历史记录表';
```

#### 2.1.5 球员数据海报表
```sql
CREATE TABLE `sd_player_poster` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `player_id` BIGINT NOT NULL COMMENT '球员ID',
  `game_id` BIGINT NOT NULL COMMENT '比赛ID',
  `poster_data_json` JSON NOT NULL COMMENT '海报数据JSON',
  `auto_selected_stats` JSON NULL COMMENT '系统自动选择的统计数据',
  `custom_stats` JSON NULL COMMENT '用户自定义的统计数据',
  `is_shared` BIT(1) DEFAULT b'0' COMMENT '是否已分享',
  `share_platform` VARCHAR(50) NULL COMMENT '分享平台',
  `share_time` DATETIME NULL COMMENT '分享时间',
  `poster_image_url` VARCHAR(500) NULL COMMENT '海报图片URL',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_player_game` (`player_id`, `game_id`),
  KEY `idx_player_create_time` (`player_id`, `create_time`),
  KEY `idx_game_id` (`game_id`)
) COMMENT='球员数据海报表';
```



### 2.10 统一表使用说明

#### 2.10.1 `sd_player_stats` 表的不同用途

| stat_type | data_view_type | 用途说明 | 主要使用字段 | 查询示例 |
|-----------|---------------|----------|-------------|----------|
| 1 | 1 | 原始平均数据 | 基础统计字段(真实赛制下的平均值) | `WHERE stat_type=1 AND data_view_type=1 AND game_format=1` |
| 1 | 2 | 标准化平均数据 | 基础统计字段(标准化到100分钟) | `WHERE stat_type=1 AND data_view_type=2 AND game_format=1` |
| 2 | 1 | 最佳数据记录 | 基础统计字段(单场最高值) | `WHERE stat_type=2 AND data_view_type=1` |
| 3 | 1 | 总计数据统计 | 基础统计字段(累计值) | `WHERE stat_type=3 AND data_view_type=1` |
| 4 | 1 | 能力评分数据 | 评分字段(efficiency_rating等) | `WHERE stat_type=4 AND season='2024'` |
| 5 | 1 | 位置匹配度数据 | 匹配度字段(pg_fit_percentage等) | `WHERE stat_type=5 AND player_id=123` |

#### 2.10.2 核心业务查询策略

**🎯 平均数据查询策略（考虑数据完整性）**

1. **当前赛季数据（高频查询）**：
```sql
-- 查询球员当前赛季平均数据（预计算，响应<50ms）
SELECT 
    points, rebounds, assists, field_goal_percentage,
    games_played, valid_stats_games, no_stats_games,
    data_completeness_rate
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 1 AND season = '2024-25';
```

2. **历史赛季数据（中频查询）**：
```sql
-- 查询球员历史赛季平均数据（实时计算，基于有效统计场次，响应<200ms）
SELECT 
    CASE 
        WHEN valid_stats_games > 0 THEN ROUND(points / valid_stats_games, 2)
        ELSE 0 
    END as avg_points,
    CASE 
        WHEN valid_stats_games > 0 THEN ROUND(total_rebounds / valid_stats_games, 2)
        ELSE 0 
    END as avg_rebounds,
    CASE 
        WHEN valid_stats_games > 0 THEN ROUND(assists / valid_stats_games, 2)
        ELSE 0 
    END as avg_assists,
    field_goal_percentage,
    games_played as total_games,
    valid_stats_games,
    no_stats_games,
    CASE 
        WHEN games_played > 0 THEN ROUND(valid_stats_games * 100.0 / games_played, 2)
        ELSE 0 
    END as data_completeness_rate
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 3 AND season = ?;
```

**🏆 排行榜实现**

```java
@Service
public class PlayerRankingService {
    
    @Cacheable(value = "ranking", key = "#type + ':' + #gameType + ':' + #season")
    public List<PlayerRankingVO> getRanking(String type, Integer gameType, String season) {
        // 数据库查询 + 排序，Spring Boot自动缓存结果
        return playerStatsMapper.getRankingList(type, gameType, season);
    }
    
    @CacheEvict(value = "ranking", allEntries = true)
    public void refreshRankingCache() {
        // 比赛结束后清理缓存，下次查询时重新计算
    }
}
```

#### 2.10.3 数据查询模式

```sql
-- 查询球员平均数据（排位赛）
SELECT points, rebounds, assists 
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 1 AND game_type = 1 AND season = ?;

-- 查询球员能力评分
SELECT efficiency_rating, scoring_rating, rebounding_rating 
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 4 AND season = ?;

-- 查询球员位置匹配度
SELECT pg_fit_percentage, sg_fit_percentage, sf_fit_percentage, 
       pf_fit_percentage, c_fit_percentage, recommended_position
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 5 AND season = ?;

-- 查询球员最佳数据
SELECT points, rebounds, assists, steals, blocks
FROM sd_player_stats 
WHERE player_id = ? AND stat_type = 2;
```

#### 2.10.3 数据更新策略

```java
// 服务层方法示例
public class PlayerStatsServiceImpl implements PlayerStatsService {
    
    // 更新平均数据
    public void updateAverageStats(Long playerId, GameTypeEnum gameType, String season) {
        // 查询或创建 stat_type=1 的记录
        PlayerStatsDO stats = getOrCreateStats(playerId, StatTypeEnum.AVERAGE, gameType, season);
        // 计算并更新基础统计字段
        // ...
    }
    
    // 更新能力评分
    public void updateAbilityRating(Long playerId, String season, AbilityRatingDTO ratingData) {
        // 查询或创建 stat_type=4 的记录
        PlayerStatsDO stats = getOrCreateStats(playerId, StatTypeEnum.ABILITY_RATING, null, season);
        // 更新评分字段
        stats.setEfficiencyRating(ratingData.getEfficiencyRating());
        stats.setScoringRating(ratingData.getScoringRating());
        // ...
    }
    
    // 更新位置匹配度
    public void updatePositionFit(Long playerId, String season, PositionFitDTO fitData) {
        // 查询或创建 stat_type=5 的记录
        PlayerStatsDO stats = getOrCreateStats(playerId, StatTypeEnum.POSITION_FIT, null, season);
        // 更新匹配度字段
        stats.setPgFitPercentage(fitData.getPgFit());
        stats.setSgFitPercentage(fitData.getSgFit());
        // ...
    }
}
```

## 2.11 无统计数据比赛处理方案 🚨

### 2.11.1 业务场景分析

在实际篮球比赛中，经常会遇到以下情况：
- ✅ **比赛正常进行**：有完整的比分结果
- ❌ **数据统计缺失**：没有详细的个人数据统计（得分、篮板、助攻等）
- 🤔 **数据质量问题**：是否应该将这些比赛纳入生涯平均数据计算？

### 2.11.2 核心设计原则

**🎯 双层统计策略**：区分"参与比赛"和"有效统计"，确保数据准确性和完整性。

#### 关键字段设计
```sql
-- 在 sd_player_stats 表中新增的关键字段
`games_played` INT DEFAULT 0 COMMENT '参赛场次（包含所有比赛）',
`valid_stats_games` INT DEFAULT 0 COMMENT '有效统计场次（仅包含有详细数据的比赛）',
`no_stats_games` INT DEFAULT 0 COMMENT '无统计数据场次（仅有比分的比赛）',
`data_completeness_rate` DECIMAL(5,2) DEFAULT 100.00 COMMENT '数据完整性比例'
```

#### 计算逻辑
```
总参赛场次 = 有效统计场次 + 无统计数据场次
平均数据 = 统计总和 ÷ 有效统计场次（而非总参赛场次）
数据完整性 = 有效统计场次 ÷ 总参赛场次 × 100%
```

### 2.11.3 数据标识和检测机制

#### 比赛数据完整性检测
```java
@Service
public class GameStatsValidationService {
    
    /**
     * 检测比赛是否有完整的个人统计数据
     */
    public boolean hasCompletePlayerStats(Long gameId) {
        List<PlayerGameStatsDO> stats = playerGameStatsMapper.selectByGameId(gameId);
        
        // 检测标准：至少有以下任一统计数据
        return stats.stream().anyMatch(stat -> 
            stat.getPoints() != null || 
            stat.getRebounds() != null || 
            stat.getAssists() != null ||
            stat.getMinutesPlayed() != null
        );
    }
    
    /**
     * 标识比赛数据类型
     */
    public GameDataTypeEnum identifyGameDataType(Long gameId) {
        boolean hasScore = gameService.hasGameScore(gameId);
        boolean hasPlayerStats = hasCompletePlayerStats(gameId);
        
        if (!hasScore) {
            return GameDataTypeEnum.NO_DATA;           // 无任何数据
        } else if (hasScore && !hasPlayerStats) {
            return GameDataTypeEnum.SCORE_ONLY;        // 仅有比分
        } else {
            return GameDataTypeEnum.COMPLETE_STATS;    // 完整统计
        }
    }
}
```

### 2.11.4 生涯数据更新策略

#### 分类处理逻辑
```java
@Service
public class PlayerCareerStatsService {
    
    /**
     * 比赛结束后更新球员生涯数据
     */
    @Transactional
    public void updatePlayerCareerAfterGame(Long gameId) {
        GameDataTypeEnum dataType = gameStatsValidationService.identifyGameDataType(gameId);
        List<PlayerGameStatsDO> gameStats = playerGameStatsMapper.selectByGameId(gameId);
        
        for (PlayerGameStatsDO gameStat : gameStats) {
            switch (dataType) {
                case COMPLETE_STATS:
                    // 🟢 完整数据：正常更新所有统计
                    updateCompleteStats(gameStat);
                    break;
                    
                case SCORE_ONLY:
                    // 🟡 仅比分：只更新参赛场次，不影响平均数据
                    updateScoreOnlyStats(gameStat);
                    break;
                    
                case NO_DATA:
                    // 🔴 无数据：仅记录参与，不做任何统计更新
                    updateNoDataStats(gameStat);
                    break;
            }
        }
    }
    
    /**
     * 处理完整统计数据的比赛
     */
    private void updateCompleteStats(PlayerGameStatsDO gameStat) {
        PlayerStatsDO totalStats = getOrCreateTotalStats(gameStat.getPlayerId());
        
        // 更新总计数据
        totalStats.setGamesPlayed(totalStats.getGamesPlayed() + 1);
        totalStats.setValidStatsGames(totalStats.getValidStatsGames() + 1);
        totalStats.setPoints(totalStats.getPoints() + gameStat.getPoints());
        totalStats.setRebounds(totalStats.getRebounds() + gameStat.getRebounds());
        // ... 其他统计数据累加
        
        // 重新计算数据完整性比例
        updateDataCompletenessRate(totalStats);
        
        // 触发平均数据重新计算
        updateAverageStats(gameStat.getPlayerId());
    }
    
    /**
     * 处理仅有比分的比赛
     */
    private void updateScoreOnlyStats(PlayerGameStatsDO gameStat) {
        PlayerStatsDO totalStats = getOrCreateTotalStats(gameStat.getPlayerId());
        
        // 只更新场次统计，不更新具体数据
        totalStats.setGamesPlayed(totalStats.getGamesPlayed() + 1);
        totalStats.setNoStatsGames(totalStats.getNoStatsGames() + 1);
        
        // 更新数据完整性比例
        updateDataCompletenessRate(totalStats);
        
        // 🚨 关键：平均数据基于 valid_stats_games 计算，所以不受影响
        log.warn("比赛ID: {} 仅有比分数据，球员ID: {} 平均数据不受影响", 
                 gameStat.getGameId(), gameStat.getPlayerId());
    }
    
    /**
     * 更新数据完整性比例
     */
    private void updateDataCompletenessRate(PlayerStatsDO stats) {
        if (stats.getGamesPlayed() > 0) {
            double rate = (double) stats.getValidStatsGames() / stats.getGamesPlayed() * 100;
            stats.setDataCompletenessRate(BigDecimal.valueOf(rate).setScale(2, RoundingMode.HALF_UP));
        }
    }
}
```

### 2.11.5 前端展示策略

#### 数据完整性透明化
```vue
<!-- 球员生涯数据展示组件 -->
<template>
  <div class="player-career-stats">
    <!-- 基础统计展示 -->
    <div class="stats-grid">
      <StatCard label="场均得分" :value="stats.avgPoints" />
      <StatCard label="场均篮板" :value="stats.avgRebounds" />
      <StatCard label="场均助攻" :value="stats.avgAssists" />
    </div>
    
    <!-- 🎯 数据完整性提示 -->
    <div class="data-quality-info" v-if="stats.dataCompletenessRate < 100">
      <div class="quality-bar">
        <div class="quality-fill" :style="{ width: stats.dataCompletenessRate + '%' }"></div>
      </div>
      <div class="quality-text">
        数据完整性：{{ stats.dataCompletenessRate }}%
        ({{ stats.validStatsGames }}/{{ stats.totalGames }} 场有详细统计)
      </div>
      <div class="quality-explanation">
        * 平均数据基于有详细统计的比赛计算，确保数据准确性
      </div>
    </div>
    
    <!-- 比赛场次明细 -->
    <div class="games-breakdown">
      <div class="games-item">
        <span class="label">总参赛：</span>
        <span class="value">{{ stats.totalGames }}场</span>
      </div>
      <div class="games-item">
        <span class="label">详细统计：</span>
        <span class="value">{{ stats.validStatsGames }}场</span>
      </div>
      <div class="games-item" v-if="stats.noStatsGames > 0">
        <span class="label">仅比分：</span>
        <span class="value">{{ stats.noStatsGames }}场</span>
      </div>
    </div>
  </div>
</template>
```

### 2.11.6 API接口调整

#### 生涯数据查询接口
```java
@RestController
@RequestMapping("/player/career")
public class PlayerCareerController {
    
    /**
     * 获取球员生涯统计数据
     */
    @GetMapping("/{playerId}/stats")
    public CommonResult<PlayerCareerStatsVO> getPlayerCareerStats(
            @PathVariable Long playerId,
            @RequestParam(required = false) String season,
            @RequestParam(required = false) Integer gameType) {
        
        PlayerCareerStatsVO stats = playerCareerService.getCareerStats(playerId, season, gameType);
        
        // 🎯 重要：返回数据包含完整性信息
        return success(stats);
    }
}

// VO对象包含数据完整性字段
@Data
public class PlayerCareerStatsVO {
    // 基础统计数据
    private BigDecimal avgPoints;
    private BigDecimal avgRebounds;
    private BigDecimal avgAssists;
    
    // 🆕 数据完整性信息
    private Integer totalGames;           // 总参赛场次
    private Integer validStatsGames;      // 有效统计场次  
    private Integer noStatsGames;         // 无统计数据场次
    private BigDecimal dataCompletenessRate; // 数据完整性比例
    
    // 数据质量提示
    private String dataQualityTip;        // 如："基于85%的比赛详细统计计算"
}
```

### 2.11.7 数据一致性保障

#### 数据校验规则
```java
/**
 * 数据一致性校验
 */
@Component
public class PlayerStatsConsistencyValidator {
    
    public void validateStatsConsistency(PlayerStatsDO stats) {
        // 规则1：场次数据一致性
        Assert.isTrue(stats.getGamesPlayed() == 
                     stats.getValidStatsGames() + stats.getNoStatsGames(),
                     "场次数据不一致");
        
        // 规则2：数据完整性比例正确性
        if (stats.getGamesPlayed() > 0) {
            double expectedRate = (double) stats.getValidStatsGames() / stats.getGamesPlayed() * 100;
            double actualRate = stats.getDataCompletenessRate().doubleValue();
            Assert.isTrue(Math.abs(expectedRate - actualRate) < 0.01,
                         "数据完整性比例计算错误");
        }
        
        // 规则3：平均数据合理性
        if (stats.getValidStatsGames() > 0) {
            // 确保平均数据是基于有效场次计算的
            double expectedAvgPoints = stats.getPoints().doubleValue() / stats.getValidStatsGames();
            // 进行合理性校验...
        }
    }
}
```

### 2.11.8 业务规则总结

| 比赛数据状态 | 参赛场次 | 有效统计场次 | 平均数据影响 | 说明 |
|-------------|----------|-------------|-------------|------|
| 🟢 完整统计 | +1 | +1 | ✅ 正常计算 | 理想情况，所有数据可用 |
| 🟡 仅有比分 | +1 | +0 | ❌ 不参与计算 | 保护数据准确性 |
| 🔴 无任何数据 | +1 | +0 | ❌ 不参与计算 | 异常情况记录 |

**🎯 核心原则**：
1. **参赛场次记录完整性**：所有比赛都计入总参赛场次
2. **平均数据计算准确性**：仅基于有完整统计的比赛计算平均值
3. **数据透明度**：前端清晰展示数据完整性信息
4. **用户理解友好**：提供数据质量解释和提示

### 2.10 数据存储策略总结

基于您提出的关键问题，我们采用了以下混合策略：

#### 2.10.1 平均数据存储决策

| 数据类型 | 存储策略 | 理由 | 性能表现 |
|----------|----------|------|----------|
| **当前赛季平均数据** | 预计算存储 | 查询频繁，用户体验要求高 | <50ms |
| **历史赛季平均数据** | 实时计算 | 查询较少，数据一致性重要 | <200ms |
| **排行榜数据** | Spring Boot缓存 | 简单标准，易维护 | <100ms |
| **最佳/总计数据** | 直接存储 | 作为计算基础，避免重复聚合 | <100ms |

#### 2.10.2 排行榜场景优化

**🎯 最终方案：Spring Boot缓存注解**

```java
@Service
public class PlayerStatsServiceImpl {
    
    @Cacheable(value = "player:stats:average", key = "#playerId + ':' + #gameType + ':' + #season")
    public PlayerStatsVO getPlayerAverageStats(Long playerId, GameTypeEnum gameType, String season) {
        // 从数据库查询并返回
    }
    
    @Cacheable(value = "ranking", key = "#rankingType + ':' + #gameType + ':' + #season", 
               unless = "#result.isEmpty()")
    public List<PlayerRankingVO> getRanking(String rankingType, GameTypeEnum gameType, String season) {
        // 从数据库查询、排序并返回
    }
    
    @CacheEvict(value = {"player:stats:average", "ranking"}, allEntries = true)
    public void refreshCacheAfterGame(Long gameId) {
        // 比赛结束后清理相关缓存
    }
}
```

**核心优势**：
1. **简化设计**：无需专门设计Redis表结构，利用Spring Boot缓存抽象
2. **标准化**：使用Spring Boot标准缓存注解，代码更规范
3. **灵活配置**：支持多种缓存实现（Redis、Caffeine等）
4. **自动管理**：缓存的更新和失效由框架自动处理
2. **数据库查询优化**：合理索引，减少查询时间
3. **缓存策略**：比赛结束后清理缓存，保证数据准确性

**性能保证**：
- 实时榜单查询：<100ms
- 历史榜单查询：<500ms  
- 多榜单聚合查询：<300ms

#### 2.10.3 数据一致性保证

**更新策略**：
```java
// 比赛结束后的数据更新流程
@Transactional
public void updatePlayerStatsAfterGame(Long gameId) {
    // 1. 更新总计数据（stat_type=3）
    updateTotalStats(gameId);
    
    // 2. 重新计算当前赛季平均数据（stat_type=1）
    recalculateAverageStats(gameId);
    
    // 3. 更新最佳数据（stat_type=2）
    updateBestStats(gameId);
    
    // 4. 重新计算能力评分（stat_type=4）
    recalculateAbilityRating(gameId);
    
    // 5. 异步更新排行榜
    rankingUpdateService.updateRankingsAsync(gameId);
}
```



## 3. 系统架构设计

### 3.1 模块结构

参考 `saidian-module-operation` 的包结构设计，重新规划球员数据生涯模块：

#### 3.1.1 模块划分

*   **`saidian-module-operation-api`**: 定义对外接口、DTO、枚举、常量（包含球员生涯相关）
*   **`saidian-module-operation-biz`**: 业务逻辑实现（包含球员生涯相关）

#### 3.1.2 核心包结构 (`saidian-module-operation-biz`)

```
cn.iocoder.yudao.module.operation
├── controller            # 控制器层
│   ├── admin             # 管理后台接口
│   │   ├── ActivityController.java
│   │   ├── ActivityTemplateController.java
│   │   ├── RegistrationController.java
│   │   ├── PlayerCareerController.java      # 新增：球员生涯管理
│   │   ├── PlayerStatsController.java       # 新增：球员数据统计管理
│   │   ├── PlayerAbilityController.java     # 新增：球员能力值管理
│   │   └── PlayerPosterController.java      # 新增：数据海报管理
│   └── app               # App端接口
│       ├── registration  # 报名相关业务接口
│       │   └── AppRegistrationController.java
│       ├── activity      # 活动相关业务接口
│       │   └── AppActivityController.java
│       ├── friendgroup   # 好友组队相关业务接口
│       │   └── AppFriendGroupController.java
│       ├── career        # 新增：生涯相关业务接口
│       │   └── AppPlayerCareerController.java
│       ├── stats         # 新增：数据统计相关业务接口
│       │   └── AppPlayerStatsController.java
│       ├── ability       # 新增：能力值相关业务接口
│       │   └── AppPlayerAbilityController.java
│       ├── poster        # 新增：数据海报相关业务接口
│       │   └── AppPlayerPosterController.java
│       └── notify        # 回调通知接口 (统一管理第三方回调)
│           ├── OperationPayNotifyController.java    # 支付回调
│           └── OperationRefundNotifyController.java # 退款回调
├── convert               # 对象转换器
│   ├── ActivityConvert.java
│   ├── RegistrationConvert.java
│   ├── FriendGroupConvert.java
│   ├── PlayerCareerConvert.java             # 新增：球员生涯转换器
│   ├── PlayerStatsConvert.java              # 新增：球员数据统计转换器
│   ├── PlayerAbilityConvert.java            # 新增：球员能力值转换器
│   └── PlayerPosterConvert.java             # 新增：球员海报转换器
├── dal                   # 数据访问层
│   ├── dataobject        # 数据对象
│   │   ├── ActivityDO.java
│   │   ├── RegistrationDO.java
│   │   ├── FriendGroupDO.java
│   │   ├── RegistrationLogDO.java
│   │   ├── PlayerStatsDO.java               # 新增：统一的球员数据对象
│   │   ├── PlayerPosterDO.java              # 新增：数据海报对象
│   │   ├── PlayerAbilityHistoryDO.java      # 新增：能力值历史记录
│   │   ├── PlayerCareerStatsDO.java         # 新增：生涯统计汇总
│   │   └── PlayerCareerLogDO.java           # 新增：生涯日志记录
│   └── mysql             # MyBatis Mapper接口
│       ├── ActivityMapper.java
│       ├── RegistrationMapper.java
│       ├── FriendGroupMapper.java
│       ├── RegistrationLogMapper.java
│       ├── PlayerStatsMapper.java           # 新增：统一的数据访问接口
│       ├── PlayerPosterMapper.java          # 新增：海报数据访问接口
│       ├── PlayerAbilityHistoryMapper.java  # 新增：能力值历史访问接口
│       └── PlayerCareerLogMapper.java       # 新增：生涯日志访问接口
├── service               # 业务逻辑层
│   ├── activity          # 活动相关服务
│   │   ├── ActivityService.java
│   │   ├── impl
│   │   │   └── ActivityServiceImpl.java
│   │   └── event         # 活动相关事件定义 (按业务模块分散)
│   │       ├── ActivityGroupingSuccessEvent.java
│   │       ├── ActivityGroupingFailedEvent.java
│   │       └── ActivityStatusChangedEvent.java
│   ├── template          # 活动模板服务
│   │   ├── ActivityTemplateService.java
│   │   └── impl
│   │       └── ActivityTemplateServiceImpl.java
│   ├── registration      # 报名相关服务
│   │   ├── RegistrationService.java
│   │   ├── impl
│   │   │   └── RegistrationServiceImpl.java
│   │   └── event         # 报名相关事件定义 (按业务模块分散)
│   │       ├── RegistrationPaidEvent.java
│   │       ├── RegistrationCancelledEvent.java
│   │       └── RegistrationStatusChangedEvent.java
│   ├── friendgroup       # 好友组队相关服务
│   │   ├── FriendGroupService.java
│   │   ├── FriendGroupCodeUtil.java
│   │   ├── impl
│   │   │   └── FriendGroupServiceImpl.java
│   │   └── event         # 好友组队相关事件定义 (按业务模块分散)
│   │       ├── FriendGroupCreatedEvent.java
│   │       ├── FriendGroupExpiredEvent.java
│   │       └── FriendGroupMemberJoinedEvent.java
│   ├── refund            # 退款相关服务
│   │   ├── ActivityRefundService.java
│   │   ├── calculator    # 退款计算器（策略模式+模板方法）
│   │   │   ├── RefundCalculator.java
│   │   │   ├── RefundCalculatorFactory.java
│   │   │   └── impl
│   │   │       ├── FriendlyRefundCalculator.java
│   │   │       ├── RankingRefundCalculator.java
│   │   │       └── LeagueRefundCalculator.java
│   │   └── impl
│   │       └── ActivityRefundServiceImpl.java
│   ├── league            # 联赛相关服务
│   │   ├── LeagueScheduleService.java
│   │   ├── LeagueRankingService.java
│   │   └── impl
│   │       ├── LeagueScheduleServiceImpl.java
│   │       └── LeagueRankingServiceImpl.java
│   ├── teamassignment    # 分队算法相关服务
│   │   ├── TeamAssignmentService.java
│   │   ├── impl
│   │   │   └── TeamAssignmentServiceImpl.java
│   │   ├── algorithm     # 分队算法核心实现
│   │   │   ├── TeamBalanceAlgorithm.java
│   │   │   ├── impl
│   │   │   │   ├── RankingTeamBalanceAlgorithm.java
│   │   │   │   ├── RealtimeTeamAssignmentAlgorithm.java
│   │   │   │   └── FinalTeamBalanceAlgorithm.java
│   │   │   ├── model     # 分队算法数据模型
│   │   │   │   ├── TeamAssignmentContext.java
│   │   │   │   ├── PlayerTeamInfo.java
│   │   │   │   ├── TeamBalanceResult.java
│   │   │   │   ├── FriendGroupConstraint.java
│   │   │   │   └── TeamStatistics.java
│   │   │   └── strategy  # 分队策略
│   │   │       ├── BalanceStrategy.java
│   │   │       ├── impl
│   │   │       │   ├── AbilityBalanceStrategy.java
│   │   │       │   ├── HeightBalanceStrategy.java
│   │   │       │   ├── PositionBalanceStrategy.java
│   │   │       │   └── FriendGroupBalanceStrategy.java
│   │   │       └── factory
│   │   │           └── BalanceStrategyFactory.java
│   │   └── event         # 分队相关事件定义
│   │       ├── TeamAssignmentChangedEvent.java
│   │       ├── PlayerTeamAssignedEvent.java
│   │       └── FriendGroupSplitEvent.java
│   ├── discount          # 优惠计算服务
│   │   ├── DiscountService.java
│   │   ├── impl
│   │   │   └── DiscountServiceImpl.java
│   │   ├── handler       # 处理器
│   │   │   ├── DiscountHandler.java
│   │   │   ├── CouponDiscountHandler.java
│   │   │   └── PointsDiscountHandler.java
│   │   ├── model         # 处理器使用的数据模型
│   │   │   ├── DiscountContext.java
│   │   │   └── DiscountResult.java
│   │   └── dto           # 优惠计算相关的DTO
│   │       ├── RegistrationPricingResult.java
│   │       └── AppliedDiscountDetailVO.java
│   ├── waitlist          # 候补队列服务
│   │   ├── WaitlistService.java
│   │   └── impl
│   │       └── WaitlistServiceImpl.java
│   ├── strategy          # 活动策略模式相关类
│   │   ├── ActivityStrategy.java
│   │   ├── impl
│   │   │   ├── RankingActivityStrategy.java
│   │   │   ├── FriendlyActivityStrategy.java
│   │   │   └── LeagueActivityStrategy.java
│   │   └── factory
│   │       └── ActivityStrategyFactory.java
│   ├── career            # 新增：生涯相关服务
│   │   ├── PlayerCareerService.java
│   │   ├── impl
│   │   │   └── PlayerCareerServiceImpl.java
│   │   └── event         # 生涯相关事件定义 (按业务模块分散)
│   │       ├── PlayerCareerCreatedEvent.java
│   │       ├── PlayerCareerUpdatedEvent.java
│   │       └── PlayerSeasonEndEvent.java
│   ├── ability           # 能力值相关服务
│   │   ├── PlayerAbilityService.java
│   │   ├── impl
│   │   │   └── PlayerAbilityServiceImpl.java
│   │   ├── calculator    # 能力值计算器（策略模式+模板方法）
│   │   │   ├── AbilityCalculator.java       # 能力值计算器接口（内置通用逻辑）
│   │   │   ├── AbilityCalculatorFactory.java # 能力值计算器工厂
│   │   │   └── impl
│   │   │       ├── BasicAbilityCalculator.java     # 基础能力值计算器
│   │   │       ├── AdvancedAbilityCalculator.java  # 高级能力值计算器
│   │   │       └── PositionAbilityCalculator.java  # 位置特定能力值计算器
│   │   └── event         # 能力值相关事件定义 (按业务模块分散)
│   │       ├── PlayerAbilityUpdatedEvent.java
│   │       ├── PlayerRatingChangedEvent.java
│   │       └── AbilityBreakthroughEvent.java
│   ├── stats             # 数据统计相关服务
│   │   ├── PlayerStatsService.java
│   │   ├── impl
│   │   │   └── PlayerStatsServiceImpl.java
│   │   ├── calculator    # 统计计算器（策略模式+模板方法）
│   │   │   ├── StatsCalculator.java         # 统计计算器接口（内置通用逻辑）
│   │   │   ├── StatsCalculatorFactory.java  # 统计计算器工厂
│   │   │   └── impl
│   │   │       ├── GameStatsCalculator.java      # 比赛统计计算器
│   │   │       ├── SeasonStatsCalculator.java    # 赛季统计计算器
│   │   │       └── CareerStatsCalculator.java    # 生涯统计计算器
│   │   └── event         # 数据统计相关事件定义 (按业务模块分散)
│   │       ├── PlayerStatsUpdatedEvent.java
│   │       ├── MilestoneAchievedEvent.java
│   │       └── RecordBrokenEvent.java
│   ├── poster            # 海报相关服务
│   │   ├── PlayerPosterService.java
│   │   ├── impl
│   │   │   └── PlayerPosterServiceImpl.java
│   │   ├── generator     # 海报生成器（策略模式）
│   │   │   ├── PosterGenerator.java         # 海报生成器接口
│   │   │   ├── PosterGeneratorFactory.java  # 海报生成器工厂
│   │   │   └── impl
│   │   │       ├── GameHighlightPosterGenerator.java    # 比赛高光海报生成器
│   │   │       ├── SeasonSummaryPosterGenerator.java    # 赛季总结海报生成器
│   │   │       └── MilestonePosterGenerator.java       # 里程碑海报生成器
│   │   └── event         # 海报相关事件定义 (按业务模块分散)
│   │       ├── PosterGeneratedEvent.java
│   │       └── PosterSharedEvent.java
│   ├── algorithm         # 算法核心服务 (新增)
│   │   ├── RatingAlgorithmService.java      # 评分算法服务接口
│   │   ├── impl
│   │   │   └── RatingAlgorithmServiceImpl.java # 评分算法服务实现
│   │   ├── rating        # 评分算法核心实现
│   │   │   ├── PlayerRatingAlgorithm.java   # 球员评分算法接口
│   │   │   ├── impl
│   │   │   │   ├── EloRatingAlgorithm.java       # ELO评分算法
│   │   │   │   ├── TrueSkillAlgorithm.java       # TrueSkill算法
│   │   │   │   └── WeightedRatingAlgorithm.java  # 加权评分算法
│   │   │   ├── model     # 评分算法数据模型
│   │   │   │   ├── RatingContext.java       # 评分上下文
│   │   │   │   ├── PlayerRatingInfo.java    # 球员评分信息
│   │   │   │   ├── RatingResult.java        # 评分结果
│   │   │   │   └── RatingHistory.java       # 评分历史
│   │   │   └── strategy  # 评分策略
│   │   │       ├── RatingStrategy.java      # 评分策略接口
│   │   │       ├── impl
│   │   │       │   ├── PositionBasedRatingStrategy.java   # 位置基础评分策略
│   │   │       │   ├── PerformanceRatingStrategy.java     # 表现评分策略
│   │   │       │   └── HistoryRatingStrategy.java         # 历史评分策略
│   │   │       └── factory
│   │   │           └── RatingStrategyFactory.java         # 评分策略工厂
│   │   └── event         # 算法相关事件定义
│   │       ├── RatingCalculatedEvent.java       # 评分计算事件
│   │       └── AlgorithmUpdatedEvent.java       # 算法更新事件
│   ├── normalizer        # 数据标准化服务 (新增)
│   │   ├── DataNormalizerService.java       # 数据标准化服务接口
│   │   ├── impl
│   │   │   └── DataNormalizerServiceImpl.java # 数据标准化服务实现
│   │   └── strategy      # 标准化策略
│   │       ├── NormalizationStrategy.java   # 标准化策略接口
│   │       ├── impl
│   │       │   ├── ZScoreNormalizationStrategy.java    # Z-Score标准化策略
│   │       │   ├── MinMaxNormalizationStrategy.java    # Min-Max标准化策略
│   │       │   └── PercentileNormalizationStrategy.java # 百分位标准化策略
│   │       └── factory
│   │           └── NormalizationStrategyFactory.java   # 标准化策略工厂
│   └── strategy          # 业务策略模式相关类
│       ├── CareerStrategy.java          # 生涯策略接口
│       ├── impl
│       │   ├── NewPlayerCareerStrategy.java     # 新球员生涯策略
│       │   ├── VeteranCareerStrategy.java       # 老将生涯策略
│       │   └── ProspectCareerStrategy.java      # 潜力球员生涯策略
│       └── factory
│           └── CareerStrategyFactory.java       # 生涯策略工厂
├── processor             # 处理器相关类
│   ├── AbstractDataProcessor.java       # 抽象数据处理器(模板方法)
│   ├── impl
│   │   ├── GameDataProcessor.java       # 比赛数据处理器
│   │   ├── SeasonDataProcessor.java     # 赛季数据处理器
│   │   └── CareerDataProcessor.java     # 生涯数据处理器
│   └── factory
│       └── DataProcessorFactory.java    # 数据处理器工厂
├── mq                    # 消息队列消费者
│   ├── career            # 生涯相关消费者
│   │   ├── PlayerCareerCreatedConsumer.java
│   │   ├── PlayerCareerUpdatedConsumer.java
│   │   └── PlayerCareerNotificationConsumer.java
│   ├── ability           # 能力值相关消费者
│   │   ├── PlayerAbilityUpdatedConsumer.java
│   │   ├── PlayerRatingChangedConsumer.java
│   │   └── AbilityNotificationConsumer.java
│   ├── stats             # 数据统计相关消费者
│   │   ├── PlayerStatsUpdatedConsumer.java
│   │   ├── MilestoneAchievedConsumer.java
│   │   └── StatsNotificationConsumer.java
│   ├── poster            # 海报相关消费者
│   │   ├── PosterGeneratedConsumer.java
│   │   └── PosterSharedConsumer.java
│   └── notification      # 通用通知消费者
│       ├── CareerSmsNotificationConsumer.java
│       ├── CareerEmailNotificationConsumer.java
│       └── CareerPushNotificationConsumer.java
├── job                   # 定时任务
│   ├── PlayerAbilityCalculationJob.java # 球员能力值计算任务
│   ├── PlayerStatsUpdateJob.java        # 球员数据更新任务
│   ├── SeasonResetJob.java               # 赛季重置任务
│   ├── PlayerRatingRecalculationJob.java # 球员评分重计算任务
│   └── CareerMilestoneCheckJob.java      # 生涯里程碑检查任务
└── common                # 通用工具类
    ├── util              # 工具类
    │   ├── StatisticsUtil.java           # 统计工具类
    │   ├── RatingUtil.java               # 评分工具类
    │   └── NormalizationUtil.java        # 标准化工具类
    └── constants         # 常量类
        ├── CareerConstants.java          # 生涯常量
        ├── AbilityConstants.java         # 能力值常量
        └── StatsConstants.java           # 统计常量
```

#### 3.1.3 核心测试结构 (`saidian-module-operation-biz`)

```
cn.iocoder.yudao.module.operation
├── service               # 服务层测试
│   ├── career            # 生涯相关服务测试
│   │   └── PlayerCareerServiceTest.java
│   ├── ability           # 能力值相关服务测试
│   │   ├── PlayerAbilityServiceTest.java
│   │   └── calculator
│   │       ├── BasicAbilityCalculatorTest.java
│   │       ├── AdvancedAbilityCalculatorTest.java
│   │       └── PositionAbilityCalculatorTest.java
│   ├── stats             # 数据统计相关服务测试
│   │   ├── PlayerStatsServiceTest.java
│   │   └── calculator
│   │       ├── GameStatsCalculatorTest.java
│   │       ├── SeasonStatsCalculatorTest.java
│   │       └── CareerStatsCalculatorTest.java
│   ├── algorithm         # 算法相关服务测试 (新增)
│   │   ├── RatingAlgorithmServiceTest.java
│   │   └── rating
│   │       ├── EloRatingAlgorithmTest.java
│   │       ├── TrueSkillAlgorithmTest.java
│   │       └── WeightedRatingAlgorithmTest.java
│   ├── poster            # 海报相关服务测试
│   │   └── PlayerPosterServiceTest.java
│   │   └── generator
│   │       ├── GameHighlightPosterGeneratorTest.java
│   │       ├── SeasonSummaryPosterGeneratorTest.java
│   │       └── MilestonePosterGeneratorTest.java
│   └── strategy          # 策略模式相关测试
│       └── impl
│           ├── NewPlayerCareerStrategyTest.java
│           ├── VeteranCareerStrategyTest.java
│           └── ProspectCareerStrategyTest.java
├── processor             # 处理器相关测试
│   └── impl
│       ├── GameDataProcessorTest.java
│       ├── SeasonDataProcessorTest.java
│       └── CareerDataProcessorTest.java
└── controller            # 控制器测试
    ├── admin             # 管理后台接口测试
    │   └── PlayerCareerControllerTest.java
    └── app               # App端接口测试
                 └── AppPlayerCareerControllerTest.java
```

### 3.2 重新设计的核心思路

#### 3.2.1 设计模式应用

参考 `saidian-module-operation` 的成功实践，在球员数据生涯模块中全面应用设计模式：

*   **策略模式 (Strategy Pattern)**:
    *   `CareerStrategy`: 处理不同类型球员的生涯管理策略
    *   `AbilityCalculator`: 不同类型的能力值计算策略
    *   `StatsCalculator`: 不同维度的数据统计策略
    *   `PosterGenerator`: 不同类型的海报生成策略
    *   `RatingStrategy`: 不同的评分计算策略
    *   `NormalizationStrategy`: 不同的数据标准化策略

*   **模板方法模式 (Template Method Pattern)**:
    *   `AbstractDataProcessor`: 定义数据处理的标准流程
    *   各种Calculator接口：内置通用计算逻辑，子类实现特定步骤

*   **工厂模式 (Factory Pattern)**:
    *   为每个策略提供对应的工厂类，统一创建和管理策略实例

*   **观察者模式 (Observer Pattern)**:
    *   通过事件驱动架构，实现模块间的解耦
    *   按业务模块分散事件定义，便于维护

#### 3.2.2 与 Operation 模块的架构对应

| Operation 模块 | Career 模块 | 说明 |
|----------------|-------------|------|
| `ActivityStrategy` | `CareerStrategy` | 核心业务策略处理 |
| `RefundCalculator` | `AbilityCalculator/StatsCalculator` | 特定计算逻辑封装 |
| `TeamAssignmentService` | `RatingAlgorithmService` | 复杂算法服务 |
| `DiscountService` | `DataNormalizerService` | 多步骤处理服务 |
| `AbstractRefundProcessor` | `AbstractDataProcessor` | 模板方法处理器 |
| `RegistrationState` | `CareerStrategy` | 状态相关业务逻辑 |

#### 3.2.3 事件驱动架构

参考 Operation 模块的事件驱动设计：

*   **事件按业务模块分散**: 每个 service 子模块下都有自己的 event 包
*   **消费者集中管理**: 所有消费者统一放在 mq 包下，按功能分组
*   **异步解耦**: 核心业务逻辑与副作用处理（通知、统计等）完全解耦

#### 3.2.4 算法模块的独立设计

学习 Operation 模块中 `teamassignment` 的设计，将复杂的算法逻辑独立成专门的服务模块：

*   **`algorithm` 服务**: 专门处理评分算法的核心逻辑
*   **`normalizer` 服务**: 专门处理数据标准化逻辑
*   **多层次设计**: algorithm -> rating -> strategy，逐层细化

#### 3.2.5 测试驱动开发支持

*   **完整的测试结构**: 覆盖服务层、处理器、控制器的完整测试
*   **策略模式测试**: 每个策略实现都有对应的单元测试
*   **算法测试**: 复杂算法有专门的测试覆盖

#### 3.2.6 模块边界清晰化

*   **API 模块**: 只包含接口定义、DTO、枚举等
*   **BIZ 模块**: 包含所有业务逻辑实现
*   **包职责单一**: 每个包都有明确的职责边界
*   **依赖关系清晰**: 避免循环依赖，保持单向依赖

### 3.3 核心类与职责重新设计

#### 3.3.1 策略模式核心类

*   **`CareerStrategy` (接口及其实现)**: **【策略模式】**
    *   封装不同类型球员的生涯管理策略：
        *   `PlayerTypeEnum getPlayerType()`: 返回此策略对应的球员类型枚举
        *   `void initializeCareer(Long playerId, PlayerCreationContext context)`: 初始化球员生涯数据
        *   `void updateCareerProgress(Long playerId, GamePerformanceData gameData)`: 更新生涯进度
        *   `CareerMilestone[] checkMilestones(Long playerId)`: 检查生涯里程碑
        *   `CareerAdvice generateAdvice(Long playerId)`: 生成生涯建议

*   **`AbilityCalculator` (接口及其实现)**: **【策略模式 + 模板方法模式】**
    *   封装特定能力值计算逻辑，职责专注：
        *   `calculateBasicAbility()`: 计算基础能力值
        *   `calculatePositionAbility()`: 计算位置特定能力值
        *   `calculateOverallRating()`: 计算综合评级
    *   **内置通用计算逻辑**：接口提供默认的能力值计算模板方法，避免重复代码
    *   **保持单向依赖**，被 `PlayerAbilityService` 使用，不依赖其他业务服务

*   **`StatsCalculator` (接口及其实现)**: **【策略模式 + 模板方法模式】**
    *   封装不同维度的数据统计计算：
        *   `calculateGameStats()`: 计算单场比赛统计
        *   `calculateSeasonStats()`: 计算赛季统计
        *   `calculateCareerStats()`: 计算生涯统计
        *   `calculateAdvancedMetrics()`: 计算高级数据指标

#### 3.3.2 核心服务类

*   **`PlayerCareerService`**: **基础生涯管理服务**
    *   基础的球员生涯 CRUD 和查询
    *   **生涯策略协调**: 根据球员类型获取 `CareerStrategy`，并将具体操作委托给它们
    *   管理事务边界，发布业务事件

*   **`PlayerAbilityService`**: **能力值管理协调者**
    *   **不直接实现复杂计算逻辑**，而是根据计算类型获取 `AbilityCalculator`，并将具体计算委托给它们
    *   **能力值历史管理**: 维护能力值变化历史，支持能力值回溯
    *   **突破检测**: 检测能力值突破和等级提升
    *   调用外部 API (如球员基础信息)，发布业务事件

*   **`PlayerStatsService`**: **数据统计协调者**
    *   协调不同维度的数据统计计算
    *   **里程碑检测**: 检测统计数据里程碑达成
    *   **记录管理**: 管理各种记录的创建和更新

*   **`RatingAlgorithmService`**: **评分算法核心服务**
    *   专门处理复杂的球员评分算法
    *   支持多种评分算法 (ELO, TrueSkill, 加权评分等)
    *   **算法模型管理**: 维护评分算法的模型和参数

#### 3.3.3 处理器类

*   **`AbstractDataProcessor` (及其子类)**: **【模板方法模式】**
    *   定义标准数据处理流程：验证 -> 计算 -> 更新 -> 通知 -> 记录日志
    *   子类实现 `processData()` 等差异化步骤
    *   `GameDataProcessor`: 处理比赛数据
    *   `SeasonDataProcessor`: 处理赛季数据  
    *   `CareerDataProcessor`: 处理生涯数据

#### 3.3.4 算法核心类

*   **`PlayerRatingAlgorithm` (接口及其实现)**: **【策略模式】**
    *   `EloRatingAlgorithm`: ELO评分算法实现
    *   `TrueSkillAlgorithm`: TrueSkill算法实现  
    *   `WeightedRatingAlgorithm`: 加权评分算法实现

*   **`RatingStrategy` (接口及其实现)**: **【策略模式】**
    *   `PositionBasedRatingStrategy`: 基于位置的评分策略
    *   `PerformanceRatingStrategy`: 基于表现的评分策略
    *   `HistoryRatingStrategy`: 基于历史的评分策略

#### 3.3.5 事件驱动架构类

*   **事件 (`*Event`) 与监听器 (`*Listener`)**: **【观察者/Spring事件】**
    *   `PlayerAbilityService` 或 `CareerStrategy` 在关键业务节点发布事件
    *   `NotificationListener` 监听事件发送通知（站内信、短信等）
    *   `StatisticsListener` 监听事件更新统计数据
    *   **本地事务**: 确保同一模块内涉及多个数据表的操作封装在同一个本地事务中

### 3.4 重新规划的核心优势总结

#### 3.4.1 架构优势对比

| 维度 | 原有设计 | 重新规划后 | 改进说明 |
|------|----------|------------|----------|
| **模块划分** | 单一模块，职责混杂 | API/BIZ 清晰分离 | 接口与实现分离，便于版本管理 |
| **包结构** | 按功能简单分包 | 按业务模块深度分包 | 每个子模块内聚，便于团队协作 |
| **设计模式** | 缺少系统性应用 | 全面应用经典模式 | 代码复用性和扩展性大幅提升 |
| **事件处理** | 集中式事件定义 | 按模块分散+集中消费 | 事件定义内聚，消费者统一管理 |
| **算法组织** | 混合在业务服务中 | 独立算法服务模块 | 算法逻辑独立，便于优化和测试 |
| **测试覆盖** | 基础测试结构 | 完整测试体系 | 每个组件都有对应测试 |

#### 3.4.2 设计模式收益

| 设计模式 | 应用场景 | 核心收益 |
|----------|----------|----------|
| **策略模式** | 生涯管理、能力计算、统计计算、海报生成 | 算法可插拔，支持运行时切换 |
| **模板方法** | 数据处理流程、计算流程 | 标准化处理流程，减少重复代码 |
| **工厂模式** | 策略创建、计算器创建 | 统一对象创建，便于扩展 |
| **观察者模式** | 事件驱动架构 | 模块解耦，副作用处理异步化 |

#### 3.4.3 开发效率提升

| 方面 | 具体提升 |
|------|----------|
| **代码复用** | 通过模板方法和策略模式，减少重复代码 60%+ |
| **扩展便利** | 新增算法或策略只需实现接口，无需修改现有代码 |
| **测试效率** | 每个策略和算法独立测试，测试用例编写简化 |
| **维护成本** | 职责边界清晰，问题定位和修复更快速 |
| **团队协作** | 按模块分工，减少代码冲突 |

#### 3.4.4 性能优势

| 优化点 | 实现方式 | 性能收益 |
|--------|----------|----------|
| **算法独立** | 专门的算法服务，支持缓存和优化 | 计算性能提升 30%+ |
| **异步处理** | 事件驱动的副作用处理 | 响应时间减少 50%+ |
| **模块化加载** | 按需加载策略和算法 | 内存使用优化 20%+ |
| **并发支持** | 无状态设计，支持高并发 | 并发处理能力大幅提升 |

#### 3.4.5 未来扩展性

| 扩展方向 | 支持程度 | 说明 |
|----------|----------|------|
| **新算法支持** | ⭐⭐⭐⭐⭐ | 只需实现算法接口，零修改现有代码 |
| **新球员类型** | ⭐⭐⭐⭐⭐ | 新增策略实现，完全向后兼容 |
| **新统计维度** | ⭐⭐⭐⭐⭐ | 新增计算器实现，支持热插拔 |
| **新海报类型** | ⭐⭐⭐⭐⭐ | 新增生成器实现，模板化支持 |
| **分布式部署** | ⭐⭐⭐⭐ | 事件驱动架构，易于拆分为微服务 |

### 3.2 核心类设计

#### 3.2.1 球员能力值计算器
```java
@Component
public class AbilityRatingCalculator {
    
    /**
     * 计算球员真实能力值
     */
    public PlayerRatingResult calculateRealAbilityRating(
            Long playerId, 
            PlayerPositionEnum position,
            List<GameStatsDTO> gameStatsList) {
        // 1. 数据标准化
        List<GameStatsDTO> normalizedStats = normalizeStats(gameStatsList);
        
        // 2. 计算各维度排名
        PlayerRankingResult rankings = calculatePlayerRankings(playerId, normalizedStats);
        
        // 3. 计算各维度评分
        Map<RatingDimensionEnum, BigDecimal> dimensionRatings = 
            calculateDimensionRatings(rankings);
        
        // 4. 应用位置权重
        BigDecimal realRating = applyPositionWeights(dimensionRatings, position);
        
        // 5. 计算位置匹配度
        Map<PlayerPositionEnum, BigDecimal> positionFits = 
            calculatePositionFits(dimensionRatings);
        
        return PlayerRatingResult.builder()
            .realAbilityRating(realRating)
            .dimensionRatings(dimensionRatings)
            .positionFits(positionFits)
            .build();
    }
    
    /**
     * 计算展示能力值变化
     */
    public BigDecimal calculateDisplayRatingChange(
            BigDecimal realRating,
            BigDecimal currentDisplayRating,
            boolean isWin,
            int seasonGames,
            int streak) {
        
        BigDecimal change;
        if (isWin) {
            // 胜利后能力值增加
            BigDecimal diff = realRating.subtract(currentDisplayRating);
            if (diff.compareTo(BigDecimal.ZERO) < 0) {
                diff = BigDecimal.ZERO;
            }
            
            BigDecimal coefficient = new BigDecimal(seasonGames + streak)
                .divide(new BigDecimal(13), 4, RoundingMode.HALF_UP);
            if (coefficient.compareTo(BigDecimal.ONE) > 0) {
                coefficient = BigDecimal.ONE;
            }
            
            change = diff.multiply(coefficient).add(new BigDecimal("0.1"));
        } else {
            // 失败后能力值减少
            BigDecimal diff = currentDisplayRating.subtract(realRating);
            if (diff.compareTo(BigDecimal.ZERO) < 0) {
                diff = BigDecimal.ZERO;
            }
            
            BigDecimal coefficient = new BigDecimal(seasonGames - Math.abs(streak))
                .divide(new BigDecimal(13), 4, RoundingMode.HALF_UP);
            if (coefficient.compareTo(BigDecimal.ONE) > 0) {
                coefficient = BigDecimal.ONE;
            }
            
            change = diff.multiply(coefficient).add(new BigDecimal("0.1")).negate();
        }
        
        return change;
    }
}
```

#### 3.2.2 数据标准化器
```java
@Component
public class StatsNormalizer {
    
    /**
     * 根据比赛类型标准化数据
     */
    public GameStatsDTO normalizeStats(GameStatsDTO originalStats, GameTypeEnum gameType) {
        int targetMinutes = getTargetMinutes(gameType);
        BigDecimal actualMinutes = originalStats.getMinutesPlayed();
        
        if (actualMinutes.compareTo(BigDecimal.ZERO) == 0) {
            return originalStats;
        }
        
        BigDecimal factor = new BigDecimal(targetMinutes)
            .divide(actualMinutes, 4, RoundingMode.HALF_UP);
        
        return GameStatsDTO.builder()
            .points(originalStats.getPoints().multiply(factor))
            .rebounds(originalStats.getRebounds().multiply(factor))
            .assists(originalStats.getAssists().multiply(factor))
            .steals(originalStats.getSteals().multiply(factor))
            .blocks(originalStats.getBlocks().multiply(factor))
            .turnovers(originalStats.getTurnovers().multiply(factor))
            .fouls(originalStats.getPersonalFouls().multiply(factor))
            // 命中率等比率数据不标准化
            .fieldGoalPercentage(originalStats.getFieldGoalPercentage())
            .threePointPercentage(originalStats.getThreePointPercentage())
            .freeThrowPercentage(originalStats.getFreeThrowPercentage())
            .build();
    }
    
    private int getTargetMinutes(GameTypeEnum gameType) {
        switch (gameType) {
            case RANKING:
            case FRIENDLY:
                return 100; // 标准化至100分钟
            case LEAGUE:
                return 48;  // 标准化至48分钟
            default:
                return 100;
        }
    }
}
```

## 4. API接口设计

### 4.1 生涯管理接口

```java
@RestController
@RequestMapping("/app/career")
public class AppPlayerCareerController {
    
    /**
     * 创建球员生涯
     */
    @PostMapping("/create")
    public CommonResult<PlayerCareerRespVO> createCareer(@RequestBody PlayerCareerCreateReqVO createReqVO) {
        // 实现生涯创建逻辑
    }
    
    /**
     * 获取球员生涯信息
     */
    @GetMapping("/info")
    public CommonResult<PlayerCareerRespVO> getCareerInfo() {
        // 获取当前用户的生涯信息
    }
    
    /**
     * 更新球员基础信息
     */
    @PutMapping("/update")
    public CommonResult<Void> updateCareer(@RequestBody PlayerCareerUpdateReqVO updateReqVO) {
        // 更新生涯信息
    }
    
    /**
     * 设置隐私级别
     */
    @PutMapping("/privacy")
    public CommonResult<Void> setPrivacyLevel(@RequestBody PlayerPrivacyUpdateReqVO privacyReqVO) {
        // 设置隐私级别
    }
}
```

### 4.2 能力值接口

```java
@RestController
@RequestMapping("/app/ability")
public class AppPlayerAbilityController {
    
    /**
     * 获取球员能力值详情
     */
    @GetMapping("/detail")
    public CommonResult<PlayerAbilityDetailRespVO> getAbilityDetail() {
        // 获取能力值详情，包括各维度评分
    }
    
    /**
     * 获取位置匹配度
     */
    @GetMapping("/position-fit")
    public CommonResult<PlayerPositionFitRespVO> getPositionFit() {
        // 获取与各位置的匹配度
    }
    
    /**
     * 获取能力值变化历史
     */
    @GetMapping("/history")
    public CommonResult<List<PlayerAbilityHistoryRespVO>> getAbilityHistory(
            @RequestParam(defaultValue = "30") Integer days) {
        // 获取最近N天的能力值变化
    }
}
```

### 4.3 数据统计接口

```java
@RestController
@RequestMapping("/app/stats")
public class AppPlayerStatsController {
    
    /**
     * 获取球员平均数据
     */
    @GetMapping("/average")
    public CommonResult<PlayerStatsRespVO> getAverageStats(
            @RequestParam(required = false) GameTypeEnum gameType,
            @RequestParam(required = false) String season) {
        // 获取平均数据统计
    }
    
    /**
     * 获取球员最佳数据
     */
    @GetMapping("/best")
    public CommonResult<PlayerStatsRespVO> getBestStats() {
        // 获取历史最佳数据
    }
    
    /**
     * 获取雷达图数据
     */
    @GetMapping("/radar")
    public CommonResult<PlayerRadarDataRespVO> getRadarData(
            @RequestParam(required = false) GameTypeEnum gameType) {
        // 获取雷达图展示数据
    }
    
    /**
     * 获取数据排名
     */
    @GetMapping("/ranking")
    public CommonResult<PlayerRankingRespVO> getPlayerRanking() {
        // 获取球员在各项数据中的排名
    }
}
```

### 4.4 数据海报接口

```java
@RestController
@RequestMapping("/app/poster")
public class AppPlayerPosterController {
    
    /**
     * 生成比赛数据海报
     */
    @PostMapping("/generate")
    public CommonResult<PlayerPosterRespVO> generatePoster(@RequestBody PosterGenerateReqVO generateReqVO) {
        // 生成数据海报
    }
    
    /**
     * 自定义海报数据
     */
    @PutMapping("/customize")
    public CommonResult<PlayerPosterRespVO> customizePoster(@RequestBody PosterCustomizeReqVO customizeReqVO) {
        // 自定义海报显示数据
    }
    
    /**
     * 分享海报
     */
    @PostMapping("/share")
    public CommonResult<Void> sharePoster(@RequestBody PosterShareReqVO shareReqVO) {
        // 记录海报分享
    }
    
    /**
     * 获取历史海报
     */
    @GetMapping("/history")
    public CommonResult<List<PlayerPosterRespVO>> getPosterHistory() {
        // 获取历史生成的海报
    }
}
```

## 5. 核心业务流程

### 5.1 生涯创建流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as App接口
    participant Service as PlayerCareerService
    participant Validator as 数据验证器
    participant DB as 数据库
    participant EventPub as 事件发布器
    participant Cache as 缓存系统

    User->>API: 提交生涯创建信息
    API->>Validator: 验证信息完整性
    
    alt 验证失败
        Validator-->>API: 返回验证错误
        API-->>User: 提示用户修改
    else 验证成功
        Validator-->>Service: 验证通过
        Service->>DB: 创建sd_player记录
        Service->>DB: 初始化sd_player_stats记录
        
        Note over Service: 设置默认能力值60分<br/>创建初始统计数据
        
        Service->>EventPub: 发布PlayerCareerCreatedEvent
        Service->>Cache: 清理相关缓存
        
        Service-->>API: 返回生涯信息
        API-->>User: 显示创建成功
        
        Note over EventPub: 异步处理后续业务<br/>如通知、统计等
    end
```

### 5.2 能力值计算流程

```mermaid
sequenceDiagram
    participant Game as 比赛系统
    participant Job as 定时任务
    participant Calculator as 能力值计算器
    participant Normalizer as 数据标准化器
    participant Algorithm as 评分算法
    participant DB as 数据库
    participant Cache as 缓存系统
    participant EventPub as 事件发布器

    Game->>Game: 比赛结束
    Note over Game: 更新比赛状态为"已结束"
    
    Job->>Job: 定时扫描已结束比赛
    Job->>Calculator: 触发能力值计算
    
    Calculator->>DB: 获取比赛数据
    Calculator->>Normalizer: 执行数据标准化
    
    Note over Normalizer: 按比赛类型和时间<br/>标准化数据
    
    Normalizer-->>Algorithm: 返回标准化数据
    Algorithm->>Algorithm: 计算各维度排名
    Algorithm->>Algorithm: 计算各维度评分
    Algorithm->>Algorithm: 应用位置权重
    
    Algorithm-->>Calculator: 返回最终能力值
    Calculator->>DB: 更新sd_player_stats
    Calculator->>DB: 更新sd_player能力值
    Calculator->>Cache: 清理相关缓存
    
    Calculator->>EventPub: 发布PlayerAbilityUpdatedEvent
    
    Note over EventPub: 异步通知用户<br/>能力值变化
```

### 5.3 数据海报生成流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as App接口
    participant PosterService as 海报服务
    participant StatsService as 数据统计服务
    participant Algorithm as 排名算法
    participant Template as 海报模板
    participant Storage as 文件存储
    participant DB as 数据库

    User->>API: 申请生成比赛海报
    API->>PosterService: 处理海报生成请求
    
    PosterService->>StatsService: 获取比赛数据
    StatsService->>DB: 查询sd_game_player_stats
    StatsService-->>PosterService: 返回比赛统计
    
    PosterService->>Algorithm: 计算数据排名
    Algorithm->>DB: 查询全平台数据
    Algorithm-->>PosterService: 返回排名结果
    
    PosterService->>PosterService: 自动选择最优4项数据
    Note over PosterService: 基于排名和数据亮点<br/>智能选择展示项目
    
    PosterService->>Template: 生成海报模板数据
    Template->>Storage: 生成海报图片
    Storage-->>Template: 返回图片URL
    
    Template-->>PosterService: 返回海报数据
    PosterService->>DB: 保存sd_player_poster记录
    
    PosterService-->>API: 返回海报信息
    API-->>User: 展示生成的海报
    
    User->>API: 分享海报
    API->>PosterService: 记录分享行为
```

## 6. 数据流转机制

### 6.1 比赛数据同步流程

```mermaid
graph TD
    A[比赛结束] --> B[更新比赛状态]
    B --> C[定时任务扫描]
    C --> D[获取比赛数据]
    D --> E[数据标准化处理]
    E --> F[更新统计数据]
    F --> G[重新计算能力值]
    G --> H[清理缓存]
    H --> I[发布更新事件]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#e8f5e8
    style I fill:#fff3e0
```

### 6.2 能力值更新机制

```mermaid
sequenceDiagram
    participant Job as 定时任务
    participant Service as PlayerStatsService
    participant Calculator as 能力值计算器
    participant DB as 数据库
    participant Cache as 缓存系统
    participant Event as 事件系统

    Job->>Service: 触发数据更新
    
    loop 处理每个球员
        Service->>DB: 查询比赛数据
        Service->>Calculator: 执行数据计算
        
        Calculator->>DB: 更新总计数据(stat_type=3)
        Calculator->>DB: 重新计算平均数据(stat_type=1)
        Calculator->>DB: 更新最佳数据(stat_type=2)
        Calculator->>DB: 计算能力评分(stat_type=4)
        Calculator->>DB: 计算位置匹配度(stat_type=5)
        
        Calculator->>DB: 更新sd_player能力值
        Calculator->>Cache: 清理相关缓存
        
        Calculator->>Event: 发布PlayerAbilityUpdatedEvent
    end
    
    Service-->>Job: 批量更新完成
```

### 6.3 数据一致性保障

```mermaid
graph TB
    subgraph "事务管理"
        A[开始事务] --> B[更新统计数据]
        B --> C[计算能力值]
        C --> D[更新球员表]
        D --> E[提交事务]
        E --> F[清理缓存]
    end
    
    subgraph "幂等性设计"
        G[重复计算检查] --> H[版本号对比]
        H --> I[计算时间戳校验]
        I --> J[避免重复更新]
    end
    
    subgraph "缓存策略"
        K[@Cacheable注解] --> L[自动缓存查询结果]
        L --> M[@CacheEvict注解]
        M --> N[数据更新时清理缓存]
    end
    
    style A fill:#e1f5fe
    style E fill:#e8f5e8
    style G fill:#f3e5f5
    style K fill:#fff3e0
```

## 7. 算法核心实现

### 7.1 位置权重配置
不同位置的球员有不同的能力值权重：
- **控球后卫(PG)**：助攻40%，得分30%，效率20%，防守20%
- **得分后卫(SG)**：得分40%，防守30%，效率20%，助攻20%
- **小前锋(SF)**：得分35%，防守25%，篮板20%，效率20%
- **大前锋(PF)**：篮板35%，防守30%，得分20%，效率15%
- **中锋(C)**：篮板40%，防守35%，盖帽25%

### 7.2 评分算法实现
基于排名的百分位评分算法：
1. **效率评分**：基于球员效率值排名计算
2. **得分评分**：综合真实命中率(40%)、得分(30%)、各项命中率(30%)
3. **篮板评分**：综合总篮板、进攻篮板、防守篮板排名
4. **其他维度**：基于相应统计数据的排名进行计算

## 8. 前端集成方案

### 8.1 页面结构
生涯页面包含以下核心模块：
- **基础信息卡片**：显示球员基本信息和能力值
- **数据统计卡片**：显示平均数据，支持按比赛类型筛选
- **雷达图卡片**：可视化展示能力值各维度
- **最佳数据卡片**：展示历史最佳单场数据
- **数据海报**：生成和分享比赛数据海报

### 8.2 关键功能
- **比赛类型筛选**：支持查看不同比赛类型的数据
- **数据可视化**：使用ECharts展示雷达图和统计图表
- **海报生成**：一键生成包含最优数据的分享海报
- **实时更新**：比赛结束后数据自动更新

## 9. 总结

本设计文档提供了完整的球员数据生涯模块架构，核心特点包括：

1. **统一数据表设计**：通过`stat_type`字段区分不同数据类型，简化数据库结构
2. **混合存储策略**：当前赛季数据预计算，历史数据实时计算，平衡性能与一致性
3. **Spring Boot缓存**：使用标准缓存注解，简化排行榜实现
4. **科学算法模型**：7维度能力评分算法，支持位置权重和数据标准化
5. **完整业务流程**：涵盖生涯创建、数据计算、海报生成等核心功能
6. **数据完整性处理**：科学处理无统计数据比赛，确保平均数据准确性和用户理解

# 附录：基于 sd_player_statistics 表的数据指标计算口径

## A1. 可计算指标对比分析

### A1.1 sd_player_statistics 表字段映射

基于您提供的 `sd_player_statistics` 表结构，与生涯模块设计中的指标进行映射分析：

| 生涯模块指标 | sd_player_statistics 字段 | 可计算性 | 计算方式 |
|-------------|---------------------------|----------|----------|
| **基础统计数据** |  |  |  |
| 出场时间 | `playing_time` (秒) | ✅ 完全支持 | 直接使用，需转换为分钟 |
| 得分 | `points` | ✅ 完全支持 | 直接使用 |
| 助攻 | `assists` | ✅ 完全支持 | 直接使用 |
| 抢断 | `steals` | ✅ 完全支持 | 直接使用 |
| 盖帽 | `blocks` | ✅ 完全支持 | 直接使用 |
| 失误 | `turnovers` | ✅ 完全支持 | 直接使用 |
| 犯规 | `fouls` | ✅ 完全支持 | 直接使用 |
| 投篮命中 | `two_point_makes + three_point_makes` | ✅ 完全支持 | 计算得出 |
| 投篮出手 | `two_point_attempts + three_point_attempts` | ✅ 完全支持 | 计算得出 |
| 二分命中 | `two_point_makes` | ✅ 完全支持 | 直接使用 |
| 二分出手 | `two_point_attempts` | ✅ 完全支持 | 直接使用 |
| 三分命中 | `three_point_makes` | ✅ 完全支持 | 直接使用 |
| 三分出手 | `three_point_attempts` | ✅ 完全支持 | 直接使用 |
| 罚球命中 | `free_throw_makes` | ✅ 完全支持 | 直接使用 |
| 罚球出手 | `free_throw_attempts` | ✅ 完全支持 | 直接使用 |
| 进攻篮板 | `offensive_rebounds` | ✅ 完全支持 | 直接使用 |
| 防守篮板 | `defensive_rebounds` | ✅ 完全支持 | 直接使用 |
| 总篮板 | `offensive_rebounds + defensive_rebounds` | ✅ 完全支持 | 计算得出 |
| **高级统计指标** |  |  |  |
| 效率值 | `efficiency` | ✅ 完全支持 | 直接使用 |
| 正负值 | `plus_minus` | ✅ 完全支持 | 直接使用 |
| 真实命中率 | `ts_rate` | ✅ 完全支持 | 直接使用（需验证计算公式） |
| 投篮命中率 | - | ✅ 可计算 | 投篮命中/投篮出手 |
| 二分命中率 | - | ✅ 可计算 | 二分命中/二分出手 |
| 三分命中率 | - | ✅ 可计算 | 三分命中/三分出手 |
| 罚球命中率 | - | ✅ 可计算 | 罚球命中/罚球出手 |
| 有效投篮命中率 | - | ✅ 可计算 | (二分命中 + 1.5×三分命中)/总出手 |
| 助攻失误比 | - | ✅ 可计算 | 助攻/失误 |
| 篮板率 | - | ❌ 无法计算 | 需要球队总篮板数据 |
| 使用率 | - | ❌ 无法计算 | 需要球队总出手、失误等数据 |

### A1.2 数据标准化计算

#### A1.2.1 时间标准化（按分钟统计）

```sql
-- 标准化公式：per 30 minutes
SELECT 
    player_id,
    game_id,
    -- 基础数据标准化（每30分钟）
    ROUND(points * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS points_per_30min,
    ROUND(assists * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS assists_per_30min,
    ROUND((offensive_rebounds + defensive_rebounds) * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS rebounds_per_30min,
    ROUND(steals * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS steals_per_30min,
    ROUND(blocks * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS blocks_per_30min,
    ROUND(turnovers * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS turnovers_per_30min,
    ROUND(fouls * 30.0 * 60 / NULLIF(playing_time, 0), 2) AS fouls_per_30min
FROM sd_player_statistics
WHERE playing_time > 0;
```

### A1.3 高级统计指标计算口径

#### A1.3.1 投篮效率指标

```sql
-- 投篮相关计算
SELECT 
    player_id,
    game_id,
    -- 基础命中率
    CASE 
        WHEN (two_point_attempts + three_point_attempts) > 0 
        THEN ROUND((two_point_makes + three_point_makes) * 100.0 / (two_point_attempts + three_point_attempts), 1)
        ELSE 0 
    END AS field_goal_percentage,
    
    -- 二分命中率
    CASE 
        WHEN two_point_attempts > 0 
        THEN ROUND(two_point_makes * 100.0 / two_point_attempts, 1)
        ELSE 0 
    END AS two_point_percentage,
    
    -- 三分命中率
    CASE 
        WHEN three_point_attempts > 0 
        THEN ROUND(three_point_makes * 100.0 / three_point_attempts, 1)
        ELSE 0 
    END AS three_point_percentage,
    
    -- 罚球命中率
    CASE 
        WHEN free_throw_attempts > 0 
        THEN ROUND(free_throw_makes * 100.0 / free_throw_attempts, 1)
        ELSE 0 
    END AS free_throw_percentage,
    
    -- 有效投篮命中率 eFG%
    CASE 
        WHEN (two_point_attempts + three_point_attempts) > 0 
        THEN ROUND((two_point_makes + 1.5 * three_point_makes) * 100.0 / (two_point_attempts + three_point_attempts), 1)
        ELSE 0 
    END AS effective_field_goal_percentage,
    
    -- 真实命中率验证（与表中ts_rate对比）
    CASE 
        WHEN (2 * (two_point_attempts + three_point_attempts) + 0.44 * free_throw_attempts) > 0
        THEN ROUND((points) * 100.0 / (2 * (two_point_attempts + three_point_attempts) + 0.44 * free_throw_attempts), 1)
        ELSE 0 
    END AS calculated_ts_percentage
    
FROM sd_player_statistics;
```

#### A1.3.2 综合表现指标

```sql
-- 综合表现计算
SELECT 
    player_id,
    game_id,
    -- 助攻失误比
    CASE 
        WHEN turnovers > 0 
        THEN ROUND(assists * 1.0 / turnovers, 2)
        ELSE assists 
    END AS assist_to_turnover_ratio,
    
    -- 抢断盖帽总和
    (steals + blocks) AS defensive_stocks,
    
    -- 简化效率值计算（验证与表中efficiency对比）
    (points + (offensive_rebounds + defensive_rebounds) + assists + steals + blocks - 
     ((two_point_attempts + three_point_attempts) - (two_point_makes + three_point_makes)) - 
     (free_throw_attempts - free_throw_makes) - turnovers) AS calculated_efficiency,
     
    -- 双双统计
    CASE 
        WHEN (
            (points >= 10) +
            ((offensive_rebounds + defensive_rebounds) >= 10) +
            (assists >= 10) +
            (steals >= 10) +
            (blocks >= 10)
        ) >= 2 THEN 1
        ELSE 0
    END AS is_double_double
    
FROM sd_player_statistics;
```

# 附录B：数据完整性处理常量和配置

## B1. 业务常量定义

```java
/**
 * 数据完整性处理相关常量
 */
public class PlayerStatsConstants {
    
    // 数据质量阈值
    public static final int MIN_GAMES_FOR_RATING = 5;                    // 能力评分最少需要的有效统计场次
    public static final double MIN_DATA_COMPLETENESS_THRESHOLD = 60.0;   // 最低数据完整性比例（60%）
    public static final int UPDATE_THRESHOLD_GAMES = 10;                 // 能力更新需要的有效统计场次阈值
    
    // 🆕 数据标准化相关常量
    public static final double STANDARD_DURATION_MINUTES = 100.0;        // 标准化基准时长（分钟）
    public static final int MIN_GAMES_FOR_CROSS_FORMAT_COMPARISON = 3;   // 跨赛制对比最少需要的比赛场次
    
    // 数据检测标准
    public static final String[] REQUIRED_STATS_FIELDS = {               // 检测统计数据完整性的必要字段
        "points", "rebounds", "assists", "minutes_played"
    };
    
    // 用户体验配置
    public static final String DATA_QUALITY_WARNING_TEMPLATE = 
        "数据完整性：%s%% (%d/%d 场有详细统计)";                          // 数据质量提示模板
    public static final String DATA_QUALITY_EXPLANATION = 
        "平均数据基于有详细统计的比赛计算，确保数据准确性";                   // 数据质量说明
    
    // 🆕 数据视图说明模板
    public static final String ORIGINAL_DATA_DESCRIPTION_TEMPLATE = 
        "显示%s真实比赛时长下的平均表现";                              // 原始数据说明模板
    public static final String STANDARDIZED_DATA_DESCRIPTION = 
        "数据已标准化到100分钟，便于跨赛制比较";                       // 标准化数据说明
}

/**
 * 比赛数据类型枚举
 */
public enum GameDataTypeEnum {
    NO_DATA(0, "无任何数据"),
    SCORE_ONLY(1, "仅有比分"), 
    COMPLETE_STATS(2, "完整统计");
    
    private final Integer code;
    private final String description;
}
```

## B2. 核心处理规则总结

### B2.1 数据分类处理规则

| 数据状态 | 检测标准 | 场次计数影响 | 平均数据影响 | 能力评分影响 |
|----------|----------|-------------|-------------|-------------|
| 🟢 **完整统计** | 至少有得分/篮板/助攻/时间之一 | `games_played++`<br/>`valid_stats_games++` | ✅ 正常参与计算 | ✅ 正常参与计算 |
| 🟡 **仅有比分** | 有比分结果，无个人统计 | `games_played++`<br/>`no_stats_games++` | ❌ 不参与计算 | ❌ 不参与计算 |
| 🔴 **无任何数据** | 无比分结果，无个人统计 | `games_played++`<br/>`no_stats_games++` | ❌ 不参与计算 | ❌ 不参与计算 |

### B2.2 计算公式调整

```sql
-- 平均数据计算公式调整
场均得分 = 总得分 ÷ 有效统计场次 (valid_stats_games)
场均篮板 = 总篮板 ÷ 有效统计场次 (valid_stats_games)  
场均助攻 = 总助攻 ÷ 有效统计场次 (valid_stats_games)

-- 数据完整性比例
数据完整性 = 有效统计场次 ÷ 总参赛场次 × 100%

-- 数据质量权重（能力评分时使用）
质量权重 = MIN(数据完整性比例 ÷ 100, 1.0)
```

### B2.3 前端展示策略

#### 数据透明度展示
```javascript
// 前端数据完整性提示逻辑
function getDataQualityTip(stats) {
    const completeness = stats.dataCompletenessRate;
    
    if (completeness >= 90) {
        return ''; // 高质量数据，无需提示
    } else if (completeness >= 70) {
        return `基于${completeness}%的比赛详细统计计算`;
    } else if (completeness >= 50) {
        return `数据完整性较低(${completeness}%)，平均数据仅供参考`;
    } else {
        return `数据样本不足，建议参与更多比赛以获得准确评估`;
    }
}

// 数据质量等级
function getDataQualityLevel(completeness) {
    if (completeness >= 90) return 'excellent';
    if (completeness >= 70) return 'good';
    if (completeness >= 50) return 'fair';
    return 'poor';
}
```

## B3. 用户体验优化建议

### B3.1 数据展示优先级

1. **高优先级展示**：
   - 总参赛场次（包含所有比赛）
   - 有效统计场次（有详细数据的比赛）
   - 基于有效场次的平均数据

2. **中优先级展示**：
   - 数据完整性比例和进度条
   - 数据质量说明文字

3. **低优先级展示**：
   - 无统计数据的比赛场次
   - 技术细节说明

### B3.2 用户引导策略

```vue
<!-- 数据质量引导组件 -->
<template>
  <div class="data-quality-guide" v-if="shouldShowGuide">
    <div class="guide-content">
      <Icon name="info-circle" />
      <div class="guide-text">
        <h4>为什么有些比赛没有详细统计？</h4>
        <p>有些比赛可能因为记录条件限制，只能记录比分结果。我们的平均数据只基于有详细统计的比赛计算，确保数据准确性。</p>
      </div>
    </div>
    <div class="guide-action">
      <Button @click="showFullStats">查看详细说明</Button>
    </div>
  </div>
</template>
```

### B3.3 数据改进建议提示

```javascript
// 根据数据完整性给出改进建议
function getImprovementSuggestion(stats) {
    const { validStatsGames, dataCompletenessRate } = stats;
    
    if (validStatsGames < 5) {
        return "建议参与更多比赛以获得准确的能力评估";
    }
    
    if (dataCompletenessRate < 70) {
        return "建议选择有数据记录的正式比赛，以提高数据完整性";
    }
    
    return null; // 数据质量良好，无需建议
}
```

## B4. 监控和预警机制

### B4.1 数据质量监控

```java
@Component
public class DataQualityMonitor {
    
    /**
     * 监控数据质量趋势
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void monitorDataQuality() {
        // 统计各时段数据完整性
        List<DataQualityTrendDO> trends = calculateDataQualityTrends();
        
        // 检测异常情况
        if (hasQualityDegradation(trends)) {
            // 发送预警通知
            sendDataQualityAlert(trends);
        }
    }
    
    /**
     * 球员数据质量个性化提醒
     */
    public void sendPersonalizedDataTips(Long playerId) {
        PlayerStatsDO stats = getPlayerStats(playerId);
        
        if (stats.getDataCompletenessRate().compareTo(BigDecimal.valueOf(50)) < 0) {
            // 发送个性化数据改进建议
            sendDataImprovementTip(playerId, stats);
        }
    }
}
```

## B5. 总结

通过引入数据完整性处理机制，我们确保了：

1. **数据准确性**：平均数据只基于有效统计计算，避免被稀释
2. **用户理解**：透明化展示数据质量，帮助用户正确理解数据含义
3. **业务连续性**：即使部分比赛缺少统计数据，系统仍能正常运行
4. **质量改进**：通过监控和提示，引导用户参与更多高质量比赛

这种设计既保证了数据的科学性，又兼顾了实际业务场景的复杂性，是一个既实用又可靠的解决方案。

## 2.9 赛制数据双视图设计方案 🎯

### 2.9.1 业务需求分析

用户需要能够查看两种不同维度的生涯数据：
- **原始数据**：真实赛制下的表现（全场30分钟均值、半场15分钟均值）
- **标准化数据**：统一时长标准的数据（统一换算到100分钟的表现）

### 2.9.2 核心设计方案

#### 数据存储策略
为每个球员的每种赛制同时存储两套平均数据：

```sql
-- 示例：球员123在2024赛季的数据记录
-- 原始数据记录
INSERT INTO sd_player_stats (player_id, stat_type, game_format, data_view_type, season, points, minutes_played, games_played, valid_stats_games) 
VALUES (123, 1, 1, 1, '2024', 18.5, 28.3, 15, 15); -- 全场原始数据

INSERT INTO sd_player_stats (player_id, stat_type, game_format, data_view_type, season, points, minutes_played, games_played, valid_stats_games) 
VALUES (123, 1, 2, 1, '2024', 12.2, 16.8, 8, 8);   -- 半场原始数据

-- 标准化数据记录（统一换算到100分钟）
INSERT INTO sd_player_stats (player_id, stat_type, game_format, data_view_type, season, points, minutes_played, games_played, valid_stats_games) 
VALUES (123, 1, 1, 2, '2024', 65.4, 100.0, 15, 15); -- 全场标准化数据

INSERT INTO sd_player_stats (player_id, stat_type, game_format, data_view_type, season, points, minutes_played, games_played, valid_stats_games) 
VALUES (123, 1, 2, 2, '2024', 72.6, 100.0, 8, 8);   -- 半场标准化数据
```

#### 标准化计算公式

```java
/**
 * 数据标准化计算服务
 */
@Service
public class DataStandardizationService {
    
    // 标准化基准时长（分钟）
    private static final double STANDARD_DURATION_MINUTES = 100.0;
    
    /**
     * 将原始数据标准化到100分钟
     */
    public PlayerStatsDO standardizeToStandardDuration(PlayerStatsDO originalStats) {
        PlayerStatsDO standardizedStats = new PlayerStatsDO();
        
        // 复制基础信息
        BeanUtils.copyProperties(originalStats, standardizedStats);
        
        // 设置为标准化视图
        standardizedStats.setDataViewType(DataViewTypeEnum.STANDARDIZED.getCode());
        standardizedStats.setMinutesPlayed(BigDecimal.valueOf(STANDARD_DURATION_MINUTES));
        
        if (originalStats.getValidStatsGames() > 0 && originalStats.getMinutesPlayed().doubleValue() > 0) {
            double avgMinutesPerGame = originalStats.getMinutesPlayed().doubleValue() / originalStats.getValidStatsGames();
            double scaleFactor = STANDARD_DURATION_MINUTES / avgMinutesPerGame;
            
            // 标准化各项数据
            standardizedStats.setPoints(scaleDecimal(originalStats.getPoints(), scaleFactor));
            standardizedStats.setTotalRebounds(scaleDecimal(originalStats.getTotalRebounds(), scaleFactor));
            standardizedStats.setAssists(scaleDecimal(originalStats.getAssists(), scaleFactor));
            standardizedStats.setSteals(scaleDecimal(originalStats.getSteals(), scaleFactor));
            standardizedStats.setBlocks(scaleDecimal(originalStats.getBlocks(), scaleFactor));
            standardizedStats.setTurnovers(scaleDecimal(originalStats.getTurnovers(), scaleFactor));
            standardizedStats.setPersonalFouls(scaleDecimal(originalStats.getPersonalFouls(), scaleFactor));
            // ... 其他统计数据标准化
        }
        
        return standardizedStats;
    }
    
    private BigDecimal scaleDecimal(BigDecimal original, double scaleFactor) {
        if (original == null) return BigDecimal.ZERO;
        return original.multiply(BigDecimal.valueOf(scaleFactor))
                      .setScale(2, RoundingMode.HALF_UP);
    }
}
```

### 2.9.3 API接口设计

#### 生涯数据查询接口增强

```java
@RestController
@RequestMapping("/player/career")
public class PlayerCareerController {
    
    /**
     * 获取球员生涯统计数据（支持视图切换）
     */
    @GetMapping("/{playerId}/stats")
    public CommonResult<PlayerCareerStatsVO> getPlayerCareerStats(
            @PathVariable Long playerId,
            @RequestParam(required = false) String season,
            @RequestParam(required = false) Integer gameType,
            @RequestParam(required = false) Integer gameFormat,
            @RequestParam(defaultValue = "1") Integer dataViewType) {  // 🆕 数据视图类型参数
        
        PlayerCareerStatsQueryDTO queryDTO = PlayerCareerStatsQueryDTO.builder()
                .playerId(playerId)
                .season(season)
                .gameType(gameType)
                .gameFormat(gameFormat)
                .dataViewType(DataViewTypeEnum.fromCode(dataViewType))
                .build();
                
        PlayerCareerStatsVO stats = playerCareerService.getCareerStats(queryDTO);
        return success(stats);
    }
    
    /**
     * 获取跨赛制对比数据
     */
    @GetMapping("/{playerId}/cross-format-comparison")
    public CommonResult<CrossFormatComparisonVO> getCrossFormatComparison(
            @PathVariable Long playerId,
            @RequestParam(required = false) String season,
            @RequestParam(defaultValue = "2") Integer dataViewType) {  // 默认使用标准化数据对比
        
        CrossFormatComparisonVO comparison = playerCareerService.getCrossFormatComparison(
                playerId, season, DataViewTypeEnum.fromCode(dataViewType));
        return success(comparison);
    }
}
```

#### 数据传输对象设计

```java
@Data
@Builder
public class PlayerCareerStatsQueryDTO {
    private Long playerId;
    private String season;
    private Integer gameType;          // 比赛类型（排位赛、友谊赛等）
    private Integer gameFormat;        // 比赛赛制（全场、半场）
    private DataViewTypeEnum dataViewType;  // 数据视图类型
}

@Data
public class PlayerCareerStatsVO {
    // 基础统计数据
    private BigDecimal avgPoints;
    private BigDecimal avgRebounds;
    private BigDecimal avgAssists;
    private BigDecimal avgMinutes;
    
    // 🆕 数据视图信息
    private Integer dataViewType;           // 当前数据视图类型
    private String dataViewTypeName;        // 视图类型名称
    private String dataDescription;         // 数据说明
    
    // 🆕 赛制信息
    private Integer gameFormat;             // 比赛赛制
    private String gameFormatName;          // 赛制名称
    
    // 数据完整性信息
    private Integer totalGames;
    private Integer validStatsGames;
    private BigDecimal dataCompletenessRate;
    
    // 🆕 视图切换支持信息
    private List<DataViewOption> availableViews;  // 可用的数据视图选项
}

@Data
public class DataViewOption {
    private Integer viewType;
    private String viewName;
    private String description;
    private Boolean available;              // 是否有数据可用
}

@Data
public class CrossFormatComparisonVO {
    private Long playerId;
    private String season;
    private Integer dataViewType;
    
    // 不同赛制的数据对比
    private PlayerCareerStatsVO fullCourtStats;    // 全场数据
    private PlayerCareerStatsVO halfCourtStats;    // 半场数据
    
    // 🎯 对比分析
    private ComparisonAnalysisVO analysis;
}

@Data
public class ComparisonAnalysisVO {
    private String preferredFormat;             // 更擅长的赛制
    private BigDecimal performanceDifference;   // 表现差异百分比
    private List<String> strengthFormats;      // 优势赛制列表
    private String analysisText;               // 文字分析
}
```

### 2.9.4 前端交互设计

#### 数据视图切换组件

```vue
<!-- 生涯数据视图切换器 -->
<template>
  <div class="career-data-viewer">
    <!-- 视图切换控制栏 -->
    <div class="view-controls">
      <div class="control-group">
        <label>数据视图:</label>
        <SegmentedControl 
          v-model="currentDataView" 
          :options="dataViewOptions"
          @change="onDataViewChange" />
      </div>
      
      <div class="control-group" v-if="currentDataView === 'original'">
        <label>比赛赛制:</label>
        <SegmentedControl 
          v-model="currentGameFormat" 
          :options="gameFormatOptions"
          @change="onGameFormatChange" />
      </div>
    </div>
    
    <!-- 数据展示区域 -->
    <div class="stats-display">
      <!-- 原始数据视图 -->
      <div v-if="currentDataView === 'original'" class="original-view">
        <div class="view-description">
          <Icon name="info-circle" />
          <span>{{ getOriginalDataDescription() }}</span>
        </div>
        <StatsCard :stats="originalStats" :game-format="currentGameFormat" />
      </div>
      
      <!-- 标准化数据视图 -->
      <div v-else-if="currentDataView === 'standardized'" class="standardized-view">
        <div class="view-description">
          <Icon name="chart-bar" />
          <span>数据已标准化到100分钟，便于跨赛制比较</span>
        </div>
        
        <!-- 跨赛制对比展示 -->
        <div class="cross-format-comparison">
          <StatsComparisonCard 
            :full-court-stats="standardizedStats.fullCourt"
            :half-court-stats="standardizedStats.halfCourt" />
          
          <!-- 对比分析 -->
          <ComparisonAnalysis :analysis="comparisonAnalysis" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { usePlayerCareerApi } from '@/api/player-career'

const props = defineProps({
  playerId: {
    type: Number,
    required: true
  },
  season: {
    type: String,
    default: null
  }
})

// 数据视图选项
const dataViewOptions = ref([
  { label: '原始数据', value: 'original', description: '真实赛制下的表现数据' },
  { label: '标准化数据', value: 'standardized', description: '统一标准下的对比数据' }
])

// 比赛赛制选项
const gameFormatOptions = ref([
  { label: '全场5v5', value: 1, description: '标准全场比赛' },
  { label: '半场4v4', value: 2, description: '半场比赛' }
])

const currentDataView = ref('original')
const currentGameFormat = ref(1)
const originalStats = ref({})
const standardizedStats = ref({})
const comparisonAnalysis = ref({})

// API实例
const playerCareerApi = usePlayerCareerApi()

// 获取原始数据描述
const getOriginalDataDescription = () => {
  const formatName = gameFormatOptions.value.find(opt => opt.value === currentGameFormat.value)?.label
  return `显示${formatName}真实比赛时长下的平均表现`
}

// 数据视图切换处理
const onDataViewChange = async (viewType) => {
  if (viewType === 'original') {
    await loadOriginalData()
  } else if (viewType === 'standardized') {
    await loadStandardizedData()
  }
}

// 赛制切换处理
const onGameFormatChange = async (gameFormat) => {
  if (currentDataView.value === 'original') {
    await loadOriginalData()
  }
}

// 加载原始数据
const loadOriginalData = async () => {
  const response = await playerCareerApi.getCareerStats({
    playerId: props.playerId,
    season: props.season,
    gameFormat: currentGameFormat.value,
    dataViewType: 1  // 原始数据
  })
  originalStats.value = response.data
}

// 加载标准化数据
const loadStandardizedData = async () => {
  const response = await playerCareerApi.getCrossFormatComparison({
    playerId: props.playerId,
    season: props.season,
    dataViewType: 2  // 标准化数据
  })
  standardizedStats.value = response.data
  comparisonAnalysis.value = response.data.analysis
}

// 初始化加载
onMounted(() => {
  loadOriginalData()
})
</script>
```

#### 跨赛制对比展示组件

```vue
<!-- 跨赛制数据对比卡片 -->
<template>
  <div class="stats-comparison-card">
    <div class="comparison-header">
      <h3>跨赛制数据对比</h3>
      <div class="comparison-note">
        基于标准化数据（100分钟）进行对比
      </div>
    </div>
    
    <div class="comparison-content">
      <!-- 对比数据表格 -->
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>统计项目</th>
              <th>全场5v5</th>
              <th>半场4v4</th>
              <th>差异</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="stat in comparisonStats" :key="stat.key">
              <td>{{ stat.label }}</td>
              <td>{{ formatStatValue(stat.fullCourt) }}</td>
              <td>{{ formatStatValue(stat.halfCourt) }}</td>
              <td :class="getDifferenceClass(stat.difference)">
                {{ formatDifference(stat.difference) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 可视化对比图表 -->
      <div class="comparison-chart">
        <ECharts :option="radarChartOption" height="300px" />
      </div>
    </div>
  </div>
</template>
```

### 2.9.5 业务服务层实现

```java
@Service
public class PlayerCareerServiceImpl implements PlayerCareerService {
    
    @Resource
    private PlayerStatsMapper playerStatsMapper;
    
    @Resource
    private DataStandardizationService dataStandardizationService;
    
    /**
     * 获取球员生涯统计数据
     */
    @Override
    public PlayerCareerStatsVO getCareerStats(PlayerCareerStatsQueryDTO queryDTO) {
        // 查询指定视图类型的数据
        PlayerStatsDO stats = playerStatsMapper.selectByConditions(
                queryDTO.getPlayerId(),
                StatTypeEnum.AVERAGE.getCode(),
                queryDTO.getGameType(),
                queryDTO.getGameFormat(),
                queryDTO.getDataViewType().getCode(),
                queryDTO.getSeason()
        );
        
        if (stats == null && queryDTO.getDataViewType() == DataViewTypeEnum.STANDARDIZED) {
            // 如果标准化数据不存在，基于原始数据实时计算
            PlayerStatsDO originalStats = playerStatsMapper.selectByConditions(
                    queryDTO.getPlayerId(),
                    StatTypeEnum.AVERAGE.getCode(),
                    queryDTO.getGameType(),
                    queryDTO.getGameFormat(),
                    DataViewTypeEnum.ORIGINAL.getCode(),
                    queryDTO.getSeason()
            );
            
            if (originalStats != null) {
                stats = dataStandardizationService.standardizeToStandardDuration(originalStats);
            }
        }
        
        if (stats == null) {
            return createEmptyStatsVO(queryDTO);
        }
        
        PlayerCareerStatsVO vo = convertToVO(stats);
        
        // 添加数据视图信息
        vo.setDataViewType(queryDTO.getDataViewType().getCode());
        vo.setDataViewTypeName(queryDTO.getDataViewType().getDescription());
        vo.setDataDescription(getDataDescription(queryDTO));
        
        // 添加可用视图选项
        vo.setAvailableViews(getAvailableDataViews(queryDTO.getPlayerId(), queryDTO.getSeason(), queryDTO.getGameFormat()));
        
        return vo;
    }
    
    /**
     * 获取跨赛制对比数据
     */
    @Override
    public CrossFormatComparisonVO getCrossFormatComparison(Long playerId, String season, DataViewTypeEnum dataViewType) {
        CrossFormatComparisonVO comparison = new CrossFormatComparisonVO();
        comparison.setPlayerId(playerId);
        comparison.setSeason(season);
        comparison.setDataViewType(dataViewType.getCode());
        
        // 获取全场数据
        PlayerCareerStatsQueryDTO fullCourtQuery = PlayerCareerStatsQueryDTO.builder()
                .playerId(playerId)
                .season(season)
                .gameFormat(GameFormatEnum.FULL_COURT.getCode())
                .dataViewType(dataViewType)
                .build();
        comparison.setFullCourtStats(getCareerStats(fullCourtQuery));
        
        // 获取半场数据
        PlayerCareerStatsQueryDTO halfCourtQuery = PlayerCareerStatsQueryDTO.builder()
                .playerId(playerId)
                .season(season)
                .gameFormat(GameFormatEnum.HALF_COURT.getCode())
                .dataViewType(dataViewType)
                .build();
        comparison.setHalfCourtStats(getCareerStats(halfCourtQuery));
        
        // 生成对比分析
        comparison.setAnalysis(generateComparisonAnalysis(comparison.getFullCourtStats(), comparison.getHalfCourtStats()));
        
        return comparison;
    }
    
    /**
     * 生成对比分析
     */
    private ComparisonAnalysisVO generateComparisonAnalysis(PlayerCareerStatsVO fullCourt, PlayerCareerStatsVO halfCourt) {
        ComparisonAnalysisVO analysis = new ComparisonAnalysisVO();
        
        if (fullCourt.getAvgPoints().compareTo(halfCourt.getAvgPoints()) > 0) {
            analysis.setPreferredFormat("全场5v5");
            analysis.setAnalysisText("该球员在全场比赛中表现更佳，显示出较强的持续作战能力");
        } else {
            analysis.setPreferredFormat("半场4v4");
            analysis.setAnalysisText("该球员在半场比赛中效率更高，适合快节奏的比赛风格");
        }
        
        // 计算表现差异
        BigDecimal pointsDiff = fullCourt.getAvgPoints().subtract(halfCourt.getAvgPoints());
        BigDecimal diffPercentage = pointsDiff.divide(halfCourt.getAvgPoints(), 4, RoundingMode.HALF_UP)
                                             .multiply(BigDecimal.valueOf(100));
        analysis.setPerformanceDifference(diffPercentage);
        
        return analysis;
    }
}
```

### 2.9.6 数据更新策略

```java
@Service
public class PlayerStatsUpdateService {
    
    /**
     * 比赛结束后更新球员统计数据
     */
    @Transactional
    public void updatePlayerStatsAfterGame(Long gameId) {
        // 1. 更新原始数据
        updateOriginalStats(gameId);
        
        // 2. 基于原始数据计算并更新标准化数据
        updateStandardizedStats(gameId);
    }
    
    /**
     * 更新标准化数据
     */
    private void updateStandardizedStats(Long gameId) {
        List<PlayerStatsDO> originalStatsList = getOriginalStatsFromGame(gameId);
        
        for (PlayerStatsDO originalStats : originalStatsList) {
            // 计算标准化数据
            PlayerStatsDO standardizedStats = dataStandardizationService.standardizeToStandardDuration(originalStats);
            
            // 保存或更新标准化数据
            PlayerStatsDO existingStandardized = playerStatsMapper.selectByConditions(
                    originalStats.getPlayerId(),
                    StatTypeEnum.AVERAGE.getCode(),
                    originalStats.getGameType(),
                    originalStats.getGameFormat(),
                    DataViewTypeEnum.STANDARDIZED.getCode(),
                    originalStats.getSeason()
            );
            
            if (existingStandardized != null) {
                // 重新计算并更新
                recalculateAndUpdateStandardizedStats(existingStandardized, standardizedStats);
            } else {
                // 新增标准化数据记录
                playerStatsMapper.insert(standardizedStats);
            }
        }
    }
}
```

### 2.9.7 枚举定义

```java
/**
 * 数据视图类型枚举
 */
@Getter
@AllArgsConstructor
public enum DataViewTypeEnum {
    ORIGINAL(1, "原始数据", "真实赛制下的表现数据"),
    STANDARDIZED(2, "标准化数据", "统一标准化到100分钟的对比数据");
    
    private final Integer code;
    private final String name;
    private final String description;
    
    public static DataViewTypeEnum fromCode(Integer code) {
        for (DataViewTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return ORIGINAL; // 默认返回原始数据
    }
}

/**
 * 比赛赛制枚举
 */
@Getter
@AllArgsConstructor
public enum GameFormatEnum {
    FULL_COURT(1, "全场5v5", "标准全场篮球比赛"),
    HALF_COURT(2, "半场4v4", "半场篮球比赛");
    
    private final Integer code;
    private final String name;
    private final String description;
}
```

## 2.10 总结

通过引入赛制数据双视图设计，我们实现了：

1. **灵活的数据展示**：用户可以选择查看原始数据或标准化数据
2. **真实的赛制表现**：保留每种赛制下的真实平均表现
3. **科学的跨赛制对比**：通过标准化数据实现公平对比
4. **完整的用户体验**：提供直观的切换界面和对比分析
5. **高效的数据管理**：同时存储两种视图的数据，确保查询性能

这种设计既满足了用户查看真实表现的需求，又支持跨赛制的科学对比分析。

## 3. 核心算法设计

### 3.1 连胜数据计算算法

#### 3.1.1 连胜更新核心逻辑

基于前面的深度分析，我们采用**混合存储策略**，下面是核心算法实现：

```java
@Service
@Transactional
public class StreakCalculationService {
    
    /**
     * 连胜数据更新核心算法
     */
    public StreakUpdateResult updatePlayerStreak(Long playerId, GameResultDTO gameResult) {
        // 1. 获取当前连胜状态
        PlayerStatsDO currentStats = getCurrentSeasonStreakStats(playerId, gameResult.getSeason());
        
        // 2. 计算新的连胜状态
        StreakCalculationDTO calculation = calculateNewStreak(currentStats, gameResult);
        
        // 3. 更新统计数据
        updateStreakStatistics(playerId, calculation);
        
        // 4. 同步主表冗余数据
        syncMainTableData(playerId, calculation);
        
        return StreakUpdateResult.builder()
            .oldStreak(currentStats.getCurrentStreak())
            .newStreak(calculation.getNewStreak())
            .isNewRecord(calculation.isNewMaxStreak())
            .build();
    }
    
    /**
     * 连胜计算逻辑
     */
    private StreakCalculationDTO calculateNewStreak(PlayerStatsDO currentStats, GameResultDTO gameResult) {
        int currentStreak = currentStats.getCurrentStreak();
        boolean isWin = gameResult.isWin();
        
        StreakCalculationDTO calculation = new StreakCalculationDTO();
        
        if (isWin) {
            // 胜利情况
            if (currentStreak >= 0) {
                // 当前为连胜或平局，继续连胜
                calculation.setNewStreak(currentStreak + 1);
            } else {
                // 当前为连败，重新开始连胜
                calculation.setNewStreak(1);
                calculation.setStreakBroken(true);
            }
            
            // 检查是否创造新的连胜记录
            int newMaxWinStreak = Math.max(currentStats.getMaxWinStreak(), calculation.getNewStreak());
            calculation.setNewMaxWinStreak(newMaxWinStreak);
            calculation.setNewMaxStreak(newMaxWinStreak > currentStats.getMaxWinStreak());
            
        } else {
            // 失败情况
            if (currentStreak <= 0) {
                // 当前为连败或平局，继续连败
                calculation.setNewStreak(currentStreak - 1);
            } else {
                // 当前为连胜，重新开始连败
                calculation.setNewStreak(-1);
                calculation.setStreakBroken(true);
            }
            
            // 检查是否创造新的连败记录
            int newMaxLoseStreak = Math.max(currentStats.getMaxLoseStreak(), Math.abs(calculation.getNewStreak()));
            calculation.setNewMaxLoseStreak(newMaxLoseStreak);
        }
        
        // 更新胜负总数和胜率
        calculation.setTotalWins(currentStats.getTotalWins() + (isWin ? 1 : 0));
        calculation.setTotalLosses(currentStats.getTotalLosses() + (isWin ? 0 : 1));
        calculation.setWinRate(calculateWinRate(calculation.getTotalWins(), calculation.getTotalLosses()));
        
        // 更新连胜开始日期
        if (calculation.isStreakBroken() || currentStats.getStreakStartDate() == null) {
            calculation.setStreakStartDate(gameResult.getGameDate());
        } else {
            calculation.setStreakStartDate(currentStats.getStreakStartDate());
        }
        
        return calculation;
    }
    
    /**
     * 胜率计算
     */
    private BigDecimal calculateWinRate(int wins, int losses) {
        int totalGames = wins + losses;
        if (totalGames == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(wins)
            .divide(BigDecimal.valueOf(totalGames), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));
    }
}
```

#### 3.1.2 连胜数据查询优化

针对不同频率的查询场景，采用分层缓存策略：

```java
@Service
public class StreakQueryService {
    
    /**
     * 获取球员当前连胜状态（高频查询优化）
     */
    @Cacheable(value = "player_current_streak", key = "#playerId")
    public PlayerCurrentStreakVO getCurrentStreak(Long playerId) {
        // 优先从主表查询（性能最优）
        PlayerDO player = playerMapper.selectById(playerId);
        
        return PlayerCurrentStreakVO.builder()
            .currentStreak(player.getCurrentStreak())
            .currentSeasonMaxWinStreak(player.getCurrentSeasonMaxWinStreak())
            .careerMaxWinStreak(player.getCareerMaxWinStreak())
            .build();
    }
    
    /**
     * 获取历史连胜数据（中频查询）
     */
    @Cacheable(value = "player_streak_history", key = "#playerId + '_' + #season")
    public List<PlayerStreakHistoryVO> getStreakHistory(Long playerId, String season) {
        // 从统计表查询历史数据
        return playerStatsMapper.selectStreakHistory(playerId, season);
    }
    
    /**
     * 获取连胜排行榜（低频查询，重缓存）
     */
    @Cacheable(value = "streak_ranking", key = "#gameType + '_' + #season", 
               unless = "#result.size() == 0")
    public List<StreakRankingVO> getStreakRanking(Integer gameType, String season) {
        return playerStatsMapper.selectStreakRanking(gameType, season);
    }
}
```

#### 3.1.3 连胜定义和规则

```java
/**
 * 连胜定义枚举
 */
@Getter
@AllArgsConstructor
public enum StreakDefinitionEnum {
    
    SEASON_BASED(1, "赛季连胜", "连胜不跨赛季，每个赛季独立计算"),
    CAREER_BASED(2, "生涯连胜", "连胜可跨赛季，计算生涯最长连胜"),
    GAME_TYPE_BASED(3, "分类连胜", "按比赛类型分别计算连胜"),
    MIXED_BASED(4, "混合连胜", "综合所有比赛类型的连胜");
    
    private final Integer code;
    private final String name;
    private final String description;
}

/**
 * 连胜业务规则配置
 */
@Configuration
public class StreakBusinessRules {
    
    /**
     * 是否跨赛季计算连胜
     */
    public static final boolean CROSS_SEASON_STREAK = false;
    
    /**
     * 是否区分比赛类型
     */
    public static final boolean SEPARATE_BY_GAME_TYPE = true;
    
    /**
     * 最小有效比赛场次（开始计算连胜的门槛）
     */
    public static final int MIN_GAMES_FOR_STREAK = 3;
    
    /**
     * 连胜记录保留时间（月）
     */
    public static final int STREAK_RECORD_RETENTION_MONTHS = 24;
    
    /**
     * 获取连胜计算规则
     */
    public StreakCalculationRule getCalculationRule(Integer gameType, String season) {
        return StreakCalculationRule.builder()
            .crossSeason(CROSS_SEASON_STREAK)
            .separateByGameType(SEPARATE_BY_GAME_TYPE)
            .minGamesThreshold(MIN_GAMES_FOR_STREAK)
            .gameType(gameType)
            .season(season)
            .build();
    }
}
```

#### 3.1.4 连胜数据迁移策略

```java
@Component
public class StreakDataMigrationService {
    
    /**
     * 历史数据迁移策略
     */
    @Async
    public void migrateHistoricalStreakData() {
        log.info("开始迁移历史连胜数据...");
        
        List<Long> playerIds = playerMapper.selectAllPlayerIds();
        int batchSize = 100;
        
        for (int i = 0; i < playerIds.size(); i += batchSize) {
            List<Long> batch = playerIds.subList(i, Math.min(i + batchSize, playerIds.size()));
            migrateBatchPlayerData(batch);
        }
        
        log.info("历史连胜数据迁移完成，共处理{}个球员", playerIds.size());
    }
    
    /**
     * 单个球员数据迁移
     */
    private void migratePlayerStreakData(Long playerId) {
        // 1. 获取球员所有比赛记录（按时间排序）
        List<GamePlayerStatsDTO> gameStats = gamePlayerStatsMapper
            .selectPlayerAllGamesOrderByTime(playerId);
        
        // 2. 按赛季和比赛类型分组
        Map<String, List<GamePlayerStatsDTO>> seasonGames = gameStats.stream()
            .collect(Collectors.groupingBy(GamePlayerStatsDTO::getSeason));
        
        // 3. 逐赛季计算连胜数据
        for (Map.Entry<String, List<GamePlayerStatsDTO>> entry : seasonGames.entrySet()) {
            String season = entry.getKey();
            List<GamePlayerStatsDTO> games = entry.getValue();
            
            recalculateSeasonStreakData(playerId, season, games);
        }
    }
}
```

### 3.2 连胜数据可视化算法

#### 3.2.1 连胜趋势图生成

```java
@Service
public class StreakVisualizationService {
    
    /**
     * 生成连胜趋势图数据
     */
    public StreakTrendVO generateStreakTrend(Long playerId, String season) {
        // 获取球员本赛季所有比赛记录
        List<GameResultDTO> games = gameMapper.selectPlayerSeasonGames(playerId, season);
        
        List<StreakPointDTO> trendPoints = new ArrayList<>();
        int currentStreak = 0;
        
        for (GameResultDTO game : games) {
            if (game.isWin()) {
                currentStreak = currentStreak >= 0 ? currentStreak + 1 : 1;
            } else {
                currentStreak = currentStreak <= 0 ? currentStreak - 1 : -1;
            }
            
            trendPoints.add(StreakPointDTO.builder()
                .gameDate(game.getGameDate())
                .streakValue(currentStreak)
                .gameType(game.getGameType())
                .isWin(game.isWin())
                .build());
        }
        
        return StreakTrendVO.builder()
            .playerId(playerId)
            .season(season)
            .trendPoints(trendPoints)
            .maxStreak(getMaxPositiveValue(trendPoints))
            .maxLoseStreak(getMaxNegativeValue(trendPoints))
            .currentStreak(getCurrentStreakValue(trendPoints))
            .build();
    }
}
```

### 3.3 能力值计算算法
    
    -- 三双统计
    CASE 
        WHEN (
            (points >= 10) + 
            ((offensive_rebounds + defensive_rebounds) >= 10) + 
            (assists >= 10) + 
            (steals >= 10) + 
            (blocks >= 10)
        ) >= 3 THEN 1 ELSE 0 
    END AS triple_double

FROM sd_player_statistics;
```

### A1.4 平均数据计算（用于 sd_player_stats stat_type=1）

#### A1.4.1 赛季平均数据计算

```sql
-- 计算球员赛季平均数据
INSERT INTO sd_player_stats (
    player_id, stat_type, game_type, season,
    games_played, minutes_played, points, assists, total_rebounds,
    steals, blocks, turnovers, personal_fouls,
    field_goals_made, field_goals_attempted, field_goal_percentage,
    two_pointers_made, two_pointers_attempted, two_point_percentage,
    three_pointers_made, three_pointers_attempted, three_point_percentage,
    free_throws_made, free_throws_attempted, free_throw_percentage,
    offensive_rebounds, defensive_rebounds,
    efficiency, true_shooting_percentage, effective_field_goal_percentage,
    assist_to_turnover_ratio
)
SELECT 
    player_id,
    1 AS stat_type,  -- 平均数据
    1 AS game_type,  -- 可根据比赛类型区分
    '2024' AS season,
    
    -- 基础统计
    COUNT(*) AS games_played,
    ROUND(AVG(playing_time / 60.0), 1) AS minutes_played,
    ROUND(AVG(points), 1) AS points,
    ROUND(AVG(assists), 1) AS assists,
    ROUND(AVG(offensive_rebounds + defensive_rebounds), 1) AS total_rebounds,
    ROUND(AVG(steals), 1) AS steals,
    ROUND(AVG(blocks), 1) AS blocks,
    ROUND(AVG(turnovers), 1) AS turnovers,
    ROUND(AVG(fouls), 1) AS personal_fouls,
    
    -- 投篮统计
    ROUND(AVG(two_point_makes + three_point_makes), 1) AS field_goals_made,
    ROUND(AVG(two_point_attempts + three_point_attempts), 1) AS field_goals_attempted,
    ROUND(
        SUM(two_point_makes + three_point_makes) * 100.0 / 
        NULLIF(SUM(two_point_attempts + three_point_attempts), 0), 1
    ) AS field_goal_percentage,
    
    ROUND(AVG(two_point_makes), 1) AS two_pointers_made,
    ROUND(AVG(two_point_attempts), 1) AS two_pointers_attempted,
    ROUND(
        SUM(two_point_makes) * 100.0 / 
        NULLIF(SUM(two_point_attempts), 0), 1
    ) AS two_point_percentage,
    
    ROUND(AVG(three_point_makes), 1) AS three_pointers_made,
    ROUND(AVG(three_point_attempts), 1) AS three_pointers_attempted,
    ROUND(
        SUM(three_point_makes) * 100.0 / 
        NULLIF(SUM(three_point_attempts), 0), 1
    ) AS three_point_percentage,
    
    ROUND(AVG(free_throw_makes), 1) AS free_throws_made,
    ROUND(AVG(free_throw_attempts), 1) AS free_throws_attempted,
    ROUND(
        SUM(free_throw_makes) * 100.0 / 
        NULLIF(SUM(free_throw_attempts), 0), 1
    ) AS free_throw_percentage,
    
    -- 篮板分类
    ROUND(AVG(offensive_rebounds), 1) AS offensive_rebounds,
    ROUND(AVG(defensive_rebounds), 1) AS defensive_rebounds,
    
    -- 高级指标
    ROUND(AVG(efficiency), 1) AS efficiency,
    ROUND(AVG(CAST(ts_rate AS DECIMAL(5,1))), 1) AS true_shooting_percentage,
    ROUND(
        SUM(two_point_makes + 1.5 * three_point_makes) * 100.0 / 
        NULLIF(SUM(two_point_attempts + three_point_attempts), 0), 1
    ) AS effective_field_goal_percentage,
    
    ROUND(
        SUM(assists) * 1.0 / NULLIF(SUM(turnovers), 0), 2
    ) AS assist_to_turnover_ratio
    
FROM sd_player_statistics 
WHERE playing_time > 0  -- 只统计有出场时间的比赛
GROUP BY player_id;
```

### A1.5 最佳数据计算（用于 sd_player_stats stat_type=2）

```sql
-- 计算球员生涯最佳数据
INSERT INTO sd_player_stats (
    player_id, stat_type, game_type, season,
    minutes_played, points, assists, total_rebounds,
    steals, blocks, efficiency,
    field_goals_made, three_pointers_made, free_throws_made
)
SELECT 
    player_id,
    2 AS stat_type,  -- 最佳数据
    0 AS game_type,  -- 全部类型
    'career' AS season,
    
    -- 各项最佳数据
    ROUND(MAX(playing_time / 60.0), 1) AS minutes_played,
    MAX(points) AS points,
    MAX(assists) AS assists,
    MAX(offensive_rebounds + defensive_rebounds) AS total_rebounds,
    MAX(steals) AS steals,
    MAX(blocks) AS blocks,
    MAX(efficiency) AS efficiency,
    MAX(two_point_makes + three_point_makes) AS field_goals_made,
    MAX(three_point_makes) AS three_pointers_made,
    MAX(free_throw_makes) AS free_throws_made
    
FROM sd_player_statistics 
WHERE playing_time > 0
GROUP BY player_id;
```

### A1.6 7维度能力评分计算口径

#### A1.6.1 评分维度数据准备

```sql
-- 为7维度评分准备标准化数据
WITH player_normalized_stats AS (
    SELECT 
        player_id,
        -- 效率维度
        ROUND(AVG(efficiency * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS efficiency_per_30min,
        
        -- 得分维度
        ROUND(AVG(points * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS scoring_per_30min,
        
        -- 篮板维度
        ROUND(AVG((offensive_rebounds + defensive_rebounds) * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS rebounding_per_30min,
        
        -- 助攻维度
        ROUND(AVG(assists * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS assisting_per_30min,
        
        -- 防守维度（抢断+盖帽）
        ROUND(AVG((steals + blocks) * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS defense_per_30min,
        
        -- 失误维度（越低越好，需要反向计算）
        ROUND(AVG(turnovers * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS turnovers_per_30min,
        
        -- 犯规维度（越低越好，需要反向计算）
        ROUND(AVG(fouls * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS fouls_per_30min,
        
        COUNT(*) AS games_played
    FROM sd_player_statistics 
    WHERE playing_time > 600  -- 至少10分钟出场时间
    GROUP BY player_id
    HAVING COUNT(*) >= 5  -- 至少5场比赛
)
SELECT * FROM player_normalized_stats;
```

#### A1.6.2 位置匹配度计算基础

```sql
-- 位置匹配度计算的基础数据
SELECT 
    player_id,
    -- 控球后卫相关指标
    ROUND(AVG(assists * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS assists_per_30min,
    ROUND(AVG(turnovers * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS turnovers_per_30min,
    ROUND(SUM(assists) * 1.0 / NULLIF(SUM(turnovers), 0), 2) AS assist_turnover_ratio,
    
    -- 得分后卫相关指标
    ROUND(AVG(points * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS scoring_per_30min,
    ROUND(SUM(three_point_makes) * 100.0 / NULLIF(SUM(three_point_attempts), 0), 1) AS three_point_percentage,
    ROUND(AVG(steals * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS steals_per_30min,
    
    -- 前锋相关指标
    ROUND(AVG((offensive_rebounds + defensive_rebounds) * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS rebounds_per_30min,
    ROUND(SUM((two_point_makes + three_point_makes)) * 100.0 / NULLIF(SUM((two_point_attempts + three_point_attempts)), 0), 1) AS field_goal_percentage,
    
    -- 中锋相关指标
    ROUND(AVG(blocks * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS blocks_per_30min,
    ROUND(AVG(offensive_rebounds * 30.0 * 60 / NULLIF(playing_time, 0)), 2) AS offensive_rebounds_per_30min,
    ROUND(SUM(two_point_makes) * 100.0 / NULLIF(SUM(two_point_attempts), 0), 1) AS two_point_percentage
    
FROM sd_player_statistics 
WHERE playing_time > 600
GROUP BY player_id
HAVING COUNT(*) >= 5;
```

## A2. 数据计算流程建议

### A2.1 比赛结束后数据处理流程

```mermaid
sequenceDiagram
    participant Game as 比赛结束
    participant StatsSrc as sd_player_statistics
    participant Calculator as 数据计算器
    participant PlayerStats as sd_player_stats
    participant PlayerTable as sd_player
    
    Game->>StatsSrc: 1. 原始数据入库
    StatsSrc->>Calculator: 2. 触发数据计算
    
    Calculator->>Calculator: 3. 计算平均数据
    Calculator->>PlayerStats: 4. 更新stat_type=1(平均数据)
    
    Calculator->>Calculator: 5. 计算最佳数据
    Calculator->>PlayerStats: 6. 更新stat_type=2(最佳数据)
    
    Calculator->>Calculator: 7. 计算总计数据
    Calculator->>PlayerStats: 8. 更新stat_type=3(总计数据)
    
    Calculator->>Calculator: 9. 计算能力评分
    Calculator->>PlayerStats: 10. 更新stat_type=4(能力评分)
    
    Calculator->>Calculator: 11. 计算位置匹配度
    Calculator->>PlayerStats: 12. 更新stat_type=5(位置匹配)
    
    Calculator->>PlayerTable: 13. 更新球员表能力值
```

### A2.2 Java代码实现建议

```java
@Service
public class PlayerStatsCalculatorService {
    
    /**
     * 基于sd_player_statistics计算并更新所有类型的统计数据
     */
    @Transactional
    public void calculateAndUpdatePlayerStats(Long playerId) {
        // 1. 获取球员原始比赛统计数据
        List<PlayerStatisticsDO> rawStats = playerStatisticsMapper.selectByPlayerId(playerId);
        
        // 2. 计算平均数据 (stat_type=1)
        PlayerStatsDTO averageStats = calculateAverageStats(rawStats);
        savePlayerStats(playerId, averageStats, StatTypeEnum.AVERAGE);
        
        // 3. 计算最佳数据 (stat_type=2)
        PlayerStatsDTO bestStats = calculateBestStats(rawStats);
        savePlayerStats(playerId, bestStats, StatTypeEnum.BEST);
        
        // 4. 计算总计数据 (stat_type=3)
        PlayerStatsDTO totalStats = calculateTotalStats(rawStats);
        savePlayerStats(playerId, totalStats, StatTypeEnum.TOTAL);
        
        // 5. 计算能力评分 (stat_type=4)
        PlayerAbilityRatingDTO abilityRating = calculateAbilityRating(playerId, averageStats);
        savePlayerAbilityRating(playerId, abilityRating);
        
        // 6. 计算位置匹配度 (stat_type=5)
        PositionFitDTO positionFit = calculatePositionFit(playerId, averageStats);
        savePositionFit(playerId, positionFit);
    }
    
    private PlayerStatsDTO calculateAverageStats(List<PlayerStatisticsDO> rawStats) {
        if (rawStats.isEmpty()) return new PlayerStatsDTO();
        
        return PlayerStatsDTO.builder()
            .gamesPlayed(rawStats.size())
            .minutesPlayed(rawStats.stream()
                .mapToDouble(s -> s.getPlayingTime() / 60.0)
                .average().orElse(0.0))
            .points(rawStats.stream()
                .mapToDouble(PlayerStatisticsDO::getPoints)
                .average().orElse(0.0))
            .assists(rawStats.stream()
                .mapToDouble(PlayerStatisticsDO::getAssists)
                .average().orElse(0.0))
            .totalRebounds(rawStats.stream()
                .mapToDouble(s -> s.getOffensiveRebounds() + s.getDefensiveRebounds())
                .average().orElse(0.0))
            // ... 其他统计项计算
            .build();
    }
}
```

## A3. 不足与建议

### A3.1 当前表结构不足
1. **缺少球队数据**：无法计算使用率、篮板率等需要球队总数据的高级指标
2. **缺少对手数据**：无法计算相对效率值
3. **缺少比赛时长**：无法准确计算每分钟统计
4. **缺少比赛结果**：无法计算胜负相关的高级指标

### A3.2 建议补充字段
```sql
-- 建议在sd_player_statistics表中补充
ALTER TABLE sd_player_statistics 
ADD COLUMN `game_duration` INT NULL COMMENT '比赛总时长(秒)',
ADD COLUMN `team_score` INT NULL COMMENT '所在球队得分',
ADD COLUMN `opponent_score` INT NULL COMMENT '对手球队得分',
ADD COLUMN `team_id` BIGINT NULL COMMENT '所在球队ID',
ADD COLUMN `is_starter` BIT(1) DEFAULT b'0' COMMENT '是否首发',
ADD COLUMN `jersey_number` VARCHAR(10) NULL COMMENT '球衣号码';
```

这样设计后，基本上所有生涯模块中定义的指标都可以基于 `sd_player_statistics` 表准确计算出来了。