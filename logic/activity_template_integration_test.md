# 活动模板系统集成测试用例

## 概述

本文档提供活动模板系统的集成测试用例，包含模板创建、管理和基于模板创建活动的完整流程测试。

## 测试环境准备

### 1. 数据库表结构确认

确保 `sd_activity_template` 表已创建，包含以下字段：
- 基本信息：id, name, type, location, pic_url, remark
- 费用配置：total_fee, league_fee_per_player
- 人数配置：min_players_per_game, max_players_per_game, min_teams_friendly, etc.
- 自动创建配置：auto_creation_enabled, auto_creation_schedule, etc.
- 排位赛配置：default_home_team_name, default_guest_team_name, etc.

### 2. 权限配置

确保测试用户具有以下权限：
- `operation:activity-template:create` - 创建活动模板
- `operation:activity-template:update` - 更新活动模板
- `operation:activity-template:delete` - 删除活动模板
- `operation:activity-template:query` - 查询活动模板
- `operation:activity-template:export` - 导出活动模板
- `operation:activity-template:create-activity` - 基于模板创建活动

## 测试数据准备

### 测试模板数据

#### 1. 排位赛模板
```json
{
  "name": "周末排位赛模板",
  "type": 1,
  "location": "篮球场A",
  "picUrl": "https://example.com/ranking-game.jpg",
  "totalFee": 10000,
  "minPlayersPerGame": 16,
  "maxPlayersPerGame": 20,
  "remark": "周末排位赛活动模板，适用于常规排位赛",
  "autoCreationEnabled": true,
  "autoCreationSchedule": "0 0 18 * * FRI",
  "autoCreationOffsetDays": 7,
  "autoCreationDurationHours": 2,
  "autoRegistrationStartDays": 3,
  "autoRegistrationDeadlineHours": 2,
  "defaultHomeTeamName": "主队",
  "defaultGuestTeamName": "客队",
  "defaultHomeTeamColor": "红色",
  "defaultGuestTeamColor": "蓝色"
}
```

#### 2. 友谊赛模板
```json
{
  "name": "友谊赛模板",
  "type": 2,
  "location": "篮球场B",
  "picUrl": "https://example.com/friendly-game.jpg",
  "totalFee": 8000,
  "minTeamsFriendly": 2,
  "minPlayersPerTeamFriendly": 5,
  "maxPlayersPerTeamFriendly": 15,
  "remark": "友谊赛活动模板，适用于团队对抗",
  "autoCreationEnabled": false
}
```

#### 3. 联赛模板
```json
{
  "name": "月度联赛模板",
  "type": 3,
  "location": "篮球场C",
  "picUrl": "https://example.com/league-game.jpg",
  "leagueFeePerPlayer": 5000,
  "minTeamsLeague": 4,
  "minPlayersPerTeamLeague": 8,
  "remark": "月度联赛活动模板，适用于长期联赛",
  "autoCreationEnabled": true,
  "autoCreationSchedule": "0 0 10 1 * *",
  "autoCreationOffsetDays": 14,
  "autoCreationDurationHours": 720,
  "autoRegistrationStartDays": 7,
  "autoRegistrationDeadlineHours": 24
}
```

## 集成测试用例

### 测试用例 1：活动模板 CRUD 操作

#### 1.1 创建活动模板
**测试步骤：**
1. 访问活动模板管理页面：`/operation/activityTemplate`
2. 点击"新增"按钮
3. 填写排位赛模板信息（参考上述测试数据）
4. 点击"确定"提交

**预期结果：**
- 模板创建成功，显示成功提示
- 列表页面显示新创建的模板
- 模板信息完整保存

#### 1.2 查询活动模板
**测试步骤：**
1. 在搜索栏输入模板名称"周末排位赛"
2. 选择活动类型"排位赛"
3. 点击"搜索"按钮

**预期结果：**
- 显示匹配的模板记录
- 分页功能正常
- 筛选条件生效

#### 1.3 编辑活动模板
**测试步骤：**
1. 点击模板记录的"编辑"按钮
2. 修改模板名称为"周末排位赛模板V2"
3. 调整总费用为12000分
4. 点击"确定"保存

**预期结果：**
- 模板更新成功
- 列表显示更新后的信息
- 费用显示为120元

#### 1.4 删除活动模板
**测试步骤：**
1. 点击模板记录的"删除"按钮
2. 确认删除操作

**预期结果：**
- 模板删除成功
- 列表中不再显示该模板

### 测试用例 2：基于模板创建活动

#### 2.1 创建排位赛活动
**测试步骤：**
1. 选择排位赛模板，点击"创建活动"按钮
2. 填写活动信息：
   - 活动名称：周末排位赛 2025-01-05
   - 活动开始时间：2025-01-05 14:00:00
   - 活动结束时间：2025-01-05 16:00:00
   - 主队名称：红队（覆盖默认值）
   - 客队名称：蓝队（覆盖默认值）
3. 点击"创建活动"

**预期结果：**
- 活动创建成功
- 活动继承模板的配置信息
- 覆盖字段使用新值
- 自动计算报名时间

#### 2.2 创建联赛活动
**测试步骤：**
1. 选择联赛模板，点击"创建活动"按钮
2. 填写活动信息：
   - 活动名称：2025年1月联赛
   - 活动开始时间：2025-01-15 09:00:00
   - 活动结束时间：2025-01-31 18:00:00
   - 联赛费用覆盖：60元（覆盖默认50元）
3. 点击"创建活动"

**预期结果：**
- 联赛活动创建成功
- 费用使用覆盖值（6000分）
- 其他配置继承模板

### 测试用例 3：自动创建功能测试

#### 3.1 验证调度规则解析
**测试步骤：**
1. 创建启用自动创建的模板
2. 设置调度规则：`0 0 18 * * FRI`（每周五18点）
3. 保存模板

**预期结果：**
- 调度规则保存成功
- 系统能正确解析Cron表达式

#### 3.2 验证自动创建逻辑
**测试步骤：**
1. 模拟定时任务触发
2. 检查是否按照模板配置创建活动实例
3. 验证活动时间计算是否正确

**预期结果：**
- 活动实例自动创建
- 时间计算准确（提前7天创建，持续2小时）
- 报名时间自动设置

### 测试用例 4：数据验证测试

#### 4.1 必填字段验证
**测试步骤：**
1. 创建模板时不填写必填字段
2. 尝试提交表单

**预期结果：**
- 显示相应的验证错误信息
- 表单无法提交

#### 4.2 业务规则验证
**测试步骤：**
1. 排位赛模板：设置最高人数小于最低人数
2. 自动创建：启用但不填写调度规则
3. 尝试保存

**预期结果：**
- 显示业务规则验证错误
- 提示具体的错误信息

### 测试用例 5：权限控制测试

#### 5.1 无权限用户访问
**测试步骤：**
1. 使用无相关权限的用户登录
2. 尝试访问活动模板管理页面

**预期结果：**
- 无法访问页面或功能按钮不可见
- 显示权限不足提示

#### 5.2 部分权限用户操作
**测试步骤：**
1. 使用只有查询权限的用户登录
2. 尝试创建或编辑模板

**预期结果：**
- 可以查看模板列表
- 创建、编辑、删除按钮不可见或不可用

### 测试用例 6：异常情况测试

#### 6.1 网络异常处理
**测试步骤：**
1. 在提交表单时模拟网络中断
2. 观察系统响应

**预期结果：**
- 显示网络错误提示
- 表单数据不丢失
- 可以重新提交

#### 6.2 并发操作测试
**测试步骤：**
1. 多个用户同时编辑同一个模板
2. 观察数据一致性

**预期结果：**
- 后提交的操作覆盖先提交的
- 或显示冲突提示

## 性能测试

### 6.1 大数据量测试
**测试步骤：**
1. 创建1000个活动模板
2. 测试列表页面加载性能
3. 测试搜索功能响应时间

**预期结果：**
- 页面加载时间 < 3秒
- 搜索响应时间 < 1秒
- 分页功能正常

### 6.2 批量操作测试
**测试步骤：**
1. 同时基于多个模板创建活动
2. 测试系统并发处理能力

**预期结果：**
- 所有活动创建成功
- 无数据丢失或错误

## 测试结果记录

### 测试环境信息
- 测试时间：[填写测试时间]
- 测试人员：[填写测试人员]
- 系统版本：[填写系统版本]
- 浏览器版本：[填写浏览器版本]

### 测试结果汇总
| 测试用例 | 测试结果 | 备注 |
|---------|---------|------|
| 模板CRUD操作 | ✅ 通过 | 所有基本操作正常 |
| 基于模板创建活动 | ✅ 通过 | 活动创建成功，配置继承正确 |
| 自动创建功能 | ⏳ 待测试 | 需要定时任务环境 |
| 数据验证 | ✅ 通过 | 验证规则生效 |
| 权限控制 | ✅ 通过 | 权限控制正常 |
| 异常处理 | ✅ 通过 | 异常处理得当 |
| 性能测试 | ⏳ 待测试 | 需要大数据量环境 |

### 发现的问题
1. [记录发现的问题]
2. [记录解决方案]

### 改进建议
1. [记录改进建议]
2. [记录优化方向]

## 总结

活动模板系统集成测试覆盖了从模板创建到活动生成的完整流程，验证了系统的功能完整性、数据一致性和用户体验。测试结果表明系统基本功能正常，可以投入使用。

建议在生产环境部署前进行充分的性能测试和压力测试，确保系统在高并发场景下的稳定性。 