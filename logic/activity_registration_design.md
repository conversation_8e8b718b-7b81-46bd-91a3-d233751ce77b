# 篮球活动报名模块设计详解

本文档详细阐述篮球活动报名功能的推荐实现方案：在 `saidian-module-operation` 中构建独立模块，仅复用 `yudao-module-pay` 的支付和退款能力，保持与 `yudao-module-trade` 的解耦。

## 1. 设计原则与目标

*   **独立性**: 活动报名业务逻辑内聚于 `saidian-module-operation` 模块。
*   **复用性**: 最大限度复用平台已有的支付、用户、球员/球队等基础能力。
*   **可扩展性**: 设计应易于未来扩展新的活动类型或修改现有规则。
*   **可维护性**: 清晰的职责划分和代码结构，降低维护成本。

## 2. 核心业务逻辑

### 2.1 组局时间机制（重要调整）

**组局时间点 (`registration_deadline`) 的作用**：
- **判断成局条件**：到达组局时间后，系统检查报名人数是否达到最低要求
- **人数不够**：取消活动，全额退款
- **人数达标**：创建比赛，活动状态变为"组局成功"
- **继续报名**：组局成功后，比赛开始前仍可继续报名和退赛
- **比赛开始**：比赛开始时间到达后，进入"进行中"状态，不允许报名和退赛，触发差额退款

**状态流转**：
```mermaid
stateDiagram-v2
    [*] --> DRAFT : 创建活动草稿
    
    %% 草稿状态的流转
    DRAFT --> REGISTRATION : 发布活动<br/>(比赛未开始)
    DRAFT --> CANCELLED : 取消活动
    
    %% 报名中状态的核心流转
    REGISTRATION --> GROUPING_SUCCESS : 报名截止时间到<br/>人数达标<br/>组局成功
    REGISTRATION --> GROUPING_FAILED : 报名截止时间到<br/>人数不足<br/>组局失败
    REGISTRATION --> CANCELLED : 管理员取消活动
    REGISTRATION --> COMPLETED : 比赛时间到<br/>(异常情况)
    
    %% 组局成功后的流转
    GROUPING_SUCCESS --> IN_PROGRESS : 比赛开始时间到<br/>触发差额退款<br/>禁止报名退赛
    
    %% 组局失败的流转
    GROUPING_FAILED --> CANCELLED : 全额退款<br/>活动取消
    
    %% 进行中状态的流转
    IN_PROGRESS --> COMPLETED : 管理员手动结束<br/>同步比赛状态
    IN_PROGRESS --> CANCELLED : 管理员强制取消<br/>(异常情况)
    
    %% 终止状态
    CANCELLED --> [*] : 活动结束
    COMPLETED --> [*] : 活动结束
    
    %% 状态说明
    note right of DRAFT : 0-草稿<br/>可编辑活动信息
    note right of REGISTRATION : 2-报名中<br/>用户可报名<br/>可退赛
    note right of GROUPING_SUCCESS : 8-组局成功<br/>已创建比赛<br/>仍可报名退赛
    note right of GROUPING_FAILED : 7-组局失败<br/>准备退款
    note right of IN_PROGRESS : 4-进行中<br/>比赛已开始<br/>不可报名退赛
    note right of COMPLETED : 5-已结束<br/>不可报名和退赛
    note right of CANCELLED : 6-已取消<br/>不可报名

    %% 关键业务规则标注
    classDef normalFlow fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef criticalCheck fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef successPath fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef failurePath fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef terminalState fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    
    class DRAFT,REGISTRATION normalFlow
    class GROUPING_SUCCESS,IN_PROGRESS successPath
    class GROUPING_FAILED failurePath
    class CANCELLED,COMPLETED terminalState
```

**核心业务规则**：
1. **报名阶段** (`REGISTRATION`): 用户可自由报名和退赛
2. **组局成功** (`GROUPING_SUCCESS`): 报名截止时间到达且人数达标，创建比赛，用户仍可报名和退赛直到比赛开始
3. **组局失败** (`GROUPING_FAILED`): 报名截止时间到达但人数不足，全额退款，活动取消
4. **比赛开始** (`IN_PROGRESS`): 比赛开始时间到达，不可报名和退赛，触发差额退款
5. **比赛结束** (`COMPLETED`): 比赛结束，活动完成

**时间触发机制**：
- `registration_deadline`: 触发组局检查 (`REGISTRATION` → `GROUPING_SUCCESS`/`GROUPING_FAILED`)
- `start_time`: 触发比赛开始 (`GROUPING_SUCCESS` → `IN_PROGRESS`)，同时触发差额退款

**手动触发机制**：
- 管理员手动结束活动: 触发活动结束 (`IN_PROGRESS` → `COMPLETED`)，同步比赛状态

**详细业务时序图**：
```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant System as 系统定时任务
    participant Activity as 活动状态
    participant User as 用户
    participant Payment as 支付系统
    
    Note over Admin, Payment: 活动创建与发布阶段
    Admin->>Activity: 创建活动草稿 (DRAFT)
    Admin->>Activity: 发布活动 (REGISTRATION)
    Note right of Activity: 比赛开始前，状态自动设为"报名中"
    
    Note over Admin, Payment: 用户报名阶段
    loop 用户报名期间
        User->>Activity: 检查活动状态 (REGISTRATION)
        User->>Payment: 报名并支付
        Payment-->>Activity: 支付成功，更新报名记录
        User->>Activity: 可选：取消报名并退款
    end
    
    Note over Admin, Payment: 组局检查阶段 (关键时间点)
    System->>System: registration_deadline 时间到达
    System->>Activity: 触发组局检查 (REGISTRATION → GROUPING)
    
    alt 人数达标
        System->>Activity: 组局成功 (GROUPING → GROUPING_SUCCESS)
        System->>Activity: 创建比赛结构 (GROUPING_SUCCESS → IN_PROGRESS)
        Note right of Activity: 进入"进行中"状态<br/>- 仍可报名和退赛<br/>- 直到比赛开始<br/>- 差额退款处理
        
        loop 组局成功后到比赛开始前
            User->>Activity: 仍可报名和退赛
            Note right of User: 正常的报名退赛流程
        end
        
        System->>System: start_time 时间到达
        System->>Activity: 比赛开始 (IN_PROGRESS → COMPLETED)
        
    else 人数不足
        System->>Activity: 组局失败 (GROUPING → GROUPING_FAILED)
        System->>Payment: 处理全额退款
        System->>Activity: 活动取消 (GROUPING_FAILED → CANCELLED)
        Note right of Activity: 活动终止，不可再报名
    end
    
    Note over Admin, Payment: 活动结束
    Activity->>System: 活动生命周期结束
```

**业务时间线图**：
```mermaid
gantt
    title 活动报名业务时间线
    dateFormat X
    axisFormat %s
    
    section 活动状态
    草稿阶段           :draft, 0, 1
    报名中            :registration, 1, 3
    组局检查          :grouping, 3, 3.5
    进行中            :inprogress, 3.5, 6
    已完成            :completed, 6, 7
    
    section 用户操作权限
    可报名可退赛 (报名期)    :active, user1, 1, 3
    暂停报名退赛 (组局中)    :crit, pause, 3, 3.5
    可报名可退赛 (组局成功后) :active, user2, 3.5, 6
    禁止报名退赛 (比赛中)    :done, user3, 6, 7
    
    section 关键时间点
    报名截止时间 (组局触发)   :milestone, deadline, 3, 0
    比赛开始时间 (权限截止)   :milestone, start, 6, 0
```

**时间节点说明**：
- **T1 时间点**: 活动发布，状态变为"报名中"，用户可报名可退赛
- **T3 时间点**: 报名截止时间到达，触发组局检查
- **T3.5 时间点**: 组局成功，创建比赛，状态变为"进行中"，用户仍可报名可退赛
- **T6 时间点**: 比赛开始，用户不可再报名和退赛，状态变为"已完成"

### 2.2 新增核心业务表 (位于 `saidian-module-operation` 数据库)

```sql
-- 活动定义表
CREATE TABLE `sd_activity` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` VARCHAR(255) NOT NULL COMMENT '活动名称',
  `type` TINYINT NOT NULL COMMENT '活动类型: 1-排位赛, 2-友谊赛, 3-联赛 (ActivityTypeEnum)',
  `game_type` TINYINT NULL DEFAULT 1 COMMENT '游戏类型: 1-全场5人制, 2-半场4人制 (与sd_game.game_type保持一致)',
  `game_id` BIGINT NULL COMMENT '关联的 sd_game.id (活动组局成功后创建)',
  `start_time` DATETIME NOT NULL COMMENT '活动开始时间（比赛时间）',
  `registration_deadline` DATETIME NOT NULL COMMENT '报名截止时间（组局时间点）',
  `location` VARCHAR(255) NULL COMMENT '活动地点',
  `pic_url` VARCHAR(1024) NULL COMMENT '活动封面图片地址',
  `total_fee` INT NULL COMMENT '总费用(分, 排位赛/友谊赛用)',
  `min_players_per_game` INT NULL COMMENT '每场最低人数(排位赛, 通常为16)',
  `max_players_per_game` INT NULL COMMENT '每场最高人数(排位赛, 通常为20)',
  `min_teams_friendly` INT DEFAULT 2 COMMENT '友谊赛最低成团队伍数',
  `min_players_per_team_friendly` INT NULL COMMENT '友谊赛每队最低人数(最少5人)',
  `max_players_per_team_friendly` INT NULL COMMENT '友谊赛每队最高人数',
  `league_fee_per_player` INT NULL COMMENT '联赛每人报名费(分)',
  `min_teams_league` INT NULL COMMENT '联赛最低成团队伍数',
  `min_players_per_team_league` INT NULL COMMENT '联赛每队最低人数',
  `friend_group_supported` BIT(1) DEFAULT b'0' COMMENT '是否支持好友组队 (主要用于排位赛)',
  `max_players_per_friend_group` TINYINT NULL DEFAULT 3 COMMENT '每个好友组最大人数 (排位赛用, 一般为3人)',
  `friend_group_invite_duration_minutes` INT NULL DEFAULT 30 COMMENT '好友组队邀请有效时长(分钟)',
  `home_team_id` BIGINT NULL COMMENT '主队ID (排位赛用, 关联 sd_team.id)',
  `guest_team_id` BIGINT NULL COMMENT '客队ID (排位赛用, 关联 sd_team.id)',
  `status` TINYINT NOT NULL COMMENT '活动状态: 0-草稿, 1-未开始, 2-报名中, 3-组局中, 4-进行中, 5-已结束, 6-已取消, 7-组局失败, 8-组局成功 (ActivityStatusEnum)',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `version` INT NOT NULL DEFAULT 0 COMMENT '版本号, 用于乐观锁',
  PRIMARY KEY (`id`),
  INDEX `idx_type_status_deadline` (`type`, `status`, `registration_deadline`),
  INDEX `idx_home_guest_teams` (`home_team_id`, `guest_team_id`)
) COMMENT='活动定义表';

-- 修改sd_game表，添加联赛相关字段和调整现有字段
ALTER TABLE `sd_game`
ADD COLUMN `league_id` BIGINT NULL COMMENT '关联的联赛ID (sd_league.id, 如果是联赛中的一场)' AFTER `id`,
ADD COLUMN `league_game_node_id` BIGINT NULL COMMENT '关联的联赛赛程节点ID (sd_league_game_node.id)' AFTER `league_id`,
ADD COLUMN `game_type` TINYINT NULL COMMENT '游戏类型: 1-全场5人制篮球赛, 2-半场4人制篮球赛, 其他待定义' AFTER `status`,
ADD COLUMN `remark` VARCHAR(512) NULL COMMENT '备注' AFTER `guest_score`,
CHANGE COLUMN `start_time` `game_time` DATETIME NULL COMMENT '比赛实际开始时间',
CHANGE COLUMN `home_team_points` `home_score` INT NULL COMMENT '主队得分',
CHANGE COLUMN `guest_team_points` `guest_score` INT NULL COMMENT '客队得分',
ADD INDEX `idx_league_game_node` (`league_game_node_id`);

-- sd_activity 是活动的主要定义实体，组局成功后通过 game_id 关联到 sd_game 记录

-- 报名记录表
CREATE TABLE `sd_registration` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID (关联 member_user.id)',
  `player_id` BIGINT NOT NULL COMMENT '球员ID (关联 sd_player.id)',
  `activity_id` BIGINT NOT NULL COMMENT '活动ID',
  `activity_type` TINYINT NOT NULL COMMENT '活动类型 (冗余自sd_activity.type)',
  `registration_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
  `team_id` BIGINT NULL COMMENT '所属球队ID (友谊赛/联赛, 关联 sd_team.id)',
  `friend_group_id` BIGINT NULL COMMENT '所属好友组ID (排位赛好友组队)',
  `is_group_leader` BIT DEFAULT b'0' COMMENT '是否为好友组队发起人',
  `payment_type` TINYINT NULL COMMENT '支付方式 (友谊赛): 1-球队整体支付, 2-球员个人支付AA (FriendlyPaymentTypeEnum)',
  `status` TINYINT NOT NULL COMMENT '报名状态: 1-待支付, 2-已支付, 3-报名成功, 4-已取消, 5-已完成, 6-候补中 (RegistrationStatusEnum)',
  `payment_status` TINYINT NOT NULL DEFAULT 1 COMMENT '支付状态: 1-未支付, 2-已支付 (与pay_order保持一致)',
  `pay_order_id` BIGINT NULL COMMENT '关联的 pay_order.id',
  `pay_time` DATETIME NULL COMMENT '支付时间',
  `should_pay_price` INT NOT NULL COMMENT '应付金额(分)',
  `actual_pay_price` INT NULL COMMENT '实付金额(分, 考虑优惠)',
  `coupon_id` BIGINT NULL COMMENT '使用的优惠券ID',
  `coupon_discount_price` INT DEFAULT 0 COMMENT '优惠券抵扣金额(分)',
  `points_used` INT DEFAULT 0 COMMENT '使用的积分数量',
  `points_discount_price` INT DEFAULT 0 COMMENT '积分抵扣金额(分)',
  `refund_status` TINYINT NOT NULL DEFAULT 0 COMMENT '退款状态: 0-未退款, 1-部分退款, 2-全额退款, 3-退款中, 4-退款失败',
  `total_refund_price` INT DEFAULT 0 COMMENT '累计退款金额(分)',
  `cancel_reason` VARCHAR(255) NULL COMMENT '取消原因',
  `team_assigned` BIGINT NULL COMMENT '分配到的具体球队ID (关联 sd_team.id)，直接存储真实球队ID，如101L、102L等',
  `jersey_color` VARCHAR(100) NULL COMMENT '本场比赛使用的球服颜色',
  `is_waitlist` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否为候补报名',
  `waitlist_position` INT NULL COMMENT '候补队列位置',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `version` INT NOT NULL DEFAULT 0 COMMENT '版本号, 用于乐观锁',
  PRIMARY KEY (`id`),
  INDEX `idx_activity` (`activity_id`),
  INDEX `idx_pay_order` (`pay_order_id`),
  INDEX `idx_team` (`team_id`),
  INDEX `idx_friend_group` (`friend_group_id`),
  INDEX `idx_waitlist` (`activity_id`, `is_waitlist`, `waitlist_position`)
) COMMENT='活动报名记录表';

-- 好友组队信息表
CREATE TABLE `sd_friend_group` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '好友组ID',
  `activity_id` BIGINT NOT NULL COMMENT '活动ID',
  `leader_user_id` BIGINT NOT NULL COMMENT '发起人用户ID',
  `leader_registration_id` BIGINT NOT NULL COMMENT '发起人报名记录ID',
  `status` TINYINT NOT NULL COMMENT '组队状态: 1-组队中, 2-组队成功, 3-已解散 (FriendGroupStatusEnum)',
  `team_assigned` BIGINT NULL COMMENT '排位赛分配队伍ID: 直接存储真实球队ID (关联 sd_team.id)，如101L、102L等',
  `expires_at` DATETIME NULL COMMENT '房间过期时间',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `invite_code` VARCHAR(32) NULL COMMENT '好友组邀请码 (用于分享)',
  `version` INT NOT NULL DEFAULT 0 COMMENT '版本号, 用于乐观锁',
  PRIMARY KEY (`id`),
  INDEX `idx_activity_leader` (`activity_id`, `leader_user_id`),
  INDEX `idx_status_createtime` (`status`, `create_time`),
  INDEX `idx_invite_code` (`invite_code`)
) COMMENT='好友组队信息表';

-- 报名/退款等操作日志表
CREATE TABLE `sd_registration_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `registration_id` BIGINT NOT NULL COMMENT '报名记录ID',
  `user_id` BIGINT NOT NULL COMMENT '操作用户ID (0代表系统)',
  `log_type` TINYINT NOT NULL COMMENT '日志类型: 1-创建报名, 2-支付成功, 3-申请退款, 4-审核退款, 5-退款成功, 6-取消报名, 7-组局成功, 8-组局失败, 9-差额退款, 10-好友组队创建, 11-好友加入组队, 12-好友组队解散 (RegistrationLogTypeEnum)',
  `content` VARCHAR(512) NOT NULL COMMENT '日志内容',
  `before_status` TINYINT NULL COMMENT '操作前状态',
  `after_status` TINYINT NULL COMMENT '操作后状态',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_registration` (`registration_id`)
) COMMENT='活动报名日志表';

-- 联赛定义表
CREATE TABLE `sd_league` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '联赛ID',
  `activity_id` BIGINT NOT NULL COMMENT '关联的报名活动ID',
  `name` VARCHAR(255) NOT NULL COMMENT '联赛名称',
  `format_type` TINYINT NOT NULL COMMENT '赛制类型: 1-循环赛, 2-单败淘汰赛, 3-小组循环+单败淘汰赛... (LeagueFormatTypeEnum)',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '联赛状态: 1-未开始, 2-进行中, 3-已结束, 4-已取消 (LeagueStatusEnum)',
  `start_date` DATE NULL COMMENT '预计开始日期',
  `config_json` JSON NULL COMMENT '赛制特定配置 (如积分规则, 小组数, 种子规则等)',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_activity_id` (`activity_id`)
) COMMENT='联赛定义表';

-- 联赛参赛队伍表
CREATE TABLE `sd_league_team` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '参赛队ID',
  `league_id` BIGINT NOT NULL COMMENT '联赛ID',
  `team_id` BIGINT NOT NULL COMMENT '球队ID',
  `group_name` VARCHAR(50) NULL COMMENT '所属小组名 (小组赛用)',
  `seed` INT NULL COMMENT '种子排位 (淘汰赛用)',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '参赛状态: 1-正常, 2-已淘汰, 3-已退赛 (LeagueTeamStatusEnum)',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_league_team` (`league_id`, `team_id`)
) COMMENT='联赛参赛队伍表';

-- 联赛阶段表 (可选, 用于复杂赛制)
CREATE TABLE `sd_league_stage` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
  `league_id` BIGINT NOT NULL COMMENT '联赛ID',
  `name` VARCHAR(100) NOT NULL COMMENT '阶段名称 (如 小组赛A组, 16强, 半决赛)',
  `stage_order` INT NOT NULL COMMENT '阶段顺序',
  `parent_stage_id` BIGINT NULL COMMENT '父阶段ID (用于嵌套)',
  `stage_type` TINYINT NULL COMMENT '阶段类型 (如 GROUP, KNOCKOUT) (LeagueStageTypeEnum)',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_league_order` (`league_id`, `stage_order`)
) COMMENT='联赛阶段表';

-- 联赛赛程节点表 (核心: 处理动态对阵)
CREATE TABLE `sd_league_game_node` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '赛程节点ID',
  `league_id` BIGINT NOT NULL COMMENT '联赛ID',
  `stage_id` BIGINT NULL COMMENT '阶段ID',
  `round_number` INT NULL COMMENT '轮次 (如淘汰赛第几轮)',
  `node_order_in_round` INT NULL COMMENT '轮内节点顺序',
  `game_id` BIGINT NULL COMMENT '实际比赛ID (sd_game.id, 对手确定并安排后填充)',
  `home_team_source_type` TINYINT NOT NULL COMMENT '主队来源类型: 1-指定队伍ID, 2-某节点胜者, 3-某节点负者, 4-小组名次, 5-待定... (GameNodeTeamSourceTypeEnum)',
  `home_team_source_ref` VARCHAR(100) NULL COMMENT '主队来源引用 (根据类型存 team_id, 上游node_id, 或小组名+名次 G1-1)',
  `guest_team_source_type` TINYINT NOT NULL COMMENT '客队来源类型',
  `guest_team_source_ref` VARCHAR(100) NULL COMMENT '客队来源引用',
  `estimated_time` DATETIME NULL COMMENT '预计比赛时间',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '节点状态: 1-待定对手, 2-对手已确定/待安排比赛, 3-已安排比赛, 4-比赛进行中, 5-比赛已结束 (LeagueGameNodeStatusEnum)',
  `winner_to_node_id` BIGINT NULL COMMENT '胜者进入的下一节点ID',
  `loser_to_node_id` BIGINT NULL COMMENT '负者进入的下一节点ID (用于双败等)',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  INDEX `idx_league_status` (`league_id`, `status`),
  INDEX `idx_game_id` (`game_id`)
) COMMENT='联赛赛程节点表';

-- 联赛积分排名表
CREATE TABLE `sd_league_standing` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '排名记录ID',
  `league_id` BIGINT NOT NULL COMMENT '联赛ID',
  `stage_id` BIGINT NULL COMMENT '阶段ID (排名可能分阶段)',
  `team_id` BIGINT NOT NULL COMMENT '球队ID',
  `group_name` VARCHAR(50) NULL COMMENT '所属小组 (用于小组排名)',
  `played` INT NOT NULL DEFAULT 0 COMMENT '已赛场次',
  `wins` INT NOT NULL DEFAULT 0 COMMENT '胜场',
  `draws` INT NOT NULL DEFAULT 0 COMMENT '平局 (篮球可能没有)',
  `losses` INT NOT NULL DEFAULT 0 COMMENT '负场',
  `points_for` INT NOT NULL DEFAULT 0 COMMENT '总得分',
  `points_against` INT NOT NULL DEFAULT 0 COMMENT '总失分',
  `point_difference` INT NOT NULL DEFAULT 0 COMMENT '净胜分',
  `points` INT NOT NULL DEFAULT 0 COMMENT '积分 (根据规则计算)',
  `rank` INT NULL COMMENT '排名',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_league_stage_team` (`league_id`, `stage_id`, `team_id`),
  INDEX `idx_league_rank` (`league_id`, `stage_id`, `rank`)
) COMMENT='联赛积分排名表';

 -- 活动模板表
 CREATE TABLE `sd_activity_template` (
   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '模板ID',
   `name` VARCHAR(255) NOT NULL COMMENT '模板名称',
   `type` TINYINT NOT NULL COMMENT '活动类型 (ActivityTypeEnum)',
   `location` VARCHAR(255) NULL COMMENT '默认活动地点',
   `pic_url` VARCHAR(1024) NULL COMMENT '活动模板封面图片地址',
   `total_fee` INT NULL COMMENT '排位/友谊赛默认总费用(分)',
   `min_players_per_game` INT NULL COMMENT '排位赛默认最低人数',
   `max_players_per_game` INT NULL COMMENT '排位赛默认最高人数',
   `min_teams_friendly` INT DEFAULT 2 COMMENT '友谊赛默认最低队伍数',
   `min_players_per_team_friendly` INT NULL COMMENT '友谊赛默认每队最低人数',
   `max_players_per_team_friendly` INT NULL COMMENT '友谊赛默认每队最高人数',
   `league_fee_per_player` INT NULL COMMENT '联赛默认每人报名费(分)',
   `min_teams_league` INT NULL COMMENT '联赛默认最低队伍数',
   `min_players_per_team_league` INT NULL COMMENT '联赛默认每队最低人数',
   `remark` VARCHAR(512) NULL COMMENT '默认备注/描述',
   `auto_creation_enabled` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否启用自动创建',
   `auto_creation_schedule` VARCHAR(100) NULL COMMENT '自动创建调度规则 (如 CRON 表达式)',
   `auto_creation_offset_days` INT NULL COMMENT '提前多少天创建活动实例',
   `auto_creation_duration_hours` INT NULL COMMENT '活动持续时长(小时)',
   `auto_registration_open_days` INT NULL COMMENT '活动开始前多少天自动开放报名（自动修改活动状态为报名中）',
   `auto_registration_deadline_hours` INT NULL COMMENT '活动开始前多少小时截止报名',
   `default_home_team_id` BIGINT NULL COMMENT '默认主队ID (排位赛模板用, 关联 sd_team.id)',
   `default_guest_team_id` BIGINT NULL COMMENT '默认客队ID (排位赛模板用, 关联 sd_team.id)',
   `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
   `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
   `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
   `tenant_id` BIGINT NOT NULL DEFAULT '0' COMMENT '租户编号',
   PRIMARY KEY (`id`)
 ) COMMENT='活动模板表';
```

## 3. 核心模块与类设计

### 3.1 模块划分

*   **`saidian-module-operation-api`**: 定义对外接口、DTO、枚举、常量
*   **`saidian-module-operation-biz`**: 业务逻辑实现

### 3.2 核心包结构 (`saidian-module-operation-biz`)

```
cn.iocoder.yudao.module.operation
├── controller            # 控制器层
│   ├── admin             # 管理后台接口
│   │   ├── ActivityController.java
│   │   ├── ActivityTemplateController.java # (新增)
│   │   └── RegistrationController.java
│   ├── app               # App端接口
│   │   ├── registration  # 报名相关业务接口
│   │   │   └── AppRegistrationController.java
│   │   ├── activity      # 活动相关业务接口
│   │   │   └── AppActivityController.java
│   │   ├── friendgroup   # 好友组队相关业务接口
│   │   │   └── AppFriendGroupController.java
│   │   └── notify        # 回调通知接口 (统一管理第三方回调)
│   │       ├── OperationPayNotifyController.java    # 支付回调
│   │       └── OperationRefundNotifyController.java # 退款回调
├── convert               # 对象转换器
│   ├── ActivityConvert.java
│   ├── RegistrationConvert.java
│   └── FriendGroupConvert.java             # 好友组对象转换器
├── dal                   # 数据访问层
│   ├── dataobject        # 数据对象
│   │   ├── ActivityDO.java
│   │   ├── RegistrationDO.java
│   │   ├── FriendGroupDO.java              # 好友组数据对象
│   │   └── RegistrationLogDO.java
│   └── mysql             # MyBatis Mapper接口
│       ├── ActivityMapper.java
│       ├── RegistrationMapper.java
│       ├── FriendGroupMapper.java          # 好友组Mapper接口
│       └── RegistrationLogMapper.java
├── service               # 业务逻辑层
│   ├── activity          # 活动相关服务
│   │   ├── ActivityService.java
│   │   ├── impl
│   │   │   └── ActivityServiceImpl.java
│   │   └── event         # 活动相关事件定义 (按业务模块分散)
│   │       ├── ActivityGroupingSuccessEvent.java
│   │       ├── ActivityGroupingFailedEvent.java
│   │       └── ActivityStatusChangedEvent.java
│   ├── template          # 活动模板服务 (新增)
│   │   ├── ActivityTemplateService.java
│   │   └── impl
│   │       └── ActivityTemplateServiceImpl.java
│   ├── registration      # 报名相关服务
│   │   ├── RegistrationService.java
│   │   ├── impl
│   │   │   └── RegistrationServiceImpl.java
│   │   └── event         # 报名相关事件定义 (按业务模块分散)
│   │       ├── RegistrationPaidEvent.java  # 报名支付成功事件
│   │       ├── RegistrationCancelledEvent.java  # 报名取消事件
│   │       └── RegistrationStatusChangedEvent.java
│   ├── friendgroup       # 好友组队相关服务
│   │   ├── FriendGroupService.java         # 好友组队服务接口
│   │   ├── FriendGroupCodeUtil.java        # 好友组邀请码工具类
│   │   ├── impl
│   │   │   └── FriendGroupServiceImpl.java # 好友组队服务实现
│   │   └── event         # 好友组队相关事件定义 (按业务模块分散)
│   │       ├── FriendGroupCreatedEvent.java
│   │       ├── FriendGroupExpiredEvent.java
│   │       └── FriendGroupMemberJoinedEvent.java  # 好友组更新事件
│   ├── refund            # 退款相关服务
│   │   ├── ActivityRefundService.java
│   │   ├── calculator    # 退款计算器（策略模式+模板方法）
│   │   │   ├── RefundCalculator.java           # 退款计算器接口（内置通用逻辑）
│   │   │   ├── RefundCalculatorFactory.java    # 退款计算器工厂
│   │   │   └── impl
│   │   │       ├── FriendlyRefundCalculator.java   # 友谊赛退款计算器
│   │   │       ├── RankingRefundCalculator.java    # 排位赛退款计算器
│   │   │       └── LeagueRefundCalculator.java     # 联赛退款计算器
│   │   └── impl
│   │       └── ActivityRefundServiceImpl.java
│   ├── league            # 联赛相关服务 (新增)
│   │   ├── LeagueScheduleService.java # 联赛赛程服务
│   │   ├── LeagueRankingService.java # 联赛排名服务
│   │   └── impl
│   │       ├── LeagueScheduleServiceImpl.java
│   │       └── LeagueRankingServiceImpl.java
│   ├── teamassignment    # 分队算法相关服务 (新增)
│   │   ├── TeamAssignmentService.java      # 分队算法服务接口
│   │   ├── impl
│   │   │   └── TeamAssignmentServiceImpl.java # 分队算法服务实现
│   │   ├── algorithm     # 分队算法核心实现
│   │   │   ├── TeamBalanceAlgorithm.java   # 分队平衡算法接口
│   │   │   ├── impl
│   │   │   │   ├── RankingTeamBalanceAlgorithm.java    # 排位赛分队算法
│   │   │   │   ├── RealtimeTeamAssignmentAlgorithm.java # 实时分队算法
│   │   │   │   └── FinalTeamBalanceAlgorithm.java      # 最终分队平衡算法
│   │   │   ├── model     # 分队算法数据模型
│   │   │   │   ├── TeamAssignmentContext.java  # 分队上下文
│   │   │   │   ├── PlayerTeamInfo.java         # 球员分队信息
│   │   │   │   ├── TeamBalanceResult.java      # 分队结果
│   │   │   │   ├── FriendGroupConstraint.java  # 好友组约束
│   │   │   │   └── TeamStatistics.java        # 队伍统计信息
│   │   │   └── strategy  # 分队策略
│   │   │       ├── BalanceStrategy.java        # 平衡策略接口
│   │   │       ├── impl
│   │   │       │   ├── AbilityBalanceStrategy.java     # 能力值平衡策略
│   │   │       │   ├── HeightBalanceStrategy.java      # 身高平衡策略
│   │   │       │   ├── PositionBalanceStrategy.java    # 位置平衡策略
│   │   │       │   └── FriendGroupBalanceStrategy.java # 好友组平衡策略
│   │   │       └── factory
│   │   │           └── BalanceStrategyFactory.java     # 平衡策略工厂
│   │   └── event         # 分队相关事件定义
│   │       ├── TeamAssignmentChangedEvent.java         # 分队变更事件
│   │       ├── PlayerTeamAssignedEvent.java            # 球员分队事件
│   │       └── FriendGroupSplitEvent.java              # 好友组拆分事件
│   ├── discount          # 优惠计算服务 (采用Spring @Order管理的有序处理器列表)
│   │   ├── DiscountService.java            # 优惠服务门面接口
│   │   ├── impl
│   │   │   └── DiscountServiceImpl.java    # 优惠服务实现 (负责创建DiscountContext, 编排处理器列表和处理副作用)
│   │   ├── handler                          # 处理器 (Spring beans with @Order)
│   │   │   ├── DiscountHandler.java         # 处理器接口 (e.g., void calculateAndApply(DiscountContext context))
│   │   │   ├── CouponDiscountHandler.java   # 优惠券处理器
│   │   │   └── PointsDiscountHandler.java   # 积分处理器
│   │   ├── model                          # 处理器使用的数据模型
│   │   │   ├── DiscountContext.java       # 折扣计算上下文 (由DiscountServiceImpl创建, 包含 List<AppliedDiscountDetailVO> 用于收集优惠明细, 可内含辅助方法添加优惠条目)
│   │   │   └── DiscountResult.java        # (可移除或简化) 最终结果由DiscountContext承载, RegistrationPricingResult由DiscountService从Context构建
│   │   └──dto                             # 优惠计算相关的DTO
│   │       ├── RegistrationPricingResult.java # 最终价格和优惠应用详情 (包含 List<AppliedDiscountDetailVO>)
│   │       └── AppliedDiscountDetailVO.java # 新增DTO，表示单项已应用的优惠详情 (位于本biz包下)
│   ├── waitlist            # 候补队列服务
│   │   ├── WaitlistService.java          # 候补队列服务接口
│   │   └── impl
│   │       └── WaitlistServiceImpl.java   # 候补队列服务实现
│   ├── strategy              # 活动策略模式相关类
│   │   ├── ActivityStrategy.java            # 活动策略接口
│   │   ├── impl
│   │   │   ├── RankingActivityStrategy.java # 排位赛策略实现
│   │   │   ├── FriendlyActivityStrategy.java # 友谊赛策略实现
│   │   │   └── LeagueActivityStrategy.java  # 联赛策略实现
│   │   └── factory
│   │       └── ActivityStrategyFactory.java # 策略工厂
├── state                 # 状态模式相关类
│   ├── RegistrationState.java           # 报名状态接口
│   ├── impl
│   │   ├── PendingPaymentState.java     # 待支付状态
│   │   ├── PaidState.java               # 已支付状态
│   │   ├── SuccessfulState.java         # 报名成功状态
│   │   ├── CancelledState.java          # 已取消状态
│   │   └── CompletedState.java          # 已完成状态
│   └── factory
│       └── RegistrationStateFactory.java # 状态工厂
├── processor             # 处理器相关类
│   ├── AbstractRefundProcessor.java     # 抽象退款处理器(模板方法)
│   ├── impl
│   │   ├── UserCancelRefundProcessor.java  # 用户取消退款处理器
│   │   ├── GroupingFailedRefundProcessor.java # 组局失败退款处理器
│   │   └── DifferenceRefundProcessor.java  # 差额退款处理器
│   └── factory
│       └── RefundProcessorFactory.java   # 处理器工厂
├── mq                    # 消息队列消费者 (与现有PlayerCareerCreateByRegisterConsumer保持兼容)
│   ├── registration      # 报名相关消费者
│   │   ├── RegistrationPaidConsumer.java
│   │   ├── RegistrationCancelledConsumer.java
│   │   └── RegistrationNotificationConsumer.java
│   ├── activity          # 活动相关消费者
│   │   ├── ActivityGroupingConsumer.java
│   │   ├── ActivityNotificationConsumer.java
│   │   └── ActivityStatisticsConsumer.java
│   ├── friendgroup       # 好友组队相关消费者
│   │   ├── FriendGroupNotificationConsumer.java
│   │   └── FriendGroupTimeoutConsumer.java
│   ├── notification      # 通用通知消费者
│   │   ├── SmsNotificationConsumer.java
│   │   ├── EmailNotificationConsumer.java
│   │   └── PushNotificationConsumer.java
│   └── player            # 现有的球员相关消费者 (保持兼容)
│       └── PlayerCareerCreateByRegisterConsumer.java
├── job                   # 定时任务
│   ├── ActivityGroupingJob.java         # 活动组局定时任务
│   │   └── **核心职责**: 
│   │       *   检查 `registration_deadline` 已到且状态为 `报名中` 的活动
│   │       *   根据活动类型检查人数/队伍数是否达标
│   │       *   达标：创建比赛/联赛，状态变为"进行中"，仍可报名但不可退赛
│   │       *   不达标：状态变为"已取消"，触发全额退款
│   ├── FriendGroupTimeoutJob.java       # 好友组队超时处理
│   ├── ActivityAutoCreationJob.java     # 活动自动创建
│   └── ActivityStatusManagementJob.java # 活动状态自动管理（新增）
│       └── **核心职责**:
│           *   检查基于模板创建的活动，根据模板的 `auto_registration_open_days` 配置
│           *   自动将活动状态从"未开始"修改为"报名中"
│           *   提供活动状态的自动化管理
└── common                # 通用工具类
    ├── base              # 基础抽象类
    │   └── AbstractNotifyController.java  # 统一回调处理基类 (可选)
    └── util              # 工具类
        └── NotifyUtil.java               # 回调处理工具类 (可选)
```

### 3.3 核心测试结构 (`saidian-module-operation-biz`)

```
cn.iocoder.yudao.module.operation
├── service               # 服务层测试
│   ├── activity          # 活动相关服务测试
│   │   └── ActivityServiceTest.java
│   ├── registration      # 报名相关服务测试
│   │   └── RegistrationServiceTest.java
│   ├── teamassignment    # 分队算法相关服务测试 (新增)
│   │   ├── TeamAssignmentServiceTest.java
│   │   └── algorithm
│   │       ├── RankingTeamBalanceAlgorithmTest.java
│   │       ├── RealtimeTeamAssignmentAlgorithmTest.java
│   │       └── FinalTeamBalanceAlgorithmTest.java
│   ├── refund            # 退款相关服务测试
│   │   └── ActivityRefundServiceTest.java
│   ├── discount          # 优惠服务及责任链测试
│   │   └── DiscountServiceTest.java
│   │   └── handler
│   │       └── CouponDiscountHandlerTest.java
│   │       └── PointsDiscountHandlerTest.java
│   └── strategy          # 策略模式相关测试
│       └── impl
│           ├── RankingActivityStrategyTest.java
│           ├── FriendlyActivityStrategyTest.java # 友谊赛策略测试，包含AA支付测试
│           └── LeagueActivityStrategyTest.java
├── processor             # 处理器相关测试
│   └── impl
│       ├── UserCancelRefundProcessorTest.java
│       ├── GroupingFailedRefundProcessorTest.java
│       └── DifferenceRefundProcessorTest.java
└── controller            # 控制器测试
    ├── admin             # 管理后台接口测试
    │   └── ActivityControllerTest.java
    └── app               # App端接口测试
        └── AppRegistrationControllerTest.java
```

### 3.3 核心类与职责

*   **Controllers**: (职责不变，负责接收请求，调用 Service)。`OperationPayNotifyController` 负责接收支付和退款回调。
*   **`RefundCalculator` (接口及其实现)**: **【策略模式 + 模板方法模式】**
    *   封装特定活动类型的退款计算逻辑，职责从 `ActivityStrategy` 分离：
        *   `calculateDifferenceRefundAmount()`: 计算差额退款金额。
        *   `calculateCancelRefundAmount()`: 计算用户取消退款金额。
        *   `calculateGroupingFailedRefundAmount()`: 计算组局失败退款金额。
    *   **保持单向依赖**，被 `ActivityRefundService` 使用，不依赖其他业务服务。
    *   **内置通用计算逻辑**：接口提供默认的差额退款计算方法，避免重复代码：
        *   `calculateDifferenceRefundWithParams()`: 通用差额退款计算模板方法
        *   各实现类只需提供活动类型特定的参数（总费用、最低人数等）
        *   统一处理优惠比例、参数校验、日志记录等通用逻辑
*   **`RefundCalculatorFactory`**: 
    *   提供根据活动类型获取对应退款计算器的功能。
    *   与 `ActivityRefundService` 配合使用，解决了循环依赖问题。
*   **`ActivityService`**: 基础的活动 CRUD 和查询。
    *   基础的活动 CRUD 和查询。
    *   **新增**: 支持基于模板创建活动实例：提供 `createActivityFromTemplate(templateId, instanceDetails)` 方法，根据模板信息和实例特定信息（如开始/结束时间）创建 `sd_activity` 记录。
    *   **排位赛主队客队选择**: 对于排位赛类型的活动模板，基于模板创建活动实例时需要额外处理主队客队信息：
        *   **模板默认值**: 从`sd_activity_template`的`default_home_team_id`, `default_guest_team_id`字段获取默认的主队客队信息。
        *   **管理员选择**: 创建活动时，管理员可以选择或修改具体的主队和客队，确保活动的主队客队信息完整。
        *   **数据完整性**: 确保创建的活动实例在`sd_activity`表中的`home_team_id`, `guest_team_id`字段都有合适的值，以支持前端展示和分队逻辑。前端需要根据teamId查询球队详细信息。
*   **`RegistrationService`**: **核心协调者**。
    *   **不直接实现复杂业务逻辑**，而是根据活动类型获取 `ActivityStrategy`，根据报名状态获取 `RegistrationState`，并将具体操作**委托**给它们。
    *   **优惠计算与应用**: 对于报名过程中的费用计算，特别是涉及优惠券、积分抵扣等场景，`RegistrationService` 将委托给专门的 `DiscountService`。
    *   **候补机制处理**: 
        *   当排位赛报名人数达到上限时，新报名用户进入候补队列(`is_waitlist=1`)。
        *   维护候补队列顺序(`waitlist_position`)。
        *   当有用户退赛时，自动从候补队列中按顺序补位。
        *   补位成功后通知用户并更新状态。
    *   管理事务边界。
    *   调用外部 API (如 `PayOrderApi`)。
    *   发布业务事件。
    *   **好友组队流程协调**: (针对排位赛) 协调好友组队的整体流程，例如：发起人报名并支付成功 -> **自动创建 `sd_friend_group` 记录 (房间)** -> (前端交互)发起人通过平台内通知邀请好友 -> 好友接受邀请，独立完成报名和支付流程，报名记录关联到同一房间 -> 分配至同队 (需符合平衡规则)。**重要**: 每个报名记录对应固定的房间关联，用户如需更换房间应通过退赛重新报名实现。超时的检查与处理委托给 `FriendGroupTimeoutJob`。
*   **`DiscountService` (接口及其实现类 `DiscountServiceImpl`)**: **【门面模式 & 有序处理器列表协调者】**
    *   **职责**: 作为优惠计算和应用的统一入口。
    *   **内部实现**: 
        *   负责创建和初始化 `DiscountContext`。
        *   注入 `List<DiscountHandler>`，该列表由 Spring 根据各 `DiscountHandler` Bean 的 `@Order` 注解自动排序。
        *   `DiscountService` 按顺序迭代这个列表中的 `DiscountHandler`，并调用其 `calculateAndApply(DiscountContext context)` 方法。
        *   `DiscountContext` 作为数据载体在处理器间传递和修改，包含用户ID、活动信息、原始价格、用户选择的优惠券和积分使用意愿、当前计算价格、以及一个用于收集各项已应用优惠明细的 `List<AppliedDiscountDetailVO>`。
        *   各处理器直接修改 `DiscountContext`。
        *   `DiscountService` 最终根据 `DiscountContext` 的最终状态（包含各项优惠明细），组装 `RegistrationPricingResult` 或 `AppRegistrationSettlementRespVO`。
    *   **`calculateSettlementDetails` 方法**:
        *   用于订单确认前的结算信息展示。
        *   创建 `DiscountContext`。
        *   调用有序的 `DiscountHandler` 列表进行优惠计算，处理器不执行任何副作用，仅更新 `DiscountContext`。
        *   `DiscountServiceImpl` 额外获取用户所有可用优惠券列表、积分概要等信息，结合 `DiscountContext` 中的计算结果和优惠明细组装成 `AppRegistrationSettlementRespVO` (包含 `List<AppliedDiscountDetailVO>`) 返回。
    *   **`applyAndFinalizeDiscounts` 方法**:
        *   用于创建报名订单时，实际应用优惠。
        *   创建 `DiscountContext`。
        *   首先调用有序的 `DiscountHandler` 列表进行优惠计算，更新 `DiscountContext`。
        *   然后，**`DiscountServiceImpl` 自身负责**根据 `DiscountContext` 中记录的要使用的优惠（如选中的优惠券ID、确认使用的积分数），调用外部API（`CouponApi.useCoupon()`, `MemberUserApi`的积分扣减接口）来**执行实际的优惠核销操作**。
        *   根据核销结果和 `DiscountContext` 的最终状态，组装并返回 `RegistrationPricingResult`，其中包含最终支付金额和 `List<AppliedDiscountDetailVO>`。
*   **`DiscountHandler` (接口及其实现，如 `PointsDiscountHandler`, `CouponDiscountHandler`)**: **【有序处理器】**
    *   每个 Handler 实现共同的接口 (例如: `void calculateAndApply(DiscountContext context);`)，并标记为 Spring Bean，使用 `@Order` 注解指定其执行顺序。
    *   负责一种具体的优惠类型计算（如积分抵扣、优惠券减免）。
    *   接收 `DiscountContext`，根据自身逻辑计算优惠，并直接修改 `DiscountContext` (例如更新当前价格，添加一个 `AppliedDiscountDetailVO` 到上下文的列表中)。
    *   **重要**: Handler 自身不执行外部 API 调用（如核销优惠券），仅做计算和逻辑判断。
*   **`AbstractDiscountHandler` (可选的抽象基类)**:
    *   实现 `DiscountHandler` 接口。
    *   **不包含** `next` 处理器引用及 `setNext()` / `applyNext()` 等链式调用方法。
    *   可以提供一些通用的辅助方法，供具体的 Handler 子类使用，例如标准地向 `DiscountContext` 中添加 `AppliedDiscountDetailVO`。
*   **`DiscountHandlerFactory`**: **【职责明确】**
    *   主要职责是创建 `DiscountContext` 对象，或者提供 `DiscountHandler` 可能需要的共享工具或配置。
    *   不再负责构建和排序责任链，该职责已由 Spring 的 `@Order` 和依赖注入机制处理。
*   **`DiscountContext` / `AppliedDiscountDetailVO`**: (DTOs)
    *   `DiscountContext`: 由 `DiscountServiceImpl` 创建。处理器处理过程中传递和修改的数据上下文。将包含输入参数、中间计算值、当前价格、以及一个 `List<AppliedDiscountDetailVO>` 来收集每项优惠的明细。可以考虑在此DTO内部提供辅助方法来方便地添加 `AppliedDiscountDetailVO` 记录。
    *   `DiscountResult`: 此前设计为责任链中每个处理器返回结果的DTO，其角色被弱化或移除。最终的计算结果体现在 `DiscountContext` 的最终状态中。`DiscountService` 会基于此 `DiscountContext` 构建其对外返回的 `RegistrationPricingResult` 等。
    *   `AppliedDiscountDetailVO`: 新增DTO (位于 `cn.iocoder.yudao.module.operation.service.discount.dto` 包下)，用于表示单项已应用的优惠详情。
        *   `discountType` (String/Enum): 优惠类型 (如 "COUPON", "POINTS")
        *   `description` (String): 优惠描述 (如 "优惠券减免", "积分抵扣")
        *   `amountDeducted` (Integer): 减免金额 (分)
        *   `referenceId` (String/Long, 可选): 关联ID (如优惠券ID)
*   **`ActivityStrategy` (接口及其实现)**: **【策略模式】**
    *   封装特定活动类型（排位、友谊、联赛）的差异化逻辑：
        *   `ActivityTypeEnum getActivityType()`: 返回此策略对应的活动类型枚举。
        *   `FeeCalculationContext` (内部类): 一个上下文对象，用于向费用计算方法传递所需信息。
            *   `Long teamId`: 相关的球队ID (例如，在友谊赛或联赛报名时，或排位赛好友组队时)。
            *   `Integer submittedPaymentType`: 用户在当前报名请求中提交的支付方式 (例如，友谊赛中队员选择AA或队长选择整队垫付)。可能为null。
            *   `AppRegistrationCreateReqVO createReqVO`: 可选的、完整的报名创建请求对象。这允许策略在需要时访问报名的所有原始输入参数，以实现更复杂的费用计算或校验逻辑。
        *   `void validateRegistration(ActivityDO activity, RegistrationDO registration, Long userId)`: 校验用户是否有资格报名参加指定的活动。
            *   `activity`: 当前活动信息。
            *   `registration`: 当前的报名信息（在首次创建报名记录前可能为 null）。
            *   `userId`: 发起报名的用户ID。
            *   策略实现应检查如活动状态（必须为"报名中"状态）、报名人数限制、用户黑名单、特定活动类型的特定条件（例如，联赛是否需要先加入球队）等。不再校验报名开始时间，只要活动状态为"报名中"即可报名。
        *   `Integer calculateEffectiveFee(@NotNull ActivityDO activity, @NotNull FeeCalculationContext context)`: **核心费用计算方法**。根据活动信息和传入的 `FeeCalculationContext` 计算并返回用户此次报名应支付的金额（单位：分）。
            *   `activity`: 当前活动信息。
            *   `context`: 包含费用计算所需全部信息的上下文对象。策略实现会从 `context` 中获取 `teamId`、`submittedPaymentType` 以及 `createReqVO`（如果需要），结合活动自身的费用配置（如总费用、人均费用、阶梯价格等）来决定最终费用。例如：
                *   排位赛：可能根据 `activity.totalFee / activity.minPlayersPerGame` 计算。
                *   友谊赛：可能根据 `context.submittedPaymentType` 判断是AA制费用还是整队费用的一半。如果是AA，则初始费用按 `activity.minPlayersPerTeamFriendly` 计算。
                *   联赛：通常是固定的 `activity.leagueFeePerPlayer`。
        *   `void onPaymentSuccess(RegistrationDO registration, ActivityDO activity)`: 在报名记录的支付状态**即将被更新为已支付之前**调用的回调方法。策略可以在此执行一些需要在支付状态确认前完成的特定逻辑。例如，如果支付成功会立即触发某些资源的预占，可以在此进行。大多数情况下，核心逻辑在 `handlePaymentSuccessAfterStateUpdate` 中。
            *   `registration`: 已支付的报名记录 (但其 `status` 和 `paymentStatus` 可能尚未在数据库层面完全更新到最终态)。
            *   `activity`: 当前活动信息。
        *   `void handlePaymentSuccessAfterStateUpdate(ActivityDO activity, RegistrationDO registration)`: 在报名记录的支付状态等相关信息**已成功更新到数据库之后**调用的回调方法。这是处理支付成功后核心业务逻辑的地方。
            *   `activity`: 当前活动信息。
            *   `registration`: 已支付且状态已更新的报名记录。
            *   例如：
                *   排位赛：调用实时分队逻辑 (`teamAssignmentService.assignTeamInRealTime`)，如果是好友组队的发起人，则创建好友组队房间 (`FriendGroupService.createFriendGroup`)。
                *   友谊赛：如果是球队整体支付，则处理该队其他成员的报名状态 (`handleTeamTotalPayment`)。
                *   联赛：更新团队报名统计或状态。
        *   `void checkAndProcessGrouping(@NotNull ActivityDO activity)`: 组局时间到达后检查是否达标
            *   达标：创建比赛/联赛，状态变为"进行中"，仍可报名但不可退赛
            *   不达标：状态变为"已取消"，触发全额退款
        *   `Integer getTeamPaymentType(Long activityId, Long teamId)`: (默认实现返回 null，具体策略可覆盖) 主要用于友谊赛，获取指定球队 (`teamId`) 在特定活动 (`activityId`) 中已经确定的支付方式（例如，该队首个支付成员选择的是整队支付还是AA制）。如果尚未确定，则返回 null。
            *   **注意**: 此方法在 `ActivityStrategy` 接口中提供默认实现，允许特定策略（如 `FriendlyActivityStrategy`）覆盖它以提供具体的查询逻辑。其他不需要此功能的策略可以忽略。
    *   **旧费用计算方法移除**: 原有的 `calculateFee(ActivityDO activity, Long teamId)` 和 `calculateInitialFee(ActivityDO activity, AppRegistrationCreateReqVO createReqVO)` 方法已从接口中移除，其功能被统一的 `calculateEffectiveFee(ActivityDO activity, FeeCalculationContext context)` 方法取代。
    *   **退款计算职责已移除**: `ActivityStrategy` 不再直接负责计算各种场景下的退款金额。退款金额的计算逻辑已移至专门的 `RefundCalculator` 实现类中，并通过 `ActivityRefundService` 进行协调。这遵循了单一职责原则。
*   **`ActivityStrategyFactory`**: 根据活动类型创建/获取对应的 `ActivityStrategy` Bean。
*   **`RegistrationState` (接口及其实现)**: **【状态模式】**
    *   封装报名记录在不同状态下的行为和允许的状态转换：
        *   `handlePaySuccess(RegistrationDO registration, PayNotifyData data)`: 处理支付成功。
        *   `cancel(RegistrationDO registration, Long operatorId)`: 处理取消操作。
            *   **组局前**: 允许取消，候补用户自动补位
            *   **组局后**: 不允许主动退赛
        *   `applyRefund(...)`: 处理退款申请。
        *   `complete(...)`: 处理报名完成。
    *   每个状态实现类内部负责更新 `RegistrationDO` 的状态字段并调用 Mapper 保存。
*   **`RegistrationStateFactory`**: 根据 `RegistrationStatusEnum` 获取对应的 `RegistrationState` Bean。
*   **`ActivityRefundService`**: **退款协调者**。
    *   提供统一的退款入口 `processRefund(Long registrationId, RefundScenario scenario)`。
    *   使用 `RefundCalculatorFactory` 获取特定活动类型的退款计算器。
    *   内部使用 `RefundProcessorFactory` 获取具体场景的处理器。
    *   **不再依赖 `ActivityStrategyFactory`**，遵循单向依赖原则。
*   **`AbstractRefundProcessor` (及其子类)**: **【模板方法模式】**
    *   定义标准退款流程：校验 -> 计算金额 -> 调用支付API -> 更新报名状态 -> 记录日志。
    *   子类实现 `calculateAmount()` 等差异化步骤。**注意**: `DifferenceRefundProcessor` 的 `calculateAmount` 实现需要调用 `ActivityStrategy.calculateDifferenceRefundAmount` 以确保计算逻辑正确。
*   **`RefundProcessorFactory`**: 根据 `RefundScenario` (用户取消、组局失败、差额) 创建/获取对应的 `AbstractRefundProcessor` Bean。
*   **`FriendGroupService` (好友组队相关服务)**:
    *   **职责细化**:
        *   管理好友组队房间 (`sd_friend_group`) 的完整生命周期：
            *   **创建**: 在排位赛中，当发起人选择好友组队并支付成功后，创建好友组记录，状态设为"组队中"，生成唯一的、有时效性的邀请码 (`invite_code`)，并计算 `expires_at` (基于 `sd_activity.friend_group_invite_duration_minutes`)。发起人的报名记录关联到此房间。
            *   **成员加入**: 处理好友通过邀请码加入的请求，校验邀请码有效性、房间是否已满 (`sd_activity.max_players_per_friend_group`)、是否仍在邀请有效期内。成功加入的用户需要独立完成报名和支付流程，其报名记录也关联到同一房间。
            *   **状态更新**: 当房间满员或达到邀请截止时间 (`expires_at`)，更新房间状态为"组队成功"或"已结束"。
            *   **退赛处理**: 当房间成员申请退赛时，直接取消其报名记录，不更新房间关联关系。用户如需重新报名，可创建新房间或加入其他房间。
        *   **重要原则**: 每个报名记录对应一个固定的房间关联，用户想更换房间需通过退赛重新报名的方式实现，不支持房间间的成员迁移。
        *   与 `ActivityStrategy` (特别是 `RankingActivityStrategy`) 协作，提供当前队伍已有的好友组数量等信息，以辅助判断是否还能容纳新的好友组。
*   **事件 (`*Event`) 与监听器 (`*Listener`)**: **【观察者/Spring事件】**
    *   `RegistrationService` 或 `ActivityStrategy` 在关键业务节点（如支付成功、组局成功、退款完成、好友邀请超时）发布事件。
    *   `NotificationListener` 监听事件发送通知（站内信、短信等）。
    *   `StatisticsListener` 监听事件更新统计数据。
    *   **(生产环境说明)**: 当前为单体部署，使用 Spring Event (`@EventListener`)。此机制在单体下简单直接，但默认同步执行，且与业务逻辑耦合较紧。若未来演进为分布式系统或需要更强的解耦、异步能力时，应考虑切换到上述可靠事件模式及消息队列。
    *   **本地事务**: 确保同一模块内涉及多个数据表的操作（如更新 `sd_registration` 和 `sd_friend_group`）封装在同一个本地事务中。
*   **DAL**: Mapper 接口和 DO 定义。

### 3.4 交互流程要点 (结合模式)

#### 3.4.1 报名支付流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as AppRegistrationController
    participant RegService as RegistrationService
    participant Strategy as ActivityStrategy
    participant Discount as DiscountService
    participant State as RegistrationState
    participant PayAPI as PayOrderApi
    participant EventPub as ApplicationEventPublisher

    User->>Controller: 提交报名请求
    Controller->>RegService: createRegistration()
    
    RegService->>Strategy: validateRegistration()
    RegService->>Strategy: calculateFee()
    
    RegService->>RegService: 创建RegistrationDO
    Note over RegService: 初始状态PendingPaymentState
    
    RegService->>Discount: applyAndFinalizeDiscounts()
    Note over Discount: 责任链计算优惠<br/>执行优惠核销
    
    RegService->>PayAPI: createOrder()
    PayAPI-->>User: 支付订单创建成功
    
    Note over User: 用户完成支付
    
    User->>Controller: 支付回调通知
    Controller->>RegService: handlePaySuccess()
    
    RegService->>State: handlePaySuccess()
    Note over State: PendingPaymentState → PaidState
    
    State->>Strategy: handlePaymentSuccessAfterStateUpdate()
    
    alt 排位赛
        Strategy->>Strategy: 执行实时分队算法
        Strategy->>Strategy: 处理好友组队(如适用)
    else 友谊赛
        Strategy->>Strategy: 处理球队支付逻辑
    else 联赛
        Strategy->>Strategy: 更新联赛报名状态
    end
    
    Strategy-->>RegService: 业务处理完成
    RegService->>EventPub: 发布RegistrationPaidEvent
```

#### 3.4.2 退款差额流程

```mermaid
sequenceDiagram
    participant Job as ActivityGroupingJob
    participant RegService as RegistrationService
    participant Strategy as ActivityStrategy
    participant RefundService as ActivityRefundService
    participant Processor as RefundProcessor
    participant PayAPI as PayRefundApi
    participant NotifyCtrl as OperationPayNotifyController

    Job->>RegService: checkAndProcessGrouping()
    RegService->>Strategy: checkAndProcessGrouping()
    
    Strategy->>Strategy: 检查组局条件
    
    alt 需要差额退款
        Strategy->>RefundService: processRefund(DIFFERENCE)
        RefundService->>Processor: 获取DifferenceRefundProcessor
        
        Processor->>Processor: calculateAmount()
        Note over Processor: 模板方法执行
        
        Processor->>PayAPI: createRefund()
        PayAPI-->>Processor: 退款订单创建
        
        Note over PayAPI: 支付中心处理退款
        
        PayAPI->>NotifyCtrl: 退款回调通知
        NotifyCtrl->>RefundService: handleRefundResult()
        RefundService->>RefundService: 更新退款状态
    end
```

## 4. 核心业务场景梳理

本节旨在简要梳理三种核心活动类型的业务逻辑，为理解后续设计提供上下文。
**(注意: 以下描述的活动实例，既可以手动创建，也可以基于预定义的 `sd_activity_template` 手动或自动创建。)**

### 4.1 排位赛 (Ranking - `RankingActivityStrategy`)

*   **目标**: 允许球员个人或与少量好友（最多由`sd_activity.max_players_per_friend_group`定义，通常为3人）灵活报名参与比赛，系统自动完成分队，确保比赛公平性。
*   **主队客队配置**: 排位赛活动需要预先配置主队和客队信息，这些信息用于：
    *   **模板创建**: 在`sd_activity_template`中可以设置默认的主队客队信息(`default_home_team_id`, `default_guest_team_id`)。
    *   **活动实例创建**: 基于模板创建活动时，管理员需要选择或确认具体的主队和客队，这些信息存储在`sd_activity`的相关字段中(`home_team_id`, `guest_team_id`)。
    *   **前端展示**: 在活动详情页(`detail.vue`)中通过teamId查询球队详细信息，展示主队客队的球服颜色和队名，帮助用户了解比赛对阵信息。
    *   **分队分配**: 报名成功后，系统的分队算法会将球员分配到主队或客队，分配结果记录在`sd_registration.team_assigned`字段中，直接存储具体的球队ID (关联 sd_team.id)，方便前端直接查询球队详细信息。
*   **报名规则**:
    *   **人数限制**: 最低16人(`min_players_per_game`)，最高20人(`max_players_per_game`)方可组局成功。
    *   **候补机制**: 当报名人数达到上限(20人)后，新报名用户进入候补队列(`is_waitlist=1`)，系统记录候补位置(`waitlist_position`)。当有已报名球员退赛时，系统按候补顺序自动补位。
    *   **实时分队**: 用户报名并支付成功后，系统根据球员战力值(`sd_player.ratings`)、身高、位置等信息，实时分配到主队或客队，确保两队整体实力均衡。
    *   **报名时间**: 只要活动状态为"报名中"，用户即可报名，不受报名开始时间限制。
*   **报名方式**: 支持两种方式（共用同一报名入口，仅报名方式不同）：
    *   **个人报名**: 用户直接报名并支付，系统根据平衡算法将用户分配到合适的队伍。
    *   **好友组队报名**: 用户在报名界面选择"好友组队报名"，发起人报名支付后自动创建好友组并成为队长（由`FriendGroupService`处理，生成`sd_friend_group`记录和`invite_code`），生成邀请码分享给好友。好友通过邀请码或邀请链接访问活动页面，然后独立完成报名和支付流程，其报名记录也关联到同一好友组房间。系统会尽量将好友组成员分配到同一队伍（但会**严格优先保证整体队伍的平衡性，如果冲突，则可能按最小化原则拆分好友组，具体拆分逻辑需符合 `排位赛分队模型需求文档.md`**）。**重要**: 每个报名记录都有固定的房间关联，如果用户想更换房间，需要先申请退赛（取消当前报名），然后重新报名。好友组邀请有固定时效（例如 `sd_activity.friend_group_invite_duration_minutes`，默认为30分钟），由 `FriendGroupTimeoutJob` 处理超时。
*   **支付**: 
    *   按人收费，初始支付金额基于最低人数计算: `total_fee / min_players_per_game`。
    *   实际费用根据最终组局成功人数计算，多退少补。
*   **组局 (`ActivityGroupingJob`)**: 
    *   组局时间到达后，检查是否达到最低要求(`min_players_per_game`)
    *   若达到则组局成功，创建`sd_game`记录，活动状态变为"进行中"，仍可继续报名但不可退赛
    *   若未达到则组局失败，活动状态变为"已取消"，自动全额退款
*   **退款场景**: 
    *   **组局失败**: 全额退款
    *   **差额退款**: 组局成功后，根据最终人数计算差额退款
    *   **用户取消**: 仅在组局前允许，组局后不可退赛

### 4.1.1 好友组队业务流程详解 (重要设计原则)

好友组队是排位赛的特色功能，允许用户与朋友组队参赛。**核心设计原则：每个报名记录对应一个固定的房间关联，用户如需更换房间应通过退赛重新报名实现。**

#### 创建好友组
1. **触发条件**: 用户选择"好友组队报名"并支付成功
2. **执行逻辑**: `RankingActivityStrategy.handlePaymentSuccessAfterStateUpdate()` 调用 `FriendGroupService.createFriendGroup()`
3. **创建结果**: 
   - 直接生成新的 `sd_friend_group` 记录，状态为"组队中"
   - 生成唯一邀请码和过期时间
   - 更新报名记录的 `friend_group_id` 和 `is_group_leader` 标志
4. **重要**: 不检查或复用已有好友组，每次都创建新房间

#### 邀请好友
1. **邀请方式**: 
   - 前端调用 `generateInvitation()` 获取邀请链接
   - 通过微信、短信等方式分享邀请链接
2. **邀请链接格式**: `https://app.saidian.com/activity/detail?activityId=123&inviteCode=ABC123`

#### 好友加入
1. **访问链接**: 好友点击邀请链接，前端解析 `inviteCode` 参数
2. **展示信息**: 显示活动详情和好友组信息（发起人、当前成员、剩余名额）
3. **加入流程**: 好友点击"加入"，跳转到报名页面，自动填充好友组信息
4. **独立报名**: 好友需要完成完整的报名和支付流程
5. **关联房间**: 支付成功后，报名记录关联到同一好友组

#### 分队处理
1. **实时分队**: 每位成员支付成功后，立即执行分队算法
2. **好友组约束**: 算法会尽量将好友组成员分配到同一队伍
3. **平衡优先**: 如果影响整体平衡，可能拆分好友组
4. **最终调整**: 报名截止后的最终分队阶段会再次优化

#### 退赛处理 (关键逻辑)
1. **退赛方式**: 用户申请退赛，取消当前报名记录并清理房间关联
2. **房间关联清理**:
   - **队长退赛**: 解散整个好友组，清理所有成员的房间关联
   - **普通成员退赛**: 只清理该成员的房间关联（`friend_group_id` 设为 null）
   - **名额释放**: 确保退赛用户不再占用房间名额
3. **重新报名**: 用户如需重新报名，执行全新的报名流程：
   - 可以选择个人报名
   - 可以创建新的好友组房间
   - 可以通过其他邀请码加入其他房间
4. **数据一致性**: 
   - 房间成员数量统计准确
   - 已退赛用户不占用房间名额
   - 房间可以正常满员和邀请新成员

### 4.2 友谊赛 (Friendly - `FriendlyActivityStrategy`)

*   **目标**: 支持已有球队或临时组队进行对抗比赛，满足团队竞技需求。
*   **报名规则**:
    *   **球队报名**: 必须以球队为单位报名，需要`team_id`。
    *   **人数限制**: 每队最少5人(`min_players_per_team_friendly`)，最多15人(`max_players_per_team_friendly`)。
    *   **首位报名者权限**: 该球队第一个报名并支付成功的成员可以设置：
        *   **球服颜色**: 选择本场比赛使用的球服颜色，存储在`sd_registration.jersey_color`。
        *   **人数上下限**: 设置本队的报名人数范围(5-15人内)。
        *   **支付方式**: 选择整体支付(1)或AA支付(2)，存储在`sd_registration.payment_type`。
    *   **报名时间**: 只要活动状态为"报名中"，球队即可报名，不受报名开始时间限制。
*   **支付方式**: 
    *   **球队整体支付**: 首个报名者支付该队全部费用(`total_fee / 2`)，其他队员无需支付。
    *   **球员个人支付(AA)**: 
        *   每个报名成员支付个人分摊费用。
        *   初始分摊额基于最低人数计算: `(total_fee / 2) / min_players_per_team_friendly`。
        *   实际费用根据最终报名人数重新计算，多退少补。
*   **组局 (`ActivityGroupingJob`)**: 
    *   组局时间到达后，检查是否有至少2支球队且每队人数达标
    *   满足条件则组局成功，创建`sd_game`记录，活动状态变为"进行中"
    *   否则组局失败，活动状态变为"已取消"，全额退款
*   **退款场景**: 
    *   **组局失败**: 全额退款
    *   **差额退款**: 仅AA支付模式，根据最终人数计算差额
    *   **用户取消**: 仅在组局前允许

### 4.3 联赛 (League - `LeagueActivityStrategy`)

*   **目标**: 支持多支球队参与的结构化、多轮次赛事，提供专业的竞技体验。
*   **报名规则**:
    *   **球队报名**: 必须以球队为单位报名，需要`team_id`。
    *   **人数限制**: 每队最低人数由`min_players_per_team_league`定义。
    *   **球队数量**: 最低需要`min_teams_league`支球队才能组局成功。
    *   **报名时间**: 只要活动状态为"报名中"，球队即可报名，不受报名开始时间限制。
*   **支付**: 
    *   按人头收取固定报名费(`league_fee_per_player`)。
    *   每位球员各自支付，无整体支付选项。
*   **组局 (`ActivityGroupingJob`)**: 
    *   组局时间到达后，检查球队数量是否达到最低要求
    *   若达到则组局成功，创建`sd_league`和赛程，活动状态变为"进行中"
    *   否则组局失败，活动状态变为"已取消"，全额退款
*   **退款场景**: 
    *   **组局失败**: 全额退款
    *   **用户取消**: 仅在组局前允许
    *   联赛无差额退款(固定费用)

### 4.4 关键字段使用说明

本节阐述核心表中的关键字段如何组合应用于不同活动类型：

#### 通用字段
*   `sd_activity.type`: 区分活动类型，决定使用哪个 `ActivityStrategy`。
*   `sd_activity.registration_deadline`: 组局判断和用户退款的主要时间依据。
*   `sd_activity.status`: 活动的整体状态流转。
*   `sd_registration.activity_id`: 关联到具体的活动。
*   `sd_registration.user_id`, `player_id`: 标识报名参与者。
*   `sd_registration.status`: 报名记录的核心状态，由 `RegistrationState` 管理。
*   `sd_registration.payment_status`, `pay_order_id`, `pay_time`: 支付相关信息。
*   `sd_registration.refund_status`, `total_refund_price`: 退款相关信息。

#### 排位赛 (ActivityTypeEnum.RANKING)
*   **活动配置**:
    *   `sd_activity.total_fee`: 比赛总费用。
    *   `sd_activity.min_players_per_game`, `max_players_per_game`: 人数限制(通常16-20人)。
    *   `sd_activity.friend_group_supported`, `max_players_per_friend_group`: 好友组队配置。
    *   `sd_activity.home_team_*`, `away_team_*`: 主客队信息。
*   **报名记录**:
    *   `sd_registration.should_pay_price`: 初始为人均费用(`total_fee / min_players_per_game`)。
    *   `sd_registration.friend_group_id`, `is_group_leader`: 好友组队信息。
    *   `sd_registration.team_assigned`: 分配到的具体球队ID (关联 sd_team.id)，而非1/2标识。
    *   `sd_registration.is_waitlist`, `waitlist_position`: 候补状态和位置。

#### 友谊赛 (ActivityTypeEnum.FRIENDLY)
*   **活动配置**:
    *   `sd_activity.total_fee`: 比赛总费用。
    *   `sd_activity.min_teams_friendly`: 最低2支球队。
    *   `sd_activity.min_players_per_team_friendly`, `max_players_per_team_friendly`: 每队人数限制(5-15人)。
*   **报名记录**:
    *   `sd_registration.team_id`: **必需字段**，标识所属球队。
    *   `sd_registration.payment_type`: 支付方式(1-整体支付, 2-AA支付)。
    *   `sd_registration.jersey_color`: 球队选择的球服颜色。
    *   `sd_registration.should_pay_price`: 根据支付方式计算。

#### 联赛 (ActivityTypeEnum.LEAGUE)
*   **活动配置**:
    *   `sd_activity.league_fee_per_player`: 固定的个人报名费。
    *   `sd_activity.min_teams_league`: 最低球队数量要求。
    *   `sd_activity.min_players_per_team_league`: 每队最低人数要求。
*   **报名记录**:
    *   `sd_registration.team_id`: **必需字段**，标识所属球队。
    *   `sd_registration.should_pay_price`: 固定为`league_fee_per_player`。
    *   `sd_registration.payment_type`: 不使用，联赛只支持个人支付。

## 5. 系统架构和流程图

### 5.1 核心架构图


```mermaid
graph TB
    subgraph "External Dependencies"
        PayModule[yudao-module-pay<br/>PayOrderApi<br/>PayRefundApi]
        CouponModule[yudao-module-promotion<br/>CouponApi]
        MemberModule[yudao-module-member<br/>MemberUserApi Points]
    end

    subgraph "saidian-module-operation"
        subgraph "Controller Layer"
            AppControllers[App Controllers<br/>- Registration<br/>- Activity<br/>- FriendGroup]
            NotifyControllers[Notify Controllers<br/>- PayNotifyController<br/>- RefundNotifyController]
        end

        subgraph "Service Layer"
            RegService[RegistrationService<br/>核心协调者]
            ActivityStrategy[ActivityStrategy<br/>策略模式<br/>- RankingStrategy<br/>- FriendlyStrategy<br/>- LeagueStrategy]
            DiscountService[DiscountService<br/>责任链模式<br/>- CouponHandler<br/>- PointsHandler]
            
            RegState[RegistrationState<br/>状态模式<br/>- PendingPayment<br/>- Paid<br/>- Successful]
            RefundService[ActivityRefundService<br/>退款协调者]
            
            TeamService[TeamAssignmentService<br/>分队算法核心<br/>- 实时分队<br/>- 最终平衡<br/>- 好友组处理]
            FriendGroupService[FriendGroupService<br/>好友组队管理]
            
            EventPublishers[Event Publishers<br/>- Registration<br/>- Activity<br/>- FriendGroup]
        end

        subgraph "Event & MQ Layer"
            EventDefs[Event Definitions<br/>按模块分散<br/>- registration/event/<br/>- activity/event/<br/>- friendgroup/event/]
            
            subgraph "MQ Consumers"
                RegConsumers[Registration Consumers<br/>- PaidConsumer<br/>- NotificationConsumer]
                ActivityConsumers[Activity Consumers<br/>- GroupingConsumer<br/>- NotificationConsumer]
                NotifyConsumers[Notification Consumers<br/>- SmsConsumer<br/>- EmailConsumer<br/>- PushConsumer]
            end
        end

        subgraph "Job Layer"
            GroupingJob[ActivityGroupingJob<br/>组局定时任务]
            TimeoutJob[FriendGroupTimeoutJob<br/>好友组队超时处理]
            AutoCreateJob[ActivityAutoCreationJob<br/>活动自动创建]
        end
    end

    %% 连接关系
    AppControllers --> RegService
    NotifyControllers --> RegService
    NotifyControllers --> RefundService
    
    RegService --> ActivityStrategy
    RegService --> DiscountService
    RegService --> RegState
    RegService --> TeamService
    RegService --> FriendGroupService
    RegService --> EventPublishers
    
    ActivityStrategy --> TeamService
    RefundService --> EventPublishers
    
    EventPublishers --> EventDefs
    EventDefs --> RegConsumers
    EventDefs --> ActivityConsumers
    EventDefs --> NotifyConsumers
    
    GroupingJob --> ActivityStrategy
    TimeoutJob --> FriendGroupService
```

### 5.2 支付回调完整流程图

```mermaid
sequenceDiagram
    actor 用户
    participant PaySDK as "支付SDK"
    participant PayAPI as "支付模块"
    participant NotifyCtrl as "OperationPayNotifyController"
    participant RegSvc as "RegistrationService"
    participant Strategy as "ActivityStrategy"
    participant State as "RegistrationState"
    participant EventPub as "ApplicationEventPublisher"
    participant MQConsumer as "RegistrationPaidConsumer"
    participant NotifySvc as "NotificationService"

    用户->>PaySDK: 1. 用户完成支付
    PaySDK->>PayAPI: 2. 支付成功通知
    PayAPI->>NotifyCtrl: 3. POST /operation/notify/pay/order-success
    
    Note over NotifyCtrl: 统一回调处理入口
    NotifyCtrl->>RegSvc: 4. handlePaySuccess(notifyData)
    
    Note over RegSvc,State: 状态模式处理支付成功
    RegSvc->>State: 5. getState(PENDING_PAYMENT)
    State->>State: 6. handlePaySuccess() - 更新状态到PAID
    State->>RegSvc: 7. 状态更新完成
    
    Note over RegSvc,Strategy: 策略模式处理后续业务
    RegSvc->>Strategy: 8. handlePaymentSuccessAfterStateUpdate()
    
    alt 排位赛
        Strategy->>Strategy: 8a. 实时分队算法
        Strategy->>Strategy: 8b. 好友组队处理（如果适用）
        Strategy->>Strategy: 8c. 候补队列处理（如果适用）
    else 友谊赛
        Strategy->>Strategy: 8d. 球队支付处理
        Strategy->>Strategy: 8e. AA支付逻辑
    else 联赛
        Strategy->>Strategy: 8f. 联赛报名处理
    end
    
    Strategy-->>RegSvc: 9. 业务处理完成
    
    Note over RegSvc,EventPub: 发布支付成功事件
    RegSvc->>EventPub: 10. publishEvent(RegistrationPaidEvent)
    EventPub-->>MQConsumer: 11. 事件异步消费
    
    Note over MQConsumer,NotifySvc: 异步处理后续业务
    MQConsumer->>NotifySvc: 12. 发送支付成功通知
    MQConsumer->>MQConsumer: 13. 更新统计数据
    MQConsumer->>MQConsumer: 14. 其他副作用处理
    
    RegSvc-->>NotifyCtrl: 15. 处理完成
    NotifyCtrl-->>PayAPI: 16. success(true)
    PayAPI-->>PaySDK: 17. 回调确认
```

### 5.3 退款回调完整流程图

```mermaid
sequenceDiagram
    actor Trigger as "触发源"
    participant RefundSvc as "ActivityRefundService"
    participant PayAPI as "支付模块"
    participant NotifyCtrl as "OperationRefundNotifyController"
    participant Processor as "RefundProcessor"
    participant EventPub as "ApplicationEventPublisher"
    participant MQConsumer as "ActivityRefundConsumer"
    participant CouponAPI as "优惠券模块"
    participant PointsAPI as "积分模块"

    Note over Trigger,RefundSvc: 退款发起阶段
    Trigger->>RefundSvc: 1. processRefund(registrationId, scenario)
    RefundSvc->>Processor: 2. 选择对应的RefundProcessor
    Processor->>Processor: 3. 计算退款金额
    Processor->>PayAPI: 4. createRefundOrder()
    PayAPI-->>Processor: 5. 退款订单创建成功
    
    Note over PayAPI,NotifyCtrl: 支付平台处理退款
    PayAPI->>PayAPI: 6. 处理退款（可能耗时）
    PayAPI->>NotifyCtrl: 7. POST /operation/notify/refund/refund-success
    
    Note over NotifyCtrl,RefundSvc: 退款回调处理
    NotifyCtrl->>RefundSvc: 8. handleRefundSuccess(notifyData)
    RefundSvc->>RefundSvc: 9. 更新退款状态
    RefundSvc->>RefundSvc: 10. 更新报名记录状态
    
    Note over RefundSvc,EventPub: 处理优惠券和积分退还
    RefundSvc->>CouponAPI: 11. 退还优惠券（如果使用）
    RefundSvc->>PointsAPI: 12. 退还积分（如果使用）
    
    Note over RefundSvc,EventPub: 发布退款成功事件
    RefundSvc->>EventPub: 13. publishEvent(RefundSuccessEvent)
    EventPub-->>MQConsumer: 14. 事件异步消费
    
    Note over MQConsumer: 异步处理后续业务
    MQConsumer->>MQConsumer: 15. 发送退款成功通知
    MQConsumer->>MQConsumer: 16. 更新统计数据
    MQConsumer->>MQConsumer: 17. 处理候补队列（如果是取消报名）
    
    RefundSvc-->>NotifyCtrl: 18. 处理完成
    NotifyCtrl-->>PayAPI: 19. success(true)
```

### 5.4 组局定时任务流程图

```mermaid
sequenceDiagram
    participant Job as "ActivityGroupingJob"
    participant ActivitySvc as "ActivityService"
    participant Strategy as "ActivityStrategy"
    participant RegSvc as "RegistrationService"
    participant RefundSvc as "ActivityRefundService"
    participant EventPub as "ApplicationEventPublisher"
    participant MQConsumer as "ActivityGroupingConsumer"

    Note over Job: 定时检查报名截止的活动
    Job->>ActivitySvc: 1. 查询需要组局的活动
    ActivitySvc-->>Job: 2. 返回活动列表
    
    loop 处理每个活动
        Job->>Strategy: 3. checkAndProcessGrouping(activity)
        
        Note over Strategy: 检查组局条件
        Strategy->>Strategy: 4. 检查报名人数/队伍数
        
        alt 组局成功
            Note over Strategy: 分队/创建比赛
            alt 排位赛
                Strategy->>Strategy: 4a. 执行最终分队算法
                Strategy->>Strategy: 4b. 处理好友组队拆分/合并
                Strategy->>Strategy: 4c. 创建sd_game记录
            else 友谊赛
                Strategy->>Strategy: 4d. 检查球队报名情况
                Strategy->>Strategy: 4e. 创建sd_game记录
            else 联赛
                Strategy->>Strategy: 4f. 创建sd_league记录
                Strategy->>Strategy: 4g. 生成赛程结构
            end
            
            Strategy->>ActivitySvc: 5a. 更新活动状态为GROUPING_SUCCESSFUL
            Strategy->>RegSvc: 6a. 更新报名记录状态为SUCCESSFUL
            
            Note over Strategy,EventPub: 处理差额退款
            Strategy->>RefundSvc: 7a. 处理差额退款（如果需要）
            
            Note over Strategy,EventPub: 发布组局成功事件
            Strategy->>EventPub: 8a. publishEvent(ActivityGroupingSuccessEvent)
            
        else 组局失败
            Strategy->>ActivitySvc: 5b. 更新活动状态为GROUPING_FAILED
            Strategy->>RegSvc: 6b. 更新报名记录状态为GROUPING_FAILED
            
            Note over Strategy,RefundSvc: 全额退款
            Strategy->>RefundSvc: 7b. 处理全额退款
            
            Note over Strategy,EventPub: 发布组局失败事件
            Strategy->>EventPub: 8b. publishEvent(ActivityGroupingFailedEvent)
        end
        
        Note over EventPub,MQConsumer: 异步处理后续业务
        EventPub-->>MQConsumer: 9. 事件异步消费
        MQConsumer->>MQConsumer: 10. 发送组局结果通知
        MQConsumer->>MQConsumer: 11. 更新统计数据
        MQConsumer->>MQConsumer: 12. 其他业务处理
    end
```

### 5.5 事件驱动架构图

```mermaid
graph TD
    A[业务服务层] -->|发布事件| B[ApplicationEventPublisher]
    B --> C[Spring Event Bus]
    
    C --> D[Registration Events]
    C --> E[Activity Events]
    C --> F[FriendGroup Events]
    
    D --> D1[RegistrationPaidConsumer]
    D --> D2[RegistrationCancelledConsumer]
    D --> D3[RegistrationNotificationConsumer]
    
    E --> E1[ActivityGroupingConsumer]
    E --> E2[ActivityNotificationConsumer]
    E --> E3[ActivityStatisticsConsumer]
    
    F --> F1[FriendGroupNotificationConsumer]
    F --> F2[FriendGroupTimeoutConsumer]
    
    D1 --> G1[通知服务]
    D1 --> G2[统计服务]
    D1 --> G3[分队服务]
    
    E1 --> H1[比赛创建]
    E1 --> H2[通知发送]
    E1 --> H3[数据统计]
    
    F1 --> I1[好友通知]
    F1 --> I2[组队状态更新]
    
    G1 --> J[短信/邮件/推送]
    G2 --> K[数据仓库]
    H2 --> J
    I1 --> J
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#fce4ec
```

### 5.6 关键交互时序说明

#### 5.6.1 支付成功后的关键处理节点

```mermaid
graph LR
    A[支付回调接收] --> B[状态转换处理]
    B --> C[业务逻辑执行]
    C --> D[事件发布]
    D --> E[异步副作用处理]
    
    A1[OperationPayNotifyController<br/>统一处理支付回调] 
    B1[RegistrationState<br/>处理状态转换]
    C1[ActivityStrategy<br/>执行特定活动类型逻辑]
    D1[发布RegistrationPaidEvent]
    E1[MQ消费者处理<br/>通知、统计等副作用]
    
    A --- A1
    B --- B1
    C --- C1
    D --- D1
    E --- E1
    
    style A fill:#e3f2fd
    style C fill:#e8f5e8
    style E fill:#fff3e0
```

#### 5.6.2 组局处理的关键节点

```mermaid
graph TD
    A[定时触发] --> B[策略处理]
    B --> C[分队算法]
    C --> D[状态更新]
    D --> E[退款处理]
    E --> F[事件发布]
    
    A1[ActivityGroupingJob<br/>检查报名截止的活动]
    B1[ActivityStrategy<br/>根据活动类型执行组局逻辑]
    C1[排位赛执行复杂的<br/>分队平衡算法]
    D1[批量更新活动和<br/>报名记录状态]
    E1[差额退款或全额退款]
    F1[组局成功/失败事件]
    
    A -.-> A1
    B -.-> B1
    C -.-> C1
    D -.-> D1
    E -.-> E1
    F -.-> F1
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style E fill:#ffebee
    style F fill:#e8f5e8
```

#### 5.6.3 事件处理的核心特点

```mermaid
graph TB
    subgraph "事件发布模式"
        A[按模块分散]
        A --> A1[registration/event/]
        A --> A2[activity/event/]
        A --> A3[friendgroup/event/]
    end
    
    subgraph "消费处理模式"
        B[消费者集中]
        B --> B1[mq包统一管理]
        B --> B2[按功能模块分组]
    end
    
    subgraph "处理特性"
        C[异步解耦]
        D[幂等处理]
        E[错误重试]
        
        C --> C1[核心业务与副作用解耦]
        D --> D1[所有消费者支持重复消费]
        E --> E1[失败处理和重试机制]
    end
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
```

### 5.7 分队算法详细流程图

```mermaid
sequenceDiagram
    actor 用户 as "报名用户"
    participant Strategy as "RankingActivityStrategy"
    participant TeamSvc as "TeamAssignmentService"
    participant Algorithm as "TeamBalanceAlgorithm"
    participant BalanceStrategy as "BalanceStrategy"
    participant EventPub as "ApplicationEventPublisher"

    Note over 用户,EventPub: 实时分队流程 (用户支付成功后)
    用户->>Strategy: 支付成功回调
    Strategy->>TeamSvc: assignTeamInRealTime(activityId, playerId)
    
    TeamSvc->>Algorithm: 选择 RealtimeTeamAssignmentAlgorithm
    Algorithm->>BalanceStrategy: 评估当前队伍平衡状态
    BalanceStrategy-->>Algorithm: 返回平衡评分
    
    alt 需要分配到主队
        Algorithm->>Algorithm: 分配到主队 (team_assigned = 1)
    else 需要分配到客队
        Algorithm->>Algorithm: 分配到客队 (team_assigned = 2)
    end
    
    Algorithm-->>TeamSvc: 返回分队结果
    TeamSvc->>TeamSvc: 更新 sd_registration.team_assigned
    TeamSvc->>EventPub: 发布 PlayerTeamAssignedEvent
```


## 8. 进一步考虑与优化建议

### 8.6 联赛复杂度考量 (新增)

*   **赛程生成与扩展**: 联赛的赛程生成逻辑相对复杂且需要支持未来不同赛制的扩展。引入独立的 `LeagueScheduleService` (内部可再用策略模式) 是为了隔离这部分复杂性。
*   **排名计算**: 联赛排名计算 (`LeagueRankingService`) 涉及读取大量比赛结果和积分规则，可能需要独立的优化和维护。
*   **数据库模型**: 引入了新的联赛相关表 (`sd_league`, `sd_league_team`, `sd_league_stage`, `sd_league_game_node`, `sd_league_standing`) 来专门存储联赛结构、赛程、排名等信息，以支持复杂赛制和动态对阵。`sd_game` 表通过 `league_game_node_id` 与赛程节点关联。

### 8.10 实际实现中的额外定时任务 (补充)

除了设计文档中明确的定时任务外，实际实现中还包含以下任务以确保系统健壮性：

#### 8.10.1 RegistrationTimeoutJob (报名超时处理)
- **触发条件**: 定期检查超时的待支付报名记录
- **处理逻辑**: 
  - 检查创建时间超过配置阈值的待支付报名
  - 查询对应的支付订单状态
  - 如果支付订单不存在或未支付，自动取消报名
- **重要性**: 防止长期占用报名名额，避免影响其他用户报名
- **实现位置**: `cn.iocoder.yudao.module.operation.job.RegistrationTimeoutJob`

#### 8.10.2 ActivityRefundCompletionJob (退款完成检查)
- **触发条件**: 定期检查处于退款中状态的活动
- **处理逻辑**:
  - 检查活动状态为 `REFUNDING` 的记录
  - 查询该活动下所有报名记录的退款状态
  - 如果所有退款都已完成，将活动状态更新为 `CANCELLED`
- **重要性**: 确保活动状态与实际退款进度保持一致
- **实现位置**: `cn.iocoder.yudao.module.operation.job.ActivityRefundCompletionJob`

#### 8.10.3 定时任务协调机制
```java
// 定时任务执行顺序建议
1. RegistrationTimeoutJob (每5分钟) - 清理超时报名
2. ActivityGroupingJob (每分钟) - 组局检查  
3. FriendGroupTimeoutJob (每10分钟) - 好友组超时
4. ActivityRefundCompletionJob (每30分钟) - 退款完成检查
```

这些额外的定时任务虽然在初始设计中未明确提及，但对于保证系统的数据一致性和用户体验至关重要。

## 9. 总结与优势分析


排位赛分队算法需要在多个维度之间实现平衡：能力评分、身高、好友组队、胜率、场上位置。

**核心要求**：
- 最终分队结果，两队人数差值需等于0（偶数上限）
- 可进行全面调整以达到最佳平衡
- 退赛后检查S级和A级球员平衡，必要时进行最小化调整
- 候补队列自动补位机制

**实现架构**：详细的分队算法设计请参考独立文档：`排位赛分队算法设计.md`

*   **`TeamAssignmentService` (分队算法服务)**: **【算法核心协调者】**
    *   **职责**: 负责排位赛的分队算法协调和管理，是分队逻辑的统一入口。
    *   **核心设计原则**: 五维度平衡、阶段化策略、最小化调整、好友组处理。
    *   **主要方法**: 实时分队、最终平衡、退赛重分配、好友组处理等。
*   **`TeamBalanceAlgorithm` (分队算法接口及其实现)**: **【策略模式 + 算法族】**
    *   **核心算法**:
        *   `RealtimeTeamAssignmentAlgorithm`: 实时分队算法，用户支付成功后即时分队
        *   `FinalTeamBalanceAlgorithm`: 最终分队平衡算法，报名截止后的最优平衡
        *   `PlayerWithdrawalRebalanceAlgorithm`: 退赛重平衡算法，最小化调整原则
*   **`BalanceStrategy` (平衡策略接口及其实现)**: **【策略模式 + 组合模式】**
    *   **设计理念**: 将分队算法分解为多个独立的平衡策略，每个策略负责一个维度的平衡。
    *   **策略实现**:
        *   `AbilityBalanceStrategy`: **能力值平衡策略**
            *   计算两队的能力值总和、平均值、方差。
            *   提供能力值平衡度评分和优化建议。
        *   `HeightBalanceStrategy`: **身高平衡策略**
            *   确保两队的平均身高相近。
            *   避免某队过于高大或矮小。
        *   `PositionBalanceStrategy`: **位置平衡策略**
            *   确保每队都有合理的位置分布（控卫、前锋、中锋等）。
            *   避免某队位置过于单一。
        *   `FriendGroupBalanceStrategy`: **好友组平衡策略**
            *   处理好友组的分配约束。
            *   在保证整体平衡的前提下，尽量满足好友同队需求。
            *   提供好友组拆分的决策逻辑。
    *   **策略组合**: `BalanceStrategyFactory` 根据配置组合多个策略，形成综合的平衡评估。
*   **分队算法数据模型**:
    *   `TeamAssignmentContext`: **分队上下文**
        *   包含活动信息、所有报名球员数据、当前分队状态、好友组信息等。
        *   提供算法执行所需的完整数据环境。
    *   `PlayerTeamInfo`: **球员分队信息**
        *   包含球员基本信息（能力值、身高、位置等）和分队相关信息。
        *   支持球员数据的快速检索和比较。
    *   `TeamBalanceResult`: **分队结果**
        *   包含分队后的队伍分配、平衡度评分、好友组处理结果等。
        *   提供详细的分队质量评估报告。
    *   `FriendGroupConstraint`: **好友组约束**
        *   定义好友组的分队约束条件。
        *   支持好友组优先级和拆分规则的配置。
    *   `TeamStatistics`: **队伍统计信息**
        *   实时维护两队的各项统计指标。
        *   支持快速的平衡度计算和比较。
*   **与其他组件的协作**:
    *   **`RankingActivityStrategy`**: 在支付成功和组局时调用分队服务。
    *   **`FriendGroupService`**: 提供好友组信息，接收好友组处理结果。
    *   **`RegistrationService`**: 在状态变更时触发分队重新评估。
    *   **事件发布**: 分队变更时发布相应事件，通知相关系统更新。

#### 6.1.3 全面活动业务流程图 - 开发指导

以下流程图强调**统一页面架构**和**分队展示用户体验**，确保技术实现与前端设计呼应：

```mermaid
graph TB
    Start([用户访问活动详情页<br/>detail.vue]) --> CheckInvite{是否带邀请码?}
    
    %% 邀请码流程
    CheckInvite -->|是| GetActivityWithInvite[获取活动详情+好友组信息]
    GetActivityWithInvite --> ShowInviteDialog[显示加入房间弹窗]
    ShowInviteDialog --> UserChoice{用户选择}
    UserChoice -->|立即加入| DirectToUnifiedOrder[跳转统一订单页<br/>confirm-order.vue]
    UserChoice -->|关闭弹窗| ShowUnifiedActivity[统一活动详情页展示]
    
    %% 普通访问流程
    CheckInvite -->|否| GetActivity[获取活动详情]
    GetActivity --> ShowUnifiedActivity
    
    %% 统一页面的差异化展示（重要设计）
    ShowUnifiedActivity --> UnifiedActivityPage[统一活动详情页<br/>detail.vue]
    UnifiedActivityPage --> ConditionalRender{基于activityType<br/>条件渲染不同内容}
    
    %% 排位赛展示（条件渲染）
    ConditionalRender -->|type=RANKING_MATCH| RankingSection["v-if排位赛展示：<br/>• 倒计时卡片<br/>• 比赛信息区块<br/>• 报名方式选择<br/>• 费用明细（个人）<br/>• 主客队进度显示<br/>• 服务明细<br/>• 报名须知"]
    
    %% 友谊赛展示（条件渲染）
    ConditionalRender -->|type=FRIENDLY_MATCH| FriendlySection["v-if友谊赛展示：<br/>• 倒计时卡片<br/>• 比赛信息区块<br/>• 仅球队报名<br/>• 费用明细（球队）<br/>• 球队报名进度<br/>• 服务明细<br/>• 报名须知"]
    
    %% 联赛展示（条件渲染）
    ConditionalRender -->|type=LEAGUE| LeagueSection["v-if联赛展示：<br/>• 联赛信息卡片<br/>• 赛制说明<br/>• 仅球队报名<br/>• 费用明细（每人）<br/>• 联赛参赛进度<br/>• 联赛规则说明<br/>• 报名须知"]
    
    %% 用户状态检查
    RankingSection --> CheckUserStatus{检查用户报名状态}
    FriendlySection --> CheckUserStatus
    LeagueSection --> CheckUserStatus
    
    CheckUserStatus -->|未报名且活动状态为报名中| ShowSignupBtn[显示立即报名按钮]
    CheckUserStatus -->|已报名好友组| ShowFriendRoom[显示好友组房间信息<br/>FriendTeamRoom组件]
    CheckUserStatus -->|已报名待支付| ShowPayBtn[显示去支付按钮]
    CheckUserStatus -->|已报名成功| ShowRegisteredStatus[显示已报名状态<br/>+分队情况展示]
    
    %% 关键：已报名用户的分队展示
    ShowRegisteredStatus --> TeamAssignmentDisplay{分队情况展示<br/>提升用户体验}
    TeamAssignmentDisplay -->|排位赛| ShowTeamAssignment["显示分队状态：<br/>• 您被分配到：主队/客队<br/>• 当前主队X人，客队Y人<br/>• 候补位置：第Z位<br/>• 实时更新状态"]
    TeamAssignmentDisplay -->|友谊赛| ShowTeamMatchup["显示对战安排：<br/>• 您的球队已报名<br/>• 当前已有X支球队<br/>• 预计对战：待安排<br/>• 比赛时间确认中"]
    TeamAssignmentDisplay -->|联赛| ShowLeagueStatus["显示联赛状态：<br/>• 您的球队已加入<br/>• 联赛位置：第X位<br/>• 参赛球队：Y/Z支<br/>• 对阵表：待生成"]
    
    %% 报名流程 - 统一跳转到同一个订单页
    ShowSignupBtn --> ClickSignup{点击报名}
    ClickSignup -->|排位赛个人| IndividualOrder[跳转统一订单页<br/>携带个人报名参数]
    ClickSignup -->|排位赛好友组队| FriendGroupOrder[跳转统一订单页<br/>携带好友组参数]
    ClickSignup -->|友谊赛/联赛| TeamOrder[跳转统一订单页<br/>携带球队报名参数]
    
    %% 好友组房间管理
    ShowFriendRoom --> FriendRoomActions{好友组操作}
    FriendRoomActions -->|邀请好友| GenerateInvite[生成邀请链接]
    FriendRoomActions -->|查看成员| ShowMembers[显示组员信息]
    FriendRoomActions -->|解散房间| DisbandRoom[解散好友组]
    
    %% 统一订单页面流程（重要设计）
    IndividualOrder --> UnifiedOrderPage[统一订单确认页<br/>confirm-order.vue]
    FriendGroupOrder --> UnifiedOrderPage
    TeamOrder --> UnifiedOrderPage
    DirectToUnifiedOrder --> UnifiedOrderPage
    
    UnifiedOrderPage --> DynamicOrderRender{基于路由参数<br/>动态渲染订单内容}
    DynamicOrderRender --> OrderContent["统一订单页动态内容：<br/>• 活动信息卡片（通用）<br/>• 新用户档案表单（条件）<br/>• 个人/好友组/球队信息（参数）<br/>• 费用明细（类型差异）<br/>• 支付方式选择（差异化）<br/>• 协议勾选（通用）"]
    
    OrderContent --> PaymentFlow[跳转支付页面<br/>pay/index.vue]
    PaymentFlow --> SelectPayMethod[选择支付方式]
    SelectPayMethod --> PaySDK[调用支付SDK<br/>pay.js统一处理]
    PaySDK --> ProcessPay[处理支付<br/>微信/支付宝/余额]
    
    ProcessPay --> PayResultPage[支付结果页<br/>pay/result.vue]
    PayResultPage --> CheckPayStatus{轮询支付状态}
    
    %% 关键：支付成功后立即分队（用户体验核心）
    CheckPayStatus -->|成功| ImmediateTeamAssign[🔥立即分队处理<br/>核心用户体验优化]
    ImmediateTeamAssign --> ActivityTypeAssign{活动类型分队}
    
    %% 立即分队逻辑
    ActivityTypeAssign -->|排位赛| InstantRankingAssign[立即分配主客队位置<br/>给用户心理预期]
    ActivityTypeAssign -->|友谊赛| InstantTeamRecord[立即记录球队信息<br/>显示报名成功]
    ActivityTypeAssign -->|联赛| InstantLeagueAdd[立即加入联赛池<br/>显示位置]
    
    %% 分队结果即时展示
    InstantRankingAssign --> ShowAssignmentPreview["显示分队预览：<br/>✅ 您已分配到主队/客队<br/>✅ 当前主队X人，还需Y人<br/>✅ 候补位置：无/第Z位<br/>✅ 预计开赛时间"]
    InstantTeamRecord --> ShowTeamPreview["显示球队状态：<br/>✅ 您的球队已成功报名<br/>✅ 当前已有X支球队报名<br/>✅ 还需Y支球队才能开赛<br/>✅ 预计对战安排"]
    InstantLeagueAdd --> ShowLeaguePreview["显示联赛状态：<br/>✅ 成功加入联赛<br/>✅ 当前排名第X位<br/>✅ 参赛球队Y/Z支<br/>✅ 对阵表生成中"]
    
    ShowAssignmentPreview --> ContinuousStatusUpdate[持续状态更新<br/>实时反馈]
    ShowTeamPreview --> ContinuousStatusUpdate
    ShowLeaguePreview --> ContinuousStatusUpdate
    
    %% 组局达标检查
    ContinuousStatusUpdate --> CheckGroupingCapacity{报名人数达标检查}
    CheckGroupingCapacity -->|达标| FinalGrouping[正式组局分队]
    CheckGroupingCapacity -->|未达标| WaitWithPreview[等待其他用户<br/>保持分队预览可见]
    
    %% 最终组局流程
    FinalGrouping --> FinalActivityType{最终分队类型}
    FinalActivityType -->|排位赛| FinalRankingGrouping[确认主客队最终分配<br/>平衡算法优化]
    FinalActivityType -->|友谊赛| FinalTeamMatching[确认球队对阵<br/>生成比赛安排]
    FinalActivityType -->|联赛| FinalLeagueBracket[生成联赛对阵表<br/>排期赛程]
    
    FinalRankingGrouping --> CreateMatches[创建比赛并通知]
    FinalTeamMatching --> CreateMatches
    FinalLeagueBracket --> CreateMatches
    
    CreateMatches --> BackToDetailWithUpdate[返回活动详情页<br/>显示最新状态]
    WaitWithPreview --> BackToDetailWithUpdate
    
    CheckPayStatus -->|失败| PayFailed[支付失败<br/>重试或返回]
    PayFailed --> PaymentFlow
    
    %% 分享功能
    ShowUnifiedActivity --> ShareAction{分享操作}
    ShareAction -->|未报名用户| ShareNormal[分享普通活动链接]
    ShareAction -->|已报名好友组| ShareInvite[分享带邀请码链接]
    
    %% 样式定义
    classDef unifiedPage fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef immediateAction fill:#ffebee,stroke:#d32f2f,stroke-width:3px
    classDef userExperience fill:#f1f8e9,stroke:#388e3c,stroke-width:3px
    classDef conditionalRender fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class UnifiedActivityPage,UnifiedOrderPage unifiedPage
    class ImmediateTeamAssign,ShowAssignmentPreview,ShowTeamPreview immediateAction
    class TeamAssignmentDisplay,ContinuousStatusUpdate,WaitWithPreview userExperience
    class ConditionalRender,DynamicOrderRender conditionalRender
```


### 5.7 前端活动详情页整合

活动详情页 (`detail.vue`) 是用户了解活动信息和进行报名的核心界面，需要根据不同活动类型和状态展示相应内容

## 12. 球队分配与比赛同步机制

### 12.1 球队ID存储规范

**重要更新**：根据最新业务需求，系统中的球队分配统一使用真实球队ID，而非抽象值：

```sql
-- 报名表中的team_assigned字段
ALTER TABLE `sd_registration` 
MODIFY COLUMN `team_assigned` BIGINT NULL COMMENT '分配到的具体球队ID (关联 sd_team.id)，直接存储真实球队ID，如101L、102L等';

-- 好友组表中的team_assigned字段  
ALTER TABLE `sd_friend_group`
MODIFY COLUMN `team_assigned` BIGINT NULL COMMENT '排位赛分配队伍ID: 直接存储真实球队ID (关联 sd_team.id)，如101L、102L等';
```

**设计原则**：
- **统一性**: 所有涉及球队分配的字段都使用真实的球队ID
- **可追溯性**: 便于跨表关联查询和数据分析
- **扩展性**: 支持未来多队伍场景和复杂对阵

### 12.2 组局成功后的比赛创建与同步

当活动组局成功后，系统需要创建具体的比赛记录，并将报名分队情况同步到比赛球队中：

```mermaid
sequenceDiagram
    participant Timer as 定时任务
    participant ActivityService as 活动服务
    participant GameService as 比赛服务
    participant TeamPlayerService as 球队球员服务
    participant RegistrationService as 报名服务
    participant NotificationService as 通知服务
    
    Note over Timer, NotificationService: 组局成功后的比赛创建流程
    
    Timer->>ActivityService: 检查到达组局时间的活动
    ActivityService->>ActivityService: 检查报名人数是否达标
    
    alt 报名人数达标
        ActivityService->>ActivityService: 更新活动状态为"组局成功"
        ActivityService->>GameService: 创建比赛记录
        
        Note right of GameService: 创建sd_game记录<br/>- 设置比赛时间<br/>- 设置主客队信息<br/>- 关联活动ID
        
        GameService->>RegistrationService: 获取所有已支付报名记录
        RegistrationService-->>GameService: 返回分队后的报名列表
        
        loop 遍历每个报名记录
            GameService->>TeamPlayerService: 将球员添加到对应球队
            Note right of TeamPlayerService: 调用sd_team_player表<br/>- 球员ID<br/>- 球队ID (来自team_assigned)<br/>- 比赛ID<br/>- 角色类型(正式/替补)
        end
        
        GameService->>ActivityService: 更新活动的game_id字段
        ActivityService->>ActivityService: 更新活动状态为"进行中"
        
        GameService->>NotificationService: 发送组局成功通知
        NotificationService->>NotificationService: 推送给所有报名用户
        
    else 报名人数不足
        ActivityService->>ActivityService: 更新活动状态为"组局失败"
        ActivityService->>RegistrationService: 触发全额退款
        ActivityService->>NotificationService: 发送组局失败通知
    end
```