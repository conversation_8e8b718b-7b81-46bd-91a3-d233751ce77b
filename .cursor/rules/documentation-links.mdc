---
description: 
globs: 
alwaysApply: false
---
# 文档与资源链接

本项目基于 [RuoYi-Vue-Pro](mdc:https:/gitee.com/zhijiantianya/ruoyi-vue-pro)，以下是源项目的一些重要链接：

- **官方文档**: <https://doc.iocoder.cn/>
- **快速启动**: <https://doc.iocoder.cn/quick-start/>
- **视频教程**: <https://doc.iocoder.cn/video/>

- **演示地址 (Vue3 + Element Plus)**: <http://dashboard-vue3.yudao.iocoder.cn>
- **演示地址 (Vue3 + vben/Ant Design Vue)**: <http://dashboard-vben.yudao.iocoder.cn>
- **演示地址 (Vue2 + Element UI)**: <http://dashboard.yudao.iocoder.cn>

- **项目 Gitee 仓库**: <https://gitee.com/zhijiantianya/ruoyi-vue-pro>
- **项目 GitHub 仓库**: <https://github.com/YunaiV/ruoyi-vue-pro>

## 特定模块文档

- **商城 - 交易订单**: <https://doc.iocoder.cn/mall/trade-order/> (包括表结构、完整流程等)
- **商城 - 售后退款**: <https://doc.iocoder.cn/mall/trade-aftersale/> (包括表结构、完整流程等)
- **支付中心 - 功能开启/接入**: <https://doc.iocoder.cn/pay/build/>

查阅这些资源可以帮助理解项目的基础架构和功能。





