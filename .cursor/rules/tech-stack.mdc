---
description: 
globs: 
alwaysApply: false
---
# 技术栈概览

本项目主要采用以下技术栈：

## 后端

- **核心框架**: Spring Boot (当前项目基于 v2.7.18)
- **数据访问**: MyBatis Plus (v3.5.7)
- **数据库**: 支持 MySQL (5.7/8.0+), Oracle, PostgreSQL, SQL Server, MariaDB, 达梦 DM, TiDB (通过 MyBatis Plus 支持)
- **缓存**: Redis (v5.0+), 使用 Redisson (v3.32.0) 作为客户端
- **安全**: Spring Security (v5.7.11) & Token & Redis
- **消息队列**: 支持 Event, Redis Streams, RabbitMQ, Kafka, RocketMQ (具体使用哪种需确认)
- **工作流引擎**: Flowable (v6.8.0)
- **任务调度**: Quartz (v2.3.2)
- **构建工具**: Maven
- **编程语言**: Java (JDK 8)
- **其他**: MapStruct (v1.6.3), Lombok (v1.18.34)

## 前端 (位于 [`yudao-ui`](mdc:yudao-ui) 目录)

- **核心框架**: Vue.js (v3.2)
- **UI 组件库**: Element Plus (项目可能基于此版本，但源项目也支持 vben/Ant Design Vue 和 Vue2/Element UI)
- **移动端**: uni-app (可选，用于开发 APP/小程序/H5 管理后台)

## 部署与其他

- **容器化**: Docker (见 [`yudao-server/Dockerfile`](mdc:yudao-server/Dockerfile))
- **服务监控**: Spring Boot Admin (v2.7.10)
- **链路追踪**: SkyWalking (v8.12.0)
- **接口文档**: Springdoc (OpenAPI 3, v1.7.0)
- **Web 服务器/反向代理**: Nginx (常用部署方式)

熟悉这些技术栈有助于理解项目的实现细节和开发模式。



