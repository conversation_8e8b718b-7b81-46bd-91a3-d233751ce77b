package cn.iocoder.yudao.module.operation.service.contract;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;

/**
 * 合同 Service 接口
 *
 * <AUTHOR>
 */
public interface ContractService {

    /*
    球员签约球队
     * @param teamId 球队编号
     * @param userId 用户编号
     */
    PlayerDO signPlayerContract(Long teamId, Long userId, Long messageId);

    void cancelPlayerContract(Long teamId, Long playerId);

    void disbandTeam(Long teamId, Long playerId);

    void applyPlayerContract(Long teamId, Long userId);

    void rejectPlayerContract(Long replyMessageId, String reason);

    /**
     * 邀请球员签约
     * @param teamId
     * @param captainUserId
     * @param receivedUserId
     */
    void invitePlayerSign(Long teamId, Long captainUserId, Long receivedUserId);
}