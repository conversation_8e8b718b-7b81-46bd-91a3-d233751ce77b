package cn.iocoder.yudao.module.operation.controller.admin.daily.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 日常活动更新结果 Request VO")
@Data
public class DailyActivityResultSaveReqVO {
    @Schema(description = "活动id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "活动id不能为空")
    private Long id;

    @Schema(description = "主队得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "主队得分不能为空")
    @Min(value = 0, message = "主队得分不能小于0")
    private Integer homeTeamPoints;

    @Schema(description = "客队得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "客队得分不能为空")
    @Min(value = 0, message = "客队得分不能小于0")
    private Integer guestTeamPoints;

    @Schema(description = "主队出席情况", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "主队出席情况")
    private List<AttendedVO> homePlayers;

    @Schema(description = "客队出席情况", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "客队出席情况")
    private List<AttendedVO> guestPlayers;

    @Data
    public static class AttendedVO {
        @Schema(description = "参赛人员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
        @NotNull(message = "参赛人员id不能为空")
        private Long playerId;

        @Schema(description = "报名ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
        @NotNull(message = "报名ID不能为空")
        private Long playerRegistrationId;

        /**
         * 报名状态（1、缺席，2、出席）
         */
        private Integer attend;
    }
}
