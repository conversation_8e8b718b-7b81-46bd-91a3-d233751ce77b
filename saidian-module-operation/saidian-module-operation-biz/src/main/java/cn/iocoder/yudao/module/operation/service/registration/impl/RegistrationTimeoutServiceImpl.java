package cn.iocoder.yudao.module.operation.service.registration.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationTimeoutService;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报名超时处理服务实现类
 * 
 * 🎯 核心价值：
 * 1. 解决事务自调用问题，确保事务正确生效
 * 2. 提供专门的超时处理逻辑，避免业务逻辑混乱
 * 3. 统一管理候补提升和超时清理
 * 4. 提供详细的日志和错误处理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class RegistrationTimeoutServiceImpl implements RegistrationTimeoutService {

    @Resource
    private RegistrationMapper registrationMapper;
    
    @Resource
    private ActivityMapper activityMapper;
    
    @Resource
    private WaitlistService waitlistService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int processTimeoutRegistrations() {
        log.info("[processTimeoutRegistrations] 开始处理创建时间超时的记录");
        
        // 查询30分钟前创建但仍未支付的记录
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);
        
        List<RegistrationDO> timeoutRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PENDING_PAYMENT.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.UNPAID.getStatus())
                        .lt(RegistrationDO::getCreateTime, timeoutThreshold)
        );
        
        if (CollUtil.isEmpty(timeoutRegistrations)) {
            log.info("[processTimeoutRegistrations] 没有找到创建时间超时的记录");
            return 0;
        }
        
        log.info("[processTimeoutRegistrations] 找到 {} 条创建时间超时的记录", timeoutRegistrations.size());
        
        int processedCount = 0;
        for (RegistrationDO registration : timeoutRegistrations) {
            try {
                updateRegistrationToTimeout(registration, "创建时间超过30分钟未支付");
                processedCount++;
            } catch (Exception e) {
                log.error("[processTimeoutRegistrations] 处理超时记录失败: registrationId={}", 
                         registration.getId(), e);
            }
        }
        
        return processedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchPromoteWaitlist() {
        log.info("[batchPromoteWaitlist] 开始批量处理候补提升");
        
        // 查询所有有候补球员的活动
        List<RegistrationDO> waitlistRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .select(RegistrationDO::getActivityId)
        );
        
        if (CollUtil.isEmpty(waitlistRegistrations)) {
            log.info("[batchPromoteWaitlist] 没有找到有候补球员的活动");
            return 0;
        }
        
        // 按活动分组
        Map<Long, List<RegistrationDO>> activityGroups = waitlistRegistrations.stream()
                .collect(Collectors.groupingBy(RegistrationDO::getActivityId));
        
        int totalPromoted = 0;
        for (Long activityId : activityGroups.keySet()) {
            try {
                ActivityDO activity = activityMapper.selectById(activityId);
                if (activity != null) {
                    List<RegistrationDO> promoted = waitlistService.promoteFromWaitlist(activity);
                    totalPromoted += promoted.size();
                    
                    if (!promoted.isEmpty()) {
                        log.info("[batchPromoteWaitlist] 活动 {} 提升了 {} 个候补球员", 
                                activityId, promoted.size());
                    }
                }
            } catch (Exception e) {
                log.error("[batchPromoteWaitlist] 处理活动候补提升失败: activityId={}", activityId, e);
            }
        }
        
        return totalPromoted;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRegistrationToTimeout(RegistrationDO registration, String reason) {
        // 1. 更新报名记录状态
        RegistrationDO updateObj = new RegistrationDO();
        updateObj.setId(registration.getId());
        updateObj.setStatus(RegistrationStatusEnum.CANCELLED.getStatus());
        updateObj.setPaymentStatus(PayStatusEnum.CLOSED.getStatus()); // 🔧 修复：使用CLOSED状态保持与支付系统一致
        updateObj.setCancelReason(reason);
        updateObj.setUpdateTime(LocalDateTime.now());

        int updateCount = registrationMapper.updateById(updateObj);
        if (updateCount == 0) {
            log.error("[updateRegistrationToTimeout] 更新报名记录失败: registrationId={}", registration.getId());
            throw new RuntimeException("更新报名状态失败");
        }

        // 2. 处理候补相关逻辑
        if (Boolean.TRUE.equals(registration.getIsWaitlist()) && registration.getWaitlistPosition() != null) {
            try {
                reorderWaitlistFromPosition(registration.getActivityId(), registration.getWaitlistPosition());
                log.info("[updateRegistrationToTimeout] 候补球员超时，重排候补队列: registrationId={}, position={}", 
                        registration.getId(), registration.getWaitlistPosition());
            } catch (Exception e) {
                log.error("[updateRegistrationToTimeout] 重排候补队列失败: registrationId={}", 
                         registration.getId(), e);
                // 不抛出异常，避免影响主流程
            }
        } else {
            // 3. 如果是正式报名球员，检查是否有候补球员可以提升
            try {
                ActivityDO activity = activityMapper.selectById(registration.getActivityId());
                if (activity != null) {
                    List<RegistrationDO> promotedRegistrations = waitlistService.promoteFromWaitlist(activity);
                    if (!promotedRegistrations.isEmpty()) {
                        log.info("[updateRegistrationToTimeout] 正式报名球员支付超时，提升了 {} 个候补球员", 
                                promotedRegistrations.size());
                    }
                }
            } catch (Exception e) {
                log.error("[updateRegistrationToTimeout] 处理候补提升失败: registrationId={}", 
                         registration.getId(), e);
                // 不抛出异常，避免影响主流程
            }
        }

        log.info("[updateRegistrationToTimeout] 成功处理超时报名: registrationId={}, reason={}", 
                registration.getId(), reason);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reorderWaitlistFromPosition(Long activityId, Integer fromPosition) {
        try {
            waitlistService.reorderWaitlist(activityId, fromPosition);
            log.info("[reorderWaitlistFromPosition] 成功重排候补队列: activityId={}, fromPosition={}", 
                    activityId, fromPosition);
        } catch (Exception e) {
            log.error("[reorderWaitlistFromPosition] 重排候补队列失败: activityId={}, fromPosition={}", 
                     activityId, fromPosition, e);
            throw e;
        }
    }
}
