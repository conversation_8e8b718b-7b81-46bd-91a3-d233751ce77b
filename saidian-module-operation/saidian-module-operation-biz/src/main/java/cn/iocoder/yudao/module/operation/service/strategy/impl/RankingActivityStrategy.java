package cn.iocoder.yudao.module.operation.service.strategy.impl;

import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;

import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.friendgroup.FriendGroupDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.strategy.AbstractActivityStrategy;
import cn.iocoder.yudao.module.operation.service.strategy.util.FeeCalculationUtils;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.friendgroup.FriendGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.extra.spring.SpringUtil;

/**
 * 排位赛活动策略
 * 
 * 继承AbstractActivityStrategy，复用通用逻辑：
 * - 基础校验、支付处理、组局检查等
 * - 重写特定的业务逻辑
 */
@Component
@Slf4j
public class RankingActivityStrategy extends AbstractActivityStrategy {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private GameService gameService;

    @Resource
    private TeamAssignmentService teamAssignmentService;

    @Resource
    private FriendGroupService friendGroupService;
    
    @Resource
    private GamePlayerSyncService gamePlayerSyncService;

    @Override
    public ActivityTypeEnum getActivityType() {
        return ActivityTypeEnum.RANKING;
    }

    @Override
    public Integer calculateEffectiveFee(@NotNull ActivityDO activity, @NotNull FeeCalculationContext context) {
        // 使用工具类计算人均费用 - 收费场景，向上取整
        return feeCalculationUtils.calculatePerPlayerFee(activity.getTotalFee(), activity.getMinPlayersPerGame(), FeeCalculationUtils.RoundingMode.CEIL);
    }

    @Override
    public void createGamesAfterGrouping(@NotNull ActivityDO activity) {
        log.info("[createGamesAfterGrouping][排位赛][活动 {}] 组局成功，创建比赛", activity.getId());
        
        try {
            // 🔧 优化：使用统一的状态查询服务获取所有有效的报名记录
            List<RegistrationDO> paidRegistrations = registrationStatusQueryService.getFormalRegistrations(activity.getId());

            if (paidRegistrations.isEmpty()) {
                log.warn("[createGamesAfterGrouping][排位赛][活动 {}] 没有已支付的报名记录", activity.getId());
                return;
            }

            // 创建比赛 - 使用GameService的通用方法
            Long gameId = gameService.createGameFromActivity(activity);

            // 更新活动的gameId
            activityMapper.updateById(
                new ActivityDO().setId(activity.getId()).setGameId(gameId)
            );
            
            // 🔥 关键：同步所有已报名球员到比赛球员表
            gamePlayerSyncService.syncAllPlayersToGame(activity.getId(), gameId);

            log.info("[createGamesAfterGrouping][排位赛][活动 {}] 比赛创建成功，gameId: {}，已同步球员名单", 
                    activity.getId(), gameId);
        } catch (Exception e) {
            log.error("[createGamesAfterGrouping][排位赛][活动 {}] 创建比赛失败", activity.getId(), e);
            throw exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "创建比赛失败: " + e.getMessage());
        }
    }

    @Override
    protected int getMinRequiredPlayersForGrouping(ActivityDO activity) {
        return activity.getMinPlayersPerGame() != null ? activity.getMinPlayersPerGame() : 16;
    }

    @Override
    public void handlePaymentSuccessAfterStateUpdate(ActivityDO activity, RegistrationDO registration) {
        log.info("[handlePaymentSuccessAfterStateUpdate][排位赛][报名ID({}) 活动ID({})] 处理支付成功后的排位赛特定逻辑",
                registration.getId(), activity.getId());

        // 1. 优先处理组局成功后的状态转换
        handlePostGroupingRegistrationStatus(activity, registration);
        
        // 2. 处理好友组队逻辑
        handleFriendGroupLogic(activity, registration);
        
        // 3. 处理实时分队
        handleRealTimeTeamAssignment(activity, registration);
    }

    @Override
    protected void doValidateRegistrationRequest(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId) {
        // 检查总费用和最低人数配置
        if (activity.getTotalFee() == null || activity.getTotalFee() <= 0) {
            throw exception(ErrorCodeConstants.ACTIVITY_CONFIG_ERROR, "排位赛总费用未配置或配置错误");
        }
        
        if (activity.getMinPlayersPerGame() == null || activity.getMinPlayersPerGame() <= 0) {
            throw exception(ErrorCodeConstants.ACTIVITY_CONFIG_ERROR, "排位赛最低人数未配置或配置错误");
        }
        
        log.info("[doValidateRegistrationRequest][排位赛] 请求参数校验通过: activityId={}, totalFee={}, minPlayers={}", 
                activity.getId(), activity.getTotalFee(), activity.getMinPlayersPerGame());
    }





    /**
     * 处理好友组队逻辑
     */
    private void handleFriendGroupLogic(ActivityDO activity, RegistrationDO registration) {
        if (registration.getIsGroupLeader() != null && registration.getIsGroupLeader()) {
            log.info("[handleFriendGroupLogic][报名ID({})] 是队长，创建好友组队房间", registration.getId());
            
            try {
                // 使用FriendGroupService创建好友组，这会正确设置所有必要的字段（包括邀请码和过期时间）
                // 注意：参数顺序应为 (activityId, userId, registrationId, activity)
                FriendGroupDO friendGroup = friendGroupService.createFriendGroup(
                    activity.getId(), 
                    registration.getUserId(), 
                    registration.getId(), 
                    activity
                );
                
                log.info("[handleFriendGroupLogic][报名ID({})] 好友组队房间创建成功, friendGroupId: {}, 邀请码: {}", 
                        registration.getId(), friendGroup.getId(), friendGroup.getInviteCode());
                        
            } catch (Exception e) {
                log.error("[handleFriendGroupLogic][报名ID({})] 创建好友组失败", registration.getId(), e);
                // 不抛出异常，避免影响支付流程，但记录错误
            }
        }
    }

    /**
     * 处理实时分队
     * 委托给TeamAssignmentService处理所有分队逻辑和相关的球员Game操作
     */
    private void handleRealTimeTeamAssignment(ActivityDO activity, RegistrationDO registration) {
        log.info("[handleRealTimeTeamAssignment][排位赛][报名ID({})] 开始实时分队", registration.getId());
        
        try {
            // 委托给TeamAssignmentService处理分队逻辑
            // 分队服务会处理：1）分队算法 2）数据库更新 3）Game球员管理
            teamAssignmentService.assignPlayerToTeam(activity.getId(), registration.getId());
            
            log.info("[handleRealTimeTeamAssignment][排位赛][报名ID({})] 实时分队完成", registration.getId());
        } catch (Exception e) {
            log.error("[handleRealTimeTeamAssignment][排位赛][报名ID({})] 实时分队失败", registration.getId(), e);
            // 不抛出异常，避免影响支付流程
        }
    }

    @Override
    protected void doPreGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        log.info("[doPreGroupingLogic][排位赛][活动 {}] 执行组局前预处理，开始最终分队算法", activity.getId());
        
        try {
            // 执行最终分队平衡算法 - 使用已存在的方法
            teamAssignmentService.assignAllPlayers(activity.getId());
            
            log.info("[doPreGroupingLogic][排位赛][活动 {}] 最终分队算法执行完成", activity.getId());
            
        } catch (Exception e) {
            log.error("[doPreGroupingLogic][排位赛][活动 {}] 最终分队算法执行失败", activity.getId(), e);
            // 记录错误但不中断组局流程，分队算法失败不应该导致组局失败
        }
    }

    @Override  
    protected void doPostGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        log.info("[doPostGroupingLogic][排位赛][活动 {}] 执行组局后处理逻辑", activity.getId());
        
        // 调用父类的默认逻辑（更新报名状态为成功）
        super.doPostGroupingLogic(activity, checkResult);
    }



    @Override
    protected int processDifferentialRefund(ActivityDO activity) {
        log.info("[processDifferentialRefund][排位赛] 开始处理排位赛活动 id={} 的差额退款", activity.getId());

        // 获取所有成功报名的非候补记录
        List<RegistrationDO> successfulRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activity.getId())
                        .eq(RegistrationDO::getStatus, RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getIsWaitlist, false) // 排除候补球员
        );

        if (successfulRegistrations.isEmpty()) {
            log.warn("[processDifferentialRefund][排位赛] 活动 id={} 没有找到成功报名的非候补记录", activity.getId());
            return 0;
        }

        int actualParticipants = successfulRegistrations.size();
        int refundCount = 0;
        log.info("[processDifferentialRefund][排位赛] 活动 id={} 实际参与人数（非候补）: {}", activity.getId(), actualParticipants);

        // 🔧 修复：每个报名使用独立事务处理，避免单个失败导致整个批次回滚，增强幂等性保护
        for (RegistrationDO registration : successfulRegistrations) {
            try {
                // 🔧 幂等性检查：避免重复处理已经处理过差额退款的记录
                if (isDifferentialRefundAlreadyProcessed(registration)) {
                    log.info("[processDifferentialRefund][排位赛] 报名 id={} 差额退款已处理过，跳过", registration.getId());
                    continue;
                }

                // 使用独立事务处理单个报名的差额退款
                getSelf().processSingleDifferentialRefund(registration, actualParticipants);
                refundCount++;
                log.info("[processDifferentialRefund][排位赛] 报名 id={} 差额退款处理已发起", registration.getId());

            } catch (Exception e) {
                log.error("[processDifferentialRefund][排位赛] 处理报名 id={} 差额退款失败", registration.getId(), e);
                // 继续处理下一个，不中断整个流程
            }
        }

        log.info("[processDifferentialRefund][排位赛] 活动 id={} 差额退款处理完成，处理 {} 笔", activity.getId(), refundCount);
        return refundCount;
    }

    /**
     * 处理单个报名的差额退款（独立事务）
     * 避免单个报名失败影响其他报名的处理
     *
     * @param registration 报名记录
     * @param actualParticipants 实际参与人数
     */
    public void processSingleDifferentialRefund(RegistrationDO registration, int actualParticipants) {
        log.info("[processSingleDifferentialRefund][排位赛] 开始处理报名 id={} 的差额退款，实际参与人数: {}",
                registration.getId(), actualParticipants);

        try {
            // 使用现有的差额退款服务处理
            activityRefundService.processRefund(registration.getId(),
                    RefundScenario.DIFFERENCE,
                    0L,
                    String.format("比赛开始差额退款，实际参与%d人", actualParticipants));

            log.info("[processSingleDifferentialRefund][排位赛] 报名 id={} 差额退款处理成功", registration.getId());

        } catch (Exception e) {
            log.error("[processSingleDifferentialRefund][排位赛] 报名 id={} 差额退款处理失败", registration.getId(), e);
            throw e; // 重新抛出异常，触发独立事务回滚
        }
    }

    /**
     * 检查是否已经处理过差额退款（幂等性检查）
     *
     * @param registration 报名记录
     * @return true-已处理过，false-未处理过
     */
    private boolean isDifferentialRefundAlreadyProcessed(RegistrationDO registration) {
        // 检查是否有退款记录且退款原因包含差额退款标识
        Integer refundStatus = registration.getRefundStatus();
        String cancelReason = registration.getCancelReason();

        // 如果退款状态为部分退款或全额退款，且取消原因包含"差额退款"，则认为已处理过
        boolean hasRefundStatus = (RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(refundStatus) ||
                RefundStatusEnum.FULL_REFUNDED.getStatus().equals(refundStatus) ||
                RefundStatusEnum.REFUNDING.getStatus().equals(refundStatus));

        boolean hasDifferentialReason = cancelReason != null &&
                (cancelReason.contains("差额退款") || cancelReason.contains("实际参与"));

        return hasRefundStatus && hasDifferentialReason;
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private RankingActivityStrategy getSelf() {
        return SpringUtil.getBean(getClass());
    }
}