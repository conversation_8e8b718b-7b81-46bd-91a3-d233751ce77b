package cn.iocoder.yudao.module.operation.controller.admin.contract;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.contract.vo.ContractTemplatePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.contract.vo.ContractTemplateRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.contract.vo.ContractTemplateSaveReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.contract.ContractTemplateDO;
import cn.iocoder.yudao.module.operation.service.contract.ContractTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同邀请")
@RestController
@RequestMapping("/operation/contract-template")
@Validated
public class ContractTemplateController {

    @Resource
    private ContractTemplateService contractTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建合同邀请")
    @PreAuthorize("@ss.hasPermission('operation:contract-template:create')")
    public CommonResult<Long> createContractTemplate(@Valid @RequestBody ContractTemplateSaveReqVO createReqVO) {
        return success(contractTemplateService.createContractTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合同邀请")
    @PreAuthorize("@ss.hasPermission('operation:contract-template:update')")
    public CommonResult<Boolean> updateContractTemplate(@Valid @RequestBody ContractTemplateSaveReqVO updateReqVO) {
        contractTemplateService.updateContractTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合同邀请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('operation:contract-template:delete')")
    public CommonResult<Boolean> deleteContractTemplate(@RequestParam("id") Long id) {
        contractTemplateService.deleteContractTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合同邀请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:contract-template:query')")
    public CommonResult<ContractTemplateRespVO> getContractTemplate(@RequestParam("id") Long id) {
        ContractTemplateDO contractTemplate = contractTemplateService.getContractTemplate(id);
        return success(BeanUtils.toBean(contractTemplate, ContractTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合同邀请分页")
    @PreAuthorize("@ss.hasPermission('operation:contract-template:query')")
    public CommonResult<PageResult<ContractTemplateRespVO>> getContractTemplatePage(@Valid ContractTemplatePageReqVO pageReqVO) {
        PageResult<ContractTemplateDO> pageResult = contractTemplateService.getContractTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ContractTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合同邀请 Excel")
    @PreAuthorize("@ss.hasPermission('operation:contract-template:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportContractTemplateExcel(@Valid ContractTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ContractTemplateDO> list = contractTemplateService.getContractTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合同邀请.xls", "数据", ContractTemplateRespVO.class,
                        BeanUtils.toBean(list, ContractTemplateRespVO.class));
    }

}