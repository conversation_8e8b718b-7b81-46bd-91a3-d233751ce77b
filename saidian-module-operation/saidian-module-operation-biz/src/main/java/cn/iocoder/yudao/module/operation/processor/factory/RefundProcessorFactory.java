package cn.iocoder.yudao.module.operation.processor.factory;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.processor.AbstractRefundProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 退款处理器工厂 - 负责根据退款场景获取对应的退款处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefundProcessorFactory {

    /**
     * 退款处理器映射 scenario -> processor
     */
    private final Map<RefundScenario, AbstractRefundProcessor> processorMap = new HashMap<>();

    /**
     * 所有退款处理器实现
     * 通过Spring注入所有实现了AbstractRefundProcessor的Bean
     */
    @Resource
    private List<AbstractRefundProcessor> processors;

    /**
     * 初始化处理器映射
     */
    @PostConstruct
    public void init() {
        processors.forEach(processor -> {
            RefundScenario scenario = processor.getSupportedScenario();
            processorMap.put(scenario, processor);
            log.info("[init] 注册退款处理器: {} -> {}", scenario, processor.getClass().getSimpleName());
        });
    }

    /**
     * 根据退款场景获取对应的处理器
     * 
     * @param scenario 退款场景
     * @return 退款处理器
     */
    public AbstractRefundProcessor getProcessor(RefundScenario scenario) {
        AbstractRefundProcessor processor = processorMap.get(scenario);
        
        if (processor == null) {
            log.error("[getProcessor] 未找到退款场景对应的处理器: {}", scenario);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.INVALID_REFUND_SCENARIO);
        }
        
        return processor;
    }
} 