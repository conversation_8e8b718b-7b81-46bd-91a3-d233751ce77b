package cn.iocoder.yudao.module.operation.controller.admin.game;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerStatisticsImportVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerStatisticsPageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerStatisticsRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerStatisticsSaveReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.service.game.PlayerStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 比赛球员数据")
@RestController
@RequestMapping("/operation/player-statistics")
@Validated
public class PlayerStatisticsController {

    @Resource
    private PlayerStatisticsService playerStatisticsService;

    @PostMapping("/create")
    @Operation(summary = "创建比赛球员数据")
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:create')")
    public CommonResult<Long> createPlayerStatistics(@Valid @RequestBody PlayerStatisticsSaveReqVO createReqVO) {
        return success(playerStatisticsService.createPlayerStatistics(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新比赛球员数据")
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:update')")
    public CommonResult<Boolean> updatePlayerStatistics(@Valid @RequestBody PlayerStatisticsSaveReqVO updateReqVO) {
        playerStatisticsService.updatePlayerStatistics(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除比赛球员数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:delete')")
    public CommonResult<Boolean> deletePlayerStatistics(@RequestParam("id") Long id) {
        playerStatisticsService.deletePlayerStatistics(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得比赛球员数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:query')")
    public CommonResult<PlayerStatisticsRespVO> getPlayerStatistics(@RequestParam("id") Integer id) {
        PlayerStatisticsDO playerStatistics = playerStatisticsService.getPlayerStatistics(id);
        return success(BeanUtils.toBean(playerStatistics, PlayerStatisticsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得比赛球员数据分页")
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:query')")
    public CommonResult<PageResult<PlayerStatisticsRespVO>> getPlayerStatisticsPage(@Valid PlayerStatisticsPageReqVO pageReqVO) {
        PageResult<PlayerStatisticsDO> pageResult = playerStatisticsService.getPlayerStatisticsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PlayerStatisticsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出比赛球员数据 Excel")
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPlayerStatisticsExcel(@Valid PlayerStatisticsPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PlayerStatisticsDO> list = playerStatisticsService.getPlayerStatisticsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "比赛球员数据.xls", "数据", PlayerStatisticsRespVO.class,
                BeanUtils.toBean(list, PlayerStatisticsRespVO.class));
    }

    @PostMapping("/import")
    @Operation(summary = "导入比赛数据")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('operation:player-statistics:create')")
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<PlayerStatisticsImportVO> list = ExcelUtils.read(file, PlayerStatisticsImportVO.class);
        return success(playerStatisticsService.createPlayerStatistics(list));
    }

}