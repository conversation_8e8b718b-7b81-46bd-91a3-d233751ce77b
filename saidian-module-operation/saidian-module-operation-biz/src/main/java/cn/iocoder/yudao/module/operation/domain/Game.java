package cn.iocoder.yudao.module.operation.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Game {
    /**
     * ID
     */
    private Long id;
    /**
     * 比赛开始时间
     */
    private LocalDateTime startTime;

    /**
     * 比赛类型：1。日常活动
     */
    private Integer type;

    /**
     * 比赛状态,与活动状态保持一致
     */
    private Integer status;

    private ParticipatingTeam homeTeam;
    private ParticipatingTeam guestTeam;
}
