package cn.iocoder.yudao.module.operation.dal.mysql.daily;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.operation.controller.app.daily.vo.AppDailyActivityRegisterPlayerRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.PlayerRegistrationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 比赛个人报名 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlayerRegistrationMapper extends BaseMapperX<PlayerRegistrationDO> {
    List<AppDailyActivityRegisterPlayerRespVO> getRegistrationByTeamId(Long teamId);
}