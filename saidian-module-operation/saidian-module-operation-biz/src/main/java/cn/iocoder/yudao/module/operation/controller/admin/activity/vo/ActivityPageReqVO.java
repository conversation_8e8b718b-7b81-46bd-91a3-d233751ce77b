package cn.iocoder.yudao.module.operation.controller.admin.activity.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 活动分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ActivityPageReqVO extends PageParam {

    @Schema(description = "活动名称", example = "排位赛")
    private String name;

    @Schema(description = "活动类型: 1-排位赛, 2-友谊赛, 3-联赛", example = "1")
    private Integer type;

    @Schema(description = "活动状态: 1-未开始, 2-报名中...", example = "2")
    private Integer status;

    @Schema(description = "活动地点", example = "篮球公园")
    private String location;

    @Schema(description = "创建时间范围")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    // 可以根据需要添加更多筛选字段，如 start_time 范围等

} 