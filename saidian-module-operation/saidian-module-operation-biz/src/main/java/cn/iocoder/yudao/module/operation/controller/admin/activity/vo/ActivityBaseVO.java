package cn.iocoder.yudao.module.operation.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Future;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 活动 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ActivityBaseVO {

    @Schema(description = "活动名称", required = true, example = "周三晚场排位赛")
    @NotNull(message = "活动名称不能为空")
    private String name;

    @Schema(description = "活动类型: 1-排位赛, 2-友谊赛, 3-联赛", required = true, example = "1")
    @NotNull(message = "活动类型不能为空")
    private Integer type;

    @Schema(description = "游戏类型: 1-全场5人制, 2-半场4人制", example = "1")
    private Integer gameType;

    @Schema(description = "关联的 sd_game ID", example = "1024")
    private Long gameId;

    @Schema(description = "活动/报名开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @Future(message = "开始时间必须是未来的时间")
    private LocalDateTime startTime;

    @Schema(description = "活动/报名结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    @Future(message = "结束时间必须是未来的时间")
    private LocalDateTime endTime;

    @Schema(description = "报名截止时间", required = true)
    @NotNull(message = "报名截止时间不能为空")
    @Future(message = "报名截止时间必须是未来的时间")
    private LocalDateTime registrationDeadline;

    @Schema(description = "活动地点", example = "XX篮球公园")
    private String location;
    
    @Schema(description = "活动封面图片地址", example = "https://example.com/image.jpg")
    private String picUrl;

    @Schema(description = "总费用(分, 排位赛/友谊赛用)", example = "6000")
    @Min(value = 0, message = "总费用不能为负数")
    private Integer totalFee;

    @Schema(description = "每场最低人数(排位赛)", example = "10")
    @Min(value = 1, message = "最低人数必须大于0")
    private Integer minPlayersPerGame;

    @Schema(description = "每场最高人数(排位赛)", example = "12")
    @Min(value = 1, message = "最高人数必须大于0")
    private Integer maxPlayersPerGame;

    @Schema(description = "友谊赛最低成团队伍数", example = "2")
    private Integer minTeamsFriendly;

    @Schema(description = "友谊赛每队最低人数", example = "5")
    private Integer minPlayersPerTeamFriendly;

    @Schema(description = "友谊赛每队最高人数", example = "7")
    private Integer maxPlayersPerTeamFriendly;

    @Schema(description = "联赛每人报名费(分)", example = "10000")
    private Integer leagueFeePerPlayer;

    @Schema(description = "联赛最低成团队伍数", example = "4")
    private Integer minTeamsLeague;

    @Schema(description = "联赛每队最低人数", example = "8")
    private Integer minPlayersPerTeamLeague;
    
    @Schema(description = "主队ID (排位赛用, 关联 sd_team.id)", example = "1001")
    private Long homeTeamId;
    
    @Schema(description = "客队ID (排位赛用, 关联 sd_team.id)", example = "1002")
    private Long guestTeamId;
    
    @Schema(description = "是否支持好友组队 (主要用于排位赛)", example = "true")
    private Boolean friendGroupSupported;
    
    @Schema(description = "每个好友组最大人数 (排位赛用, 一般为3人)", example = "3")
    private Integer maxPlayersPerFriendGroup;
    
    @Schema(description = "好友组队邀请有效时长(分钟)", example = "30")
    private Integer friendGroupInviteDurationMinutes;

    @Schema(description = "备注", example = "请穿深色球衣")
    private String remark;

    @AssertTrue(message = "结束时间必须在开始时间之后")
    public boolean isEndTimeAfterStartTime() {
        return startTime == null || endTime == null || endTime.isAfter(startTime);
    }

    @AssertTrue(message = "报名截止时间必须在开始时间之前")
    public boolean isDeadlineBeforeStartTime() {
        return startTime == null || registrationDeadline == null || registrationDeadline.isBefore(startTime);
    }

    @AssertTrue(message = "报名截止时间必须在结束时间之前")
    public boolean isDeadlineBeforeEndTime() {
        return endTime == null || registrationDeadline == null || registrationDeadline.isBefore(endTime);
    }

    @AssertTrue(message = "最高人数必须大于或等于最低人数")
    public boolean isMaxPlayersValid() {
        if (type != null && type == 1 && minPlayersPerGame != null && maxPlayersPerGame != null) {
            return maxPlayersPerGame >= minPlayersPerGame;
        }
        return true;
    }
    
    @AssertTrue(message = "排位赛必须设置主队和客队ID")
    public boolean isTeamInfoValidForRanking() {
        if (type != null && type == 1) { // 排位赛
            return homeTeamId != null && guestTeamId != null;
        }
        return true;
    }
    
    @AssertTrue(message = "好友组队设置必须有最大人数和有效时长")
    public boolean isFriendGroupSettingsValid() {
        if (Boolean.TRUE.equals(friendGroupSupported)) {
            return maxPlayersPerFriendGroup != null && maxPlayersPerFriendGroup > 0 &&
                   friendGroupInviteDurationMinutes != null && friendGroupInviteDurationMinutes > 0;
        }
        return true;
    }

} 