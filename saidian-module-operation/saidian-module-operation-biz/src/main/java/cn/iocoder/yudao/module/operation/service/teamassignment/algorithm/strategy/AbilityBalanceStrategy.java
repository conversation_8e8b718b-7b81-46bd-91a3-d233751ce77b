package cn.iocoder.yudao.module.operation.service.teamassignment.algorithm.strategy;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.enums.PlayerLevelEnum;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;
import cn.iocoder.yudao.module.operation.service.teamassignment.algorithm.config.BalanceWeightConfig;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import cn.iocoder.yudao.module.operation.util.LevelBalanceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 能力值平衡策略
 *
 * 负责评估和优化两队的能力值平衡，确保比赛的公平性。
 * 基于球员的综合能力评分进行平衡度计算。
 *
 * <AUTHOR>
 */
@Component
@Order(1) // 最高优先级
@Slf4j
public class AbilityBalanceStrategy implements BalanceStrategy {

    // 注意：权重配置已迁移到 BalanceWeightConfig 类中统一管理

    /**
     * 可接受的能力值差异阈值（百分比）
     */
    private static final double ACCEPTABLE_ABILITY_DIFF_THRESHOLD = 0.15; // 15%

    @Override
    public String getStrategyType() {
        return "ABILITY_BALANCE";
    }

    @Override
    public double getWeight() {
        return BalanceWeightConfig.ABILITY_WEIGHT;
    }

    @Override
    public int calculateBalanceScore(@NotNull List<PlayerRegistrationInfo> homeTeamPlayers,
                                   @NotNull List<PlayerRegistrationInfo> guestTeamPlayers) {
        
        if (homeTeamPlayers.isEmpty() && guestTeamPlayers.isEmpty()) {
            return 100; // 都为空时认为完全平衡
        }

        if (homeTeamPlayers.isEmpty() || guestTeamPlayers.isEmpty()) {
            return 0; // 一队为空时完全不平衡
        }

        // 1. 按能力等级分类统计（核心修复）
        Map<PlayerLevelEnum, Integer> homeLevels = LevelBalanceUtils.getLevelDistribution(homeTeamPlayers);
        Map<PlayerLevelEnum, Integer> guestLevels = LevelBalanceUtils.getLevelDistribution(guestTeamPlayers);
        
        // 2. 计算等级平衡度（优先级最高）
        int levelBalanceScore = LevelBalanceUtils.calculateLevelBalance(homeLevels, guestLevels);
        
        // 3. 计算平均能力值平衡度
        int avgBalanceScore = calculateAverageAbilityBalance(homeTeamPlayers, guestTeamPlayers);
        
        // 4. 综合评分（等级平衡权重更高，确保S级和A级球员均匀分布）
        return (int) (levelBalanceScore * 0.7 + avgBalanceScore * 0.3);
    }

    /**
     * 计算平均能力值平衡度（保留原有逻辑作为补充）
     */
    private int calculateAverageAbilityBalance(List<PlayerRegistrationInfo> homeTeamPlayers,
                                             List<PlayerRegistrationInfo> guestTeamPlayers) {
        // 计算两队的平均能力值
        double homeAvgAbility = calculateAverageAbility(homeTeamPlayers);
        double guestAvgAbility = calculateAverageAbility(guestTeamPlayers);

        // 计算能力值差异百分比
        double maxAbility = Math.max(homeAvgAbility, guestAvgAbility);
        if (maxAbility == 0) {
            return 100; // 都为0时认为平衡
        }

        double abilityDiffPercentage = Math.abs(homeAvgAbility - guestAvgAbility) / maxAbility;

        // 转换为平衡度评分（0-100）
        if (abilityDiffPercentage <= ACCEPTABLE_ABILITY_DIFF_THRESHOLD) {
            // 在可接受范围内，评分80-100
            return (int) (100 - (abilityDiffPercentage / ACCEPTABLE_ABILITY_DIFF_THRESHOLD) * 20);
        } else {
            // 超出可接受范围，评分0-80
            double excessDiff = abilityDiffPercentage - ACCEPTABLE_ABILITY_DIFF_THRESHOLD;
            return Math.max(0, (int) (80 - excessDiff * 200)); // 快速下降到0
        }
    }

    @Override
    public TeamAssignmentResult.BalanceEvaluation.OptimizationSuggestion getOptimizationSuggestion(
            @NotNull List<PlayerRegistrationInfo> homeTeamPlayers,
            @NotNull List<PlayerRegistrationInfo> guestTeamPlayers) {
        
        // 1. 获取等级分布
        Map<PlayerLevelEnum, Integer> homeLevels = LevelBalanceUtils.getLevelDistribution(homeTeamPlayers);
        Map<PlayerLevelEnum, Integer> guestLevels = LevelBalanceUtils.getLevelDistribution(guestTeamPlayers);
        
        // 2. 分析等级不平衡情况
        LevelBalanceUtils.LevelImbalanceAnalysis analysis = LevelBalanceUtils.analyzeLevelImbalance(homeLevels, guestLevels);
        
        List<String> suggestions = new ArrayList<>();
        String suggestionType;
        int expectedImprovement = 0;

        if (analysis.isBalanced()) {
            suggestionType = "MAINTAIN";
            suggestions.add("当前能力等级分布平衡良好，建议保持现状");
        } else {
            suggestionType = "REBALANCE_LEVEL";
            
            // 针对S级和A级球员的具体建议
            Map<PlayerLevelEnum, Integer> homeAdvantages = analysis.getHomeAdvantages();
            Map<PlayerLevelEnum, Integer> guestAdvantages = analysis.getGuestAdvantages();
            
            for (Map.Entry<PlayerLevelEnum, Integer> entry : homeAdvantages.entrySet()) {
                PlayerLevelEnum level = entry.getKey();
                int count = entry.getValue();
                suggestions.add(String.format("主队%s球员多%d人，建议调整%d人到客队", 
                    level.getName(), count, (count + 1) / 2));
            }
            
            for (Map.Entry<PlayerLevelEnum, Integer> entry : guestAdvantages.entrySet()) {
                PlayerLevelEnum level = entry.getKey();
                int count = entry.getValue();
                suggestions.add(String.format("客队%s球员多%d人，建议调整%d人到主队", 
                    level.getName(), count, (count + 1) / 2));
            }
            
            expectedImprovement = calculateLevelImprovement(analysis);
        }

        return TeamAssignmentResult.BalanceEvaluation.OptimizationSuggestion.builder()
                .suggestionType(suggestionType)
                .description(String.format("主队等级分布: %s; 客队等级分布: %s",
                           LevelBalanceUtils.formatLevelDistribution(homeLevels),
                           LevelBalanceUtils.formatLevelDistribution(guestLevels)))
                .suggestedActions(suggestions)
                .expectedImprovement(expectedImprovement)
                .build();
    }

    /**
     * 计算球员列表的平均能力值
     */
    private double calculateAverageAbility(List<PlayerRegistrationInfo> players) {
        if (players.isEmpty()) {
            return 0.0;
        }

        double totalAbility = calculateTotalAbility(players);
        return totalAbility / players.size();
    }

    /**
     * 计算球员列表的总能力值
     */
    private double calculateTotalAbility(List<PlayerRegistrationInfo> players) {
        return players.stream()
                .mapToDouble(this::getPlayerAbility)
                .sum();
    }

    /**
     * 获取球员能力值
     */
    private double getPlayerAbility(PlayerRegistrationInfo playerInfo) {
        PlayerDO player = playerInfo.getPlayer();
        if (player == null || player.getRatings() == null) {
            return 0.0;
        }
        return player.getRatings().doubleValue();
    }

    /**
     * 计算预期改善程度
     */
    private int calculateExpectedImprovement(double higherAbility, double lowerAbility) {
        double currentDiff = higherAbility - lowerAbility;
        double maxPossibleImprovement = currentDiff / 2; // 理想情况下能改善一半差异
        double improvementPercentage = maxPossibleImprovement / higherAbility;
        return Math.min(50, (int) (improvementPercentage * 100)); // 最多50分改善
    }

    /**
     * 计算等级调整的预期改善
     */
    private int calculateLevelImprovement(LevelBalanceUtils.LevelImbalanceAnalysis analysis) {
        PlayerLevelEnum mostImbalanced = analysis.getMostImbalancedLevel();
        if (mostImbalanced == null) {
            return 0;
        }
        
        // 根据最不平衡等级的权重计算预期改善
        int totalImbalance = 0;
        for (Map.Entry<PlayerLevelEnum, Integer> entry : analysis.getHomeAdvantages().entrySet()) {
            totalImbalance += entry.getValue();
        }
        for (Map.Entry<PlayerLevelEnum, Integer> entry : analysis.getGuestAdvantages().entrySet()) {
            totalImbalance += entry.getValue();
        }
        
        // 每调整一个球员可以改善的分数
        return Math.min(totalImbalance * 5, 25);
    }

} 
