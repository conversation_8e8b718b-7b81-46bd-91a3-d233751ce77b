package cn.iocoder.yudao.module.operation.service.strategy.impl;

import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;

import cn.iocoder.yudao.module.operation.controller.admin.game.vo.GameSaveReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.GameStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.activity.FriendlyPaymentTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.strategy.AbstractActivityStrategy;
import cn.iocoder.yudao.module.operation.service.strategy.util.FeeCalculationUtils;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants.*;

/**
 * 友谊赛活动策略实现
 *
 * 继承AbstractActivityStrategy，复用通用逻辑：
 * - 基础校验、支付处理、组局检查等
 * - 重写友谊赛特定的业务逻辑
 */
@Service
@Slf4j
public class FriendlyActivityStrategy extends AbstractActivityStrategy {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private TeamService teamService;

    @Resource
    private GameService gameService;

    @Resource
    private GamePlayerSyncService gamePlayerSyncService;

    // 默认友谊赛支付方式
    private static final int DEFAULT_PAYMENT_TYPE = FriendlyPaymentTypeEnum.TEAM_TOTAL.getType();

    @Override
    public ActivityTypeEnum getActivityType() {
        return ActivityTypeEnum.FRIENDLY;
    }

    @Override
    public Integer calculateEffectiveFee(@NotNull ActivityDO activity, @NotNull FeeCalculationContext context) {
        // 友谊赛必须提供队伍ID
        Long teamId = context.getTeamId();
        if (teamId == null) {
            log.error("[calculateEffectiveFee][友谊赛][活动 {}] 缺少必需的队伍ID", activity.getId());
            throw exception(TEAM_ID_REQUIRED_FOR_FRIENDLY_GAME, "友谊赛报名必须指定球队ID");
        }

        // 校验支付方式
        Integer paymentTypeToUse = context.getSubmittedPaymentType();
        if (paymentTypeToUse != null && !isValidPaymentType(paymentTypeToUse)) {
            log.error("[calculateEffectiveFee][友谊赛][活动 {}] 无效的支付方式: {}", activity.getId(), paymentTypeToUse);
            throw exception(INVALID_PAYMENT_TYPE, "无效的支付方式");
        }

        // 如果没有指定支付方式，尝试从已有记录获取
        if (paymentTypeToUse == null) {
            paymentTypeToUse = getTeamPaymentType(activity.getId(), teamId);
        }

        // 如果仍然没有，使用默认支付方式
        if (paymentTypeToUse == null) {
            paymentTypeToUse = DEFAULT_PAYMENT_TYPE;
        }

        return calculateFeeByContextAndPaymentType(activity, context, paymentTypeToUse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createGamesAfterGrouping(@NotNull ActivityDO activity) {
        log.info("[createGamesAfterGrouping][友谊赛][活动 {}] 组局成功，创建比赛", activity.getId());

        try {
            // 获取所有已报名成功的报名记录
            List<RegistrationDO> paidRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                    .eq(RegistrationDO::getActivityId, activity.getId())
                    .eq(RegistrationDO::getStatus, RegistrationStatusEnum.SUCCESSFUL.getStatus())
            );

            if (paidRegistrations.isEmpty()) {
                log.warn("[createGamesAfterGrouping][友谊赛][活动 {}] 没有已报名成功的报名记录", activity.getId());
                return;
            }

            // 根据球队分组创建比赛
            Map<Long, List<RegistrationDO>> teamGroups = paidRegistrations.stream()
                .filter(reg -> reg.getTeamId() != null)
                .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

            List<Long> teamIds = new ArrayList<>(teamGroups.keySet());
            if (teamIds.size() >= 2) {
                // 友谊赛：创建队伍间的比赛
                // 根据最早报名时间确定主客队：最早报名的队伍作为主队
                List<Long> sortedTeamIds = teamGroups.entrySet().stream()
                    .sorted((entry1, entry2) -> {
                        // 获取每队最早的报名时间
                        RegistrationDO earliestReg1 = entry1.getValue().stream()
                            .min(Comparator.comparing(RegistrationDO::getRegistrationTime))
                            .orElse(null);
                        RegistrationDO earliestReg2 = entry2.getValue().stream()
                            .min(Comparator.comparing(RegistrationDO::getRegistrationTime))
                            .orElse(null);

                        if (earliestReg1 == null && earliestReg2 == null) return 0;
                        if (earliestReg1 == null) return 1;
                        if (earliestReg2 == null) return -1;

                        return earliestReg1.getRegistrationTime().compareTo(earliestReg2.getRegistrationTime());
                    })
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

                Long homeTeamId = sortedTeamIds.get(0);  // 最早报名的队伍作为主队
                Long guestTeamId = sortedTeamIds.get(1); // 第二早报名的队伍作为客队

                GameSaveReqVO gameCreateReq = new GameSaveReqVO();
                gameCreateReq.setStartTime(activity.getStartTime());
                gameCreateReq.setType(activity.getGameType());
                gameCreateReq.setStatus(GameStatusEnum.WAIT_START.getStatus());
                gameCreateReq.setPicUrl(activity.getPicUrl());
                gameCreateReq.setHomeTeamId(homeTeamId);
                gameCreateReq.setGuestTeamId(guestTeamId);

                Long gameId = gameService.createGame(gameCreateReq);

                // 更新活动的gameId
                activityMapper.updateById(
                    new ActivityDO().setId(activity.getId()).setGameId(gameId)
                );

                // 🔥 关键：同步所有已报名球员到比赛球员表
                gamePlayerSyncService.syncAllPlayersToGame(activity.getId(), gameId);

                log.info("[createGamesAfterGrouping][友谊赛][活动 {}] 比赛创建成功，gameId: {}, 主队: {}, 客队: {}，已同步球员名单",
                        activity.getId(), gameId, homeTeamId, guestTeamId);
            } else {
                log.warn("[createGamesAfterGrouping][友谊赛][活动 {}] 参赛队伍数量不足：{} 队，至少需要 2 队",
                        activity.getId(), teamIds.size());
            }
        } catch (Exception e) {
            log.error("[createGamesAfterGrouping][友谊赛][活动 {}] 创建比赛失败", activity.getId(), e);
            throw exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "创建比赛失败: " + e.getMessage());
        }
    }

    @Override
    protected int getMinRequiredPlayersForGrouping(ActivityDO activity) {
        // 友谊赛需要至少2支队伍，每队至少最低人数
        Integer minTeams = activity.getMinTeamsFriendly() != null ? activity.getMinTeamsFriendly() : 2;
        Integer minPlayersPerTeam = activity.getMinPlayersPerTeamFriendly() != null ?
            activity.getMinPlayersPerTeamFriendly() : 5;
        return minTeams * minPlayersPerTeam;
    }

    @Override
    public void handlePaymentSuccessAfterStateUpdate(ActivityDO activity, RegistrationDO registration) {
        log.info("[handlePaymentSuccessAfterStateUpdate][友谊赛][报名ID {}] 处理支付成功后的友谊赛特定逻辑",
                registration.getId());

        // 1. 优先处理组局成功后的状态转换
        handlePostGroupingRegistrationStatus(activity, registration);

        // 2. 处理球队整体支付逻辑 - 只有真正支付费用的人才触发垫付逻辑
        if (FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(registration.getPaymentType())) {
            // 检查是否为真正的垫付者（实际支付金额大于0）
            Integer actualPayPrice = registration.getActualPayPrice();
            if (actualPayPrice != null && actualPayPrice > 0) {
                log.info("[handlePaymentSuccessAfterStateUpdate][友谊赛][报名ID {}] 检测到真正垫付者，实付金额: {}分",
                        registration.getId(), actualPayPrice);
                handleTeamTotalPayment(activity.getId(), registration.getTeamId(), registration.getId());
            } else {
                log.info("[handlePaymentSuccessAfterStateUpdate][友谊赛][报名ID {}] 支付0元，无需触发垫付逻辑，实付金额: {}分",
                        registration.getId(), actualPayPrice);
            }
        }
    }

    @Override
    protected void doValidateRegistrationRequest(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId) {
        // 友谊赛必须提供队伍ID
        if (createReqVO.getTeamId() == null) {
            log.error("[doValidateRegistrationRequest][友谊赛][活动 {}] 缺少必需的队伍ID", activity.getId());
            throw exception(TEAM_ID_REQUIRED_FOR_FRIENDLY_GAME, "友谊赛报名必须指定球队ID");
        }

        // 校验支付方式
        if (createReqVO.getPaymentType() != null && !isValidPaymentType(createReqVO.getPaymentType())) {
            log.error("[doValidateRegistrationRequest][友谊赛][活动 {}] 无效的支付方式: {}",
                    activity.getId(), createReqVO.getPaymentType());
            throw exception(INVALID_PAYMENT_TYPE, "无效的支付方式");
        }

        // 校验球队是否存在
        try {
            if (teamService.getTeam(createReqVO.getTeamId()) == null) {
                throw exception(ErrorCodeConstants.TEAM_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("[doValidateRegistrationRequest][友谊赛][活动 {}] 球队ID {} 校验失败: {}",
                    activity.getId(), createReqVO.getTeamId(), e.getMessage());
            throw exception(ErrorCodeConstants.TEAM_NOT_EXISTS);
        }

        // 校验球队支付方式一致性
        validateTeamPaymentConsistency(activity.getId(), createReqVO.getTeamId(), createReqVO.getPaymentType());

        log.info("[doValidateRegistrationRequest][友谊赛] 请求参数校验通过: activityId={}, teamId={}, paymentType={}",
                activity.getId(), createReqVO.getTeamId(), createReqVO.getPaymentType());
    }






    /**
     * 根据上下文和支付方式计算费用
     */
    private Integer calculateFeeByContextAndPaymentType(ActivityDO activity, FeeCalculationContext context, Integer paymentType) {
        if (activity.getTotalFee() == null || activity.getTotalFee() <= 0) {
            log.error("[calculateFeeByContextAndPaymentType][友谊赛 {}] 总费用配置错误: {}", activity.getId(), activity.getTotalFee());
            throw exception(FRIENDLY_GAME_TOTAL_FEE_CONFIG_ERROR, "友谊赛总费用配置错误");
        }

        // 如果是球队整体支付，费用为总费用的一半
        if (FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(paymentType)) {
            // 如果球队已支付，则当前报名者无需再支付
            if (context.getTeamId() != null && checkTeamHasPaid(activity.getId(), context.getTeamId(), paymentType)) {
                log.info("[calculateFeeByContextAndPaymentType][友谊赛 {}] 球队 {} 已整体支付，当前报名费用为0",
                        activity.getId(), context.getTeamId());
                return 0;
            }
            // 使用工具类计算半队费用（整体支付向上取整）
            return feeCalculationUtils.calculateHalfFee(activity.getTotalFee(), FeeCalculationUtils.RoundingMode.CEIL);
        }

        // 如果是AA制，则费用为总费用的一半除以最低成队人数
        if (FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(paymentType)) {
            if (activity.getMinPlayersPerTeamFriendly() == null || activity.getMinPlayersPerTeamFriendly() <= 0) {
                log.error("[calculateFeeByContextAndPaymentType][友谊赛 {}] AA制模式下，每队最低人数配置错误: {}",
                        activity.getId(), activity.getMinPlayersPerTeamFriendly());
                throw exception(FRIENDLY_GAME_AA_MIN_PLAYERS_CONFIG_ERROR);
            }

            // 计算半队费用，然后分摊到最低人数（AA制收费向上取整）
            Integer halfTeamFee = feeCalculationUtils.calculateHalfFee(activity.getTotalFee(), FeeCalculationUtils.RoundingMode.CEIL);
            return feeCalculationUtils.calculatePerPlayerFee(halfTeamFee, activity.getMinPlayersPerTeamFriendly(), FeeCalculationUtils.RoundingMode.CEIL);
        }

        log.warn("[calculateFeeByContextAndPaymentType][友谊赛 {}] 未知的支付方式: {}", activity.getId(), paymentType);
        throw exception(INVALID_PAYMENT_TYPE, "无效的支付方式");
    }

    /**
     * 获取指定球队在指定活动中已确定的支付方式
     */
    public Integer getTeamPaymentType(Long activityId, Long teamId) {
        if (teamId == null) return null;
        RegistrationDO paidRegistration = registrationMapper.selectOne(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getTeamId, teamId)
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .isNotNull(RegistrationDO::getPaymentType)
                        .orderByAsc(RegistrationDO::getPayTime)
                        .last("LIMIT 1")
        );
        if (paidRegistration != null && paidRegistration.getPaymentType() != null) {
            return paidRegistration.getPaymentType();
        }
        return null;
    }

    /**
     * 检查指定球队在指定活动中是否已完成整体支付
     */
    private boolean checkTeamHasPaid(Long activityId, Long teamId, Integer paymentType) {
        List<RegistrationDO> registrations = registrationMapper.selectList(new LambdaQueryWrapper<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getTeamId, teamId)
                .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                .eq(RegistrationDO::getPaymentType, paymentType));

        return !registrations.isEmpty();
    }

    /**
     * 检查支付方式是否有效
     */
    private boolean isValidPaymentType(Integer paymentType) {
        return FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(paymentType) ||
               FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(paymentType);
    }

    /**
     * 校验球队支付方式一致性
     */
    private void validateTeamPaymentConsistency(Long activityId, Long teamId, Integer requestedPaymentType) {
        // 获取球队已确定的支付方式
        Integer existingPaymentType = getTeamPaymentType(activityId, teamId);

        if (existingPaymentType != null) {
            // 如果球队已有支付方式，新报名必须使用相同方式
            if (requestedPaymentType != null && !requestedPaymentType.equals(existingPaymentType)) {
                log.warn("[validateTeamPaymentConsistency][友谊赛 {}] 球队 {} 已确定支付方式为 {}，不能使用 {}",
                        activityId, teamId, existingPaymentType, requestedPaymentType);
                throw exception(REGISTRATION_NOT_ALLOWED_TEAM_PAID,
                        String.format("球队已确定支付方式为%s，不能使用其他支付方式",
                                existingPaymentType == 1 ? "球队整体支付" : "AA制支付"));
            }

            log.info("[validateTeamPaymentConsistency][友谊赛 {}] 球队 {} 支付方式校验通过，已确定支付方式: {}, 请求支付方式: {}",
                    activityId, teamId, existingPaymentType, requestedPaymentType);
        }
    }

    /**
     * 处理球队整体支付
     * 只有真正支付了球队费用的垫付者才会调用此方法
     */
    private void handleTeamTotalPayment(Long activityId, Long teamId, Long payingRegistrationId) {
        log.info("[handleTeamTotalPayment] 处理球队整体支付 - 活动ID: {}, 球队ID: {}, 支付者报名ID: {}",
                activityId, teamId, payingRegistrationId);

        try {
            // 获取同队所有其他已报名（但未支付）的成员
            List<RegistrationDO> sameTeamRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                    .eq(RegistrationDO::getActivityId, activityId)
                    .eq(RegistrationDO::getTeamId, teamId)
                    .ne(RegistrationDO::getId, payingRegistrationId)
                    .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.UNPAID.getStatus())
            );

            if (sameTeamRegistrations.isEmpty()) {
                log.info("[handleTeamTotalPayment] 暂无其他未支付队友，垫付逻辑完成 - 活动ID: {}, 球队ID: {}",
                        activityId, teamId);
                return;
            }

            // 将其他队员的报名状态更新为已支付（因为队友已垫付）
            for (RegistrationDO registration : sameTeamRegistrations) {
                registration.setPaymentStatus(PayStatusEnum.PAID.getStatus());
                registration.setStatus(RegistrationStatusEnum.PAID.getStatus());
                registration.setPayTime(LocalDateTime.now());
                registration.setActualPayPrice(0); // 队友已垫付，其他人实付为0

                registrationMapper.updateById(registration);

                log.info("[handleTeamTotalPayment] 更新队员报名状态为已支付 - 报名ID: {}", registration.getId());
            }

            log.info("[handleTeamTotalPayment] 球队整体支付处理完成，共更新 {} 名队友状态", sameTeamRegistrations.size());

        } catch (Exception e) {
            log.error("[handleTeamTotalPayment] 处理球队整体支付异常", e);
            throw exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "处理球队整体支付失败: " + e.getMessage());
        }
    }

    @Override
    protected void doPreGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        log.info("[doPreGroupingLogic][友谊赛][活动 {}] 执行组局前预处理", activity.getId());

        // 友谊赛的组局前逻辑比较简单，主要是验证球队数据完整性
        try {
            // 验证所有报名球队的数据完整性
            validateTeamDataIntegrity(activity.getId());

            log.info("[doPreGroupingLogic][友谊赛][活动 {}] 组局前预处理完成", activity.getId());
        } catch (Exception e) {
            log.error("[doPreGroupingLogic][友谊赛][活动 {}] 组局前预处理失败", activity.getId(), e);
            // 记录错误但不中断组局流程
        }
    }

    @Override
    protected void doPostGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        log.info("[doPostGroupingLogic][友谊赛][活动 {}] 执行组局后处理逻辑", activity.getId());

        // 调用父类的默认逻辑
        super.doPostGroupingLogic(activity, checkResult);

        // 友谊赛的差额退款在比赛开始时处理，组局时不处理
        log.info("[doPostGroupingLogic][友谊赛][活动 {}] 组局完成，差额退款将在比赛开始时处理", activity.getId());
    }

    /**
     * 验证球队数据完整性
     */
    private void validateTeamDataIntegrity(Long activityId) {
        // 获取所有已支付的报名记录
        List<RegistrationDO> paidRegistrations = registrationMapper.selectList(
            new LambdaQueryWrapper<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus())
        );

        // 按球队分组
        Map<Long, List<RegistrationDO>> teamGroups = paidRegistrations.stream()
            .filter(reg -> reg.getTeamId() != null)
            .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        // 验证球队数据
        for (Map.Entry<Long, List<RegistrationDO>> entry : teamGroups.entrySet()) {
            Long teamId = entry.getKey();
            List<RegistrationDO> teamRegistrations = entry.getValue();

            log.debug("[validateTeamDataIntegrity][友谊赛][活动 {}] 球队 {} 有 {} 名球员",
                    activityId, teamId, teamRegistrations.size());
        }
    }

    /**
     * 处理友谊赛的差额退款（AA制模式）
     */
    private void processDifferentialRefundForFriendly(ActivityDO activity, GroupingCheckResult checkResult) {
        log.info("[processDifferentialRefundForFriendly][友谊赛][活动 {}] 开始处理AA制差额退款", activity.getId());

        // 获取所有成功的非候补报名记录，按球队分组
        List<RegistrationDO> successfulRegistrations = registrationMapper.selectList(
            new LambdaQueryWrapper<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activity.getId())
                .eq(RegistrationDO::getStatus, RegistrationStatusEnum.SUCCESSFUL.getStatus())
                .eq(RegistrationDO::getIsWaitlist, false) // 排除候补球员
        );

        Map<Long, List<RegistrationDO>> teamGroups = successfulRegistrations.stream()
            .filter(reg -> reg.getTeamId() != null)
            .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        // 只有AA制的球队需要处理差额退款
        for (Map.Entry<Long, List<RegistrationDO>> entry : teamGroups.entrySet()) {
            Long teamId = entry.getKey();
            List<RegistrationDO> teamRegistrations = entry.getValue();

            // 检查是否是AA制支付
            boolean isAAPayment = teamRegistrations.stream()
                .anyMatch(reg -> FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(reg.getPaymentType()));

            if (isAAPayment) {
                processDifferentialRefundForAATeam(activity, teamId, teamRegistrations,
                    "友谊赛组局成功，根据实际球队人数计算AA制差额退款");
            }
        }
    }

    /**
     * 处理单个AA制球队的差额退款（通用方法）
     *
     * @param activity 活动信息
     * @param teamId 球队ID
     * @param teamRegistrations 球队所有报名记录
     * @param refundReason 退款原因描述
     */
    private void processDifferentialRefundForAATeam(ActivityDO activity, Long teamId, List<RegistrationDO> teamRegistrations, String refundReason) {
        log.info("[processDifferentialRefundForAATeam][友谊赛][活动 {}] 处理球队 {} 的AA制差额退款",
                activity.getId(), teamId);

        Integer totalFee = activity.getTotalFee();
        Integer minPlayersPerTeam = activity.getMinPlayersPerTeamFriendly();

        if (totalFee == null || minPlayersPerTeam == null) {
            log.warn("[processDifferentialRefundForAATeam][友谊赛][活动 {}] 球队 {} 费用配置不完整，跳过差额退款",
                    activity.getId(), teamId);
            return;
        }

        int actualTeamSize = teamRegistrations.size();

        // 初始费用：基于最低人数的人均费用（向上取整）
        Integer halfTeamFee = feeCalculationUtils.calculateHalfFee(totalFee, FeeCalculationUtils.RoundingMode.CEIL);
        int initialFeePerPlayer = feeCalculationUtils.calculatePerPlayerFee(halfTeamFee, minPlayersPerTeam, FeeCalculationUtils.RoundingMode.CEIL);

        // 实际费用：基于实际人数的人均费用（向下取整）
        int actualFeePerPlayer = feeCalculationUtils.calculatePerPlayerFee(halfTeamFee, actualTeamSize, FeeCalculationUtils.RoundingMode.FLOOR);

        // 计算每人应退金额
        int refundPerPlayer = initialFeePerPlayer - actualFeePerPlayer;

        if (refundPerPlayer > 0) {
            log.info("[processDifferentialRefundForAATeam][友谊赛][活动 {}] 球队 {} 需要退款，每人退款：{}分",
                    activity.getId(), teamId, refundPerPlayer);

            // 为每个队员处理差额退款
            for (RegistrationDO registration : teamRegistrations) {
                try {
                    activityRefundService.processRefund(registration.getId(),
                            cn.iocoder.yudao.module.operation.enums.refund.RefundScenario.DIFFERENCE,
                            0L, // operatorId
                            refundReason);
                    log.info("[processDifferentialRefundForAATeam][友谊赛][报名 {}] 差额退款处理成功，退款金额：{}分",
                            registration.getId(), refundPerPlayer);
                } catch (Exception e) {
                    log.error("[processDifferentialRefundForAATeam][友谊赛][报名 {}] 差额退款处理失败",
                            registration.getId(), e);
                }
            }
        } else {
            log.info("[processDifferentialRefundForAATeam][友谊赛][活动 {}] 球队 {} 无需退款，每人费用：初始{}分，实际{}分",
                    activity.getId(), teamId, initialFeePerPlayer, actualFeePerPlayer);
        }
    }

    @Override
    protected int processDifferentialRefund(ActivityDO activity) {
        log.info("[processDifferentialRefund][友谊赛] 开始处理友谊赛活动 id={} 的差额退款", activity.getId());

        // 🔧 优化：使用统一的状态查询服务获取可退款的报名记录
        List<RegistrationDO> successfulRegistrations = registrationStatusQueryService.getRefundableRegistrations(
                activity.getId(), RefundScenario.DIFFERENCE);

        if (successfulRegistrations.isEmpty()) {
            log.warn("[processDifferentialRefund][友谊赛] 活动 id={} 没有找到成功报名的非候补记录", activity.getId());
            return 0;
        }

        Map<Long, List<RegistrationDO>> teamGroups = successfulRegistrations.stream()
            .filter(reg -> reg.getTeamId() != null)
            .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        int totalRefundCount = 0;
        int aaTeamsCount = 0;
        int totalPaymentTeamsCount = 0;
        int unknownPaymentTeamsCount = 0;

        // 处理每个球队的差额退款 - 增强幂等性保护
        for (Map.Entry<Long, List<RegistrationDO>> entry : teamGroups.entrySet()) {
            Long teamId = entry.getKey();
            List<RegistrationDO> teamRegistrations = entry.getValue();

            // 检查球队的支付方式，增加null安全检查
            Integer paymentType = getTeamPaymentType(activity.getId(), teamId);

            if (paymentType == null) {
                // 支付方式未确定，可能是数据异常，记录警告并跳过
                unknownPaymentTeamsCount++;
                log.warn("[processDifferentialRefund][友谊赛] 球队 id={} 支付方式未确定，跳过差额退款处理。球队成员数: {}",
                        teamId, teamRegistrations.size());
                continue;
            }

            if (FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(paymentType)) {
                // AA制支付：需要差额退款
                aaTeamsCount++;

                // 🔧 幂等性检查：检查球队是否已经处理过差额退款
                boolean teamAlreadyProcessed = teamRegistrations.stream()
                        .anyMatch(this::isDifferentialRefundAlreadyProcessed);

                if (teamAlreadyProcessed) {
                    log.info("[processDifferentialRefund][友谊赛] 球队 id={} 差额退款已处理过，跳过", teamId);
                    continue;
                }

                try {
                    // 处理这个球队的AA制差额退款
                    processDifferentialRefundForAATeam(activity, teamId, teamRegistrations,
                        String.format("友谊赛比赛开始差额退款，球队实际参赛%d人", teamRegistrations.size()));
                    totalRefundCount += teamRegistrations.size();
                    log.info("[processDifferentialRefund][友谊赛] 球队 id={} AA制差额退款处理完成，处理 {} 人",
                            teamId, teamRegistrations.size());
                } catch (Exception e) {
                    log.error("[processDifferentialRefund][友谊赛] 球队 id={} AA制差额退款处理失败", teamId, e);
                }

            } else if (FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(paymentType)) {
                // 球队整体支付：无需差额退款
                totalPaymentTeamsCount++;
                log.info("[processDifferentialRefund][友谊赛] 球队 id={} 球队整体支付，无需差额退款", teamId);

            } else {
                // 未知的支付方式
                unknownPaymentTeamsCount++;
                log.warn("[processDifferentialRefund][友谊赛] 球队 id={} 支付方式未知: {}，跳过差额退款处理", teamId, paymentType);
            }
        }

        log.info("[processDifferentialRefund][友谊赛] 活动 id={} 差额退款处理完成，AA制球队: {}支, 整体支付球队: {}支, 未知支付方式球队: {}支, 总退款人数: {}",
                activity.getId(), aaTeamsCount, totalPaymentTeamsCount, unknownPaymentTeamsCount, totalRefundCount);
        return totalRefundCount;
    }

    /**
     * 检查是否已经处理过差额退款（幂等性检查）
     *
     * @param registration 报名记录
     * @return true-已处理过，false-未处理过
     */
    private boolean isDifferentialRefundAlreadyProcessed(RegistrationDO registration) {
        // 检查是否有退款记录且退款原因包含差额退款标识
        Integer refundStatus = registration.getRefundStatus();
        String cancelReason = registration.getCancelReason();

        // 如果退款状态为部分退款或全额退款，且取消原因包含"差额退款"，则认为已处理过
        boolean hasRefundStatus = (cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(refundStatus) ||
                cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum.FULL_REFUNDED.getStatus().equals(refundStatus) ||
                cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum.REFUNDING.getStatus().equals(refundStatus));

        boolean hasDifferentialReason = cancelReason != null &&
                (cancelReason.contains("差额退款") || cancelReason.contains("实际参赛"));

        return hasRefundStatus && hasDifferentialReason;
    }

}