package cn.iocoder.yudao.module.operation.service.activity.impl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.module.operation.constant.ActivityFeeConstants;
import cn.iocoder.yudao.module.operation.constant.OperationConstants;
import cn.iocoder.yudao.module.operation.enums.PlayerLevelEnum;
import cn.iocoder.yudao.module.operation.util.PlayerLevelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityCreateReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityPageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityUpdateReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.TeamInfoPageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.TeamInfoRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.TeamBalanceReportVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.TeamRebalancePreviewVO;
import cn.iocoder.yudao.module.operation.controller.admin.template.vo.ActivityCreateFromTemplateReqVO;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;
import cn.iocoder.yudao.module.operation.controller.admin.team.vo.TeamPageReqVO;
import cn.iocoder.yudao.module.operation.controller.app.activity.vo.AppActivityCardReqVO;
import cn.iocoder.yudao.module.operation.controller.app.activity.vo.AppActivityCardResponseVO;
import cn.iocoder.yudao.module.operation.controller.app.activity.vo.AppActivityRespVO;
import cn.iocoder.yudao.module.operation.controller.app.friendgroup.vo.FriendTeamRoomVO;
import cn.iocoder.yudao.module.operation.convert.activity.ActivityConvert;
import cn.iocoder.yudao.module.operation.convert.registration.RegistrationConvert;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.friendgroup.FriendGroupDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.team.TeamDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.template.ActivityTemplateDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.GameStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.activity.ActivityService;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationStatusQueryService;
import cn.iocoder.yudao.module.operation.service.friendgroup.FriendGroupService;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.strategy.validator.ActivityValidator;
import cn.iocoder.yudao.module.operation.service.strategy.ActivityStrategy;
import cn.iocoder.yudao.module.operation.service.strategy.factory.ActivityStrategyFactory;
import cn.iocoder.yudao.module.operation.service.strategy.util.FeeCalculationUtils;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.teamassignment.algorithm.TeamAssignmentAlgorithmManager;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import cn.iocoder.yudao.module.operation.util.TeamBalanceScoreCalculator;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 活动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ActivityServiceImpl implements ActivityService {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private ActivityValidator activityValidator;

    @Resource
    private PlayerService playerService;

    @Resource
    private TeamService teamService;

    @Resource
    private FriendGroupService friendGroupService;

    @Resource
    private FeeCalculationUtils feeCalculationUtils;

    @Resource
    private RegistrationStatusQueryService registrationStatusQueryService;

    @Resource
    private ActivityRefundService activityRefundService;

    @Resource
    private TeamAssignmentService teamAssignmentService;

    @Resource
    private TeamAssignmentAlgorithmManager algorithmManager;

    @Resource
    private GamePlayerSyncService gamePlayerSyncService;

    @Resource
    private WaitlistService waitlistService;

    @Resource
    private ActivityStrategyFactory activityStrategyFactory;

    @Resource
    private GameService gameService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public Long createActivity(@Valid ActivityCreateReqVO createReqVO) {
        // 校验活动时间
        validateActivityTime(null, createReqVO.getStartTime(), createReqVO.getEndTime(),
                createReqVO.getRegistrationDeadline());

        // 插入
        ActivityDO activity = ActivityConvert.INSTANCE.convert(createReqVO);

        // 设置初始状态
        LocalDateTime now = LocalDateTime.now();
        // 根据新的业务逻辑设置状态：
        // 1. 比赛未开始 -> 可以报名
        // 2. 报名截止时间仅用于组局触发，不影响状态设置
        if (activity.getStartTime() != null && now.isBefore(activity.getStartTime())) {
            // 比赛未开始，设置为报名中状态
            activity.setStatus(ActivityStatusEnum.REGISTRATION.getStatus());
        } else {
            // 比赛已开始或时间异常，设置为已完成
            activity.setStatus(ActivityStatusEnum.COMPLETED.getStatus());
        }
        activityMapper.insert(activity);
        // 返回
        return activity.getId();
    }

    @Override
    public PageResult<ActivityDO> getActivityPage(ActivityPageReqVO pageReqVO) {
        return activityMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<ActivityDO>()
                .likeIfPresent(ActivityDO::getName, pageReqVO.getName())
                .eqIfPresent(ActivityDO::getType, pageReqVO.getType())
                .eqIfPresent(ActivityDO::getStatus, pageReqVO.getStatus())
                .likeIfPresent(ActivityDO::getLocation, pageReqVO.getLocation())
                .betweenIfPresent(ActivityDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(ActivityDO::getId)); // 默认按ID倒序
    }

    @Override
    public ActivityDO getActivity(Long id) {
        ActivityDO activity = activityMapper.selectById(id);
        if (activity == null || Boolean.TRUE.equals(activity.getDeleted())) { // Check deleted flag
            return null;
        }
        return activity;
    }

    @Override
    public AppActivityRespVO getActivityDetailForApp(Long id, Long currentUserId, String inviteCode) {
        return getActivityDetailForApp(id, currentUserId, inviteCode, null);
    }

    @Override
    public AppActivityRespVO getActivityDetailForApp(Long id, Long currentUserId, String inviteCode, Long teamId) {
        ActivityDO activityDO = validateActivityExists(id);

        AppActivityRespVO respVO = new AppActivityRespVO();
        respVO.setId(activityDO.getId());
        respVO.setType(activityDO.getType());
        respVO.setStatus(activityDO.getStatus());

        // 构建各个模块的信息
        respVO.setBasicInfo(buildBasicInfo(activityDO));
        respVO.setGameConfig(buildGameConfig(activityDO));
        respVO.setFeeInfo(buildFeeInfo(activityDO, teamId));
        respVO.setSignupRelatedInfo(buildSignupRelatedInfo(activityDO, currentUserId));

        return respVO;
    }

    /**
     * 构建基础信息
     */
    private AppActivityRespVO.BasicInfoVO buildBasicInfo(ActivityDO activityDO) {
        AppActivityRespVO.BasicInfoVO basicInfo = new AppActivityRespVO.BasicInfoVO();
        basicInfo.setTitle(activityDO.getName());
        basicInfo.setStartTime(activityDO.getStartTime());
        basicInfo.setEndTime(activityDO.getEndTime());
        basicInfo.setSignupDeadline(activityDO.getRegistrationDeadline());
        basicInfo.setCoverUrl(activityDO.getPicUrl());
        basicInfo.setDescription(activityDO.getRemark());
        basicInfo.setLocation(activityDO.getLocation());
        return basicInfo;
    }

    /**
     * 构建游戏配置信息
     */
    private AppActivityRespVO.GameConfigVO buildGameConfig(ActivityDO activityDO) {
        AppActivityRespVO.GameConfigVO gameConfig = new AppActivityRespVO.GameConfigVO();
        gameConfig.setMinPlayersToStart(activityDO.getMinPlayersPerGame());
        gameConfig.setMaxPlayersTotal(activityDO.getMaxPlayersPerGame());
        gameConfig.setGameType(activityDO.getGameType());

        // 设置主客队信息（仅排位赛需要）
        if (ActivityTypeEnum.RANKING.getType().equals(activityDO.getType())) {
            setTeamInfo(gameConfig, activityDO);
        }

        // 设置队伍数量配置
        setTeamCountConfig(gameConfig, activityDO);

        return gameConfig;
    }

    /**
     * 设置主客队信息
     */
    private void setTeamInfo(AppActivityRespVO.GameConfigVO gameConfig, ActivityDO activityDO) {
        // 设置主队信息
        TeamInfo homeTeamInfo = getTeamInfo(activityDO.getHomeTeamId(), true);
        gameConfig.setHomeTeamName(homeTeamInfo.getName());
        gameConfig.setHomeTeamColor(homeTeamInfo.getColor());

        // 设置客队信息
        TeamInfo awayTeamInfo = getTeamInfo(activityDO.getGuestTeamId(), false);
        gameConfig.setAwayTeamName(awayTeamInfo.getName());
        gameConfig.setAwayTeamColor(awayTeamInfo.getColor());
    }

    /**
     * 获取球队信息的辅助方法
     */
    private TeamInfo getTeamInfo(Long teamId, boolean isHomeTeam) {
        if (teamId != null) {
            TeamDO team = teamService.getTeam(teamId);
            if (team != null) {
                String color = isHomeTeam
                        ? (StringUtils.hasText(team.getHomeColor()) ? team.getHomeColor()
                        : ActivityFeeConstants.DEFAULT_HOME_TEAM_COLOR)
                        : (StringUtils.hasText(team.getGuestColor()) ? team.getGuestColor()
                        : ActivityFeeConstants.DEFAULT_AWAY_TEAM_COLOR);
                return new TeamInfo(team.getName(), color);
            }
        }

        // 默认值
        String defaultName = isHomeTeam ? "主队" : "客队";
        String defaultColor = isHomeTeam ? ActivityFeeConstants.DEFAULT_HOME_TEAM_COLOR
                : ActivityFeeConstants.DEFAULT_AWAY_TEAM_COLOR;
        return new TeamInfo(defaultName, defaultColor);
    }

    /**
     * 设置队伍数量配置
     */
    private void setTeamCountConfig(AppActivityRespVO.GameConfigVO gameConfig, ActivityDO activityDO) {
        if (ActivityTypeEnum.FRIENDLY.getType().equals(activityDO.getType())) {
            gameConfig.setMaxTeams(activityDO.getMinTeamsFriendly());
            gameConfig.setMinPlayersPerTeam(activityDO.getMinPlayersPerTeamFriendly());
            gameConfig.setMaxPlayersPerTeam(activityDO.getMaxPlayersPerTeamFriendly());
        } else if (ActivityTypeEnum.LEAGUE.getType().equals(activityDO.getType())) {
            gameConfig.setMaxTeams(activityDO.getMinTeamsLeague());
            gameConfig.setMinPlayersPerTeam(activityDO.getMinPlayersPerTeamLeague());
            // 联赛暂时没有每队最大人数限制，使用默认值或者最大人数与最小人数相同
            gameConfig.setMaxPlayersPerTeam(
                    activityDO.getMinPlayersPerTeamLeague() != null ? activityDO.getMinPlayersPerTeamLeague() + 10
                            : 15); // 最小人数基础上+10，或默认15
        }
    }

    /**
     * 构建费用信息，统一使用 FeeCalculationUtils 进行费用计算
     */
    private AppActivityRespVO.FeeInfoVO buildFeeInfo(ActivityDO activityDO, Long teamId) {
        AppActivityRespVO.FeeInfoVO feeInfo = new AppActivityRespVO.FeeInfoVO();

        // 基础费用信息
        feeInfo.setTotalFee(activityDO.getTotalFee());
        feeInfo.setSupportFeeAmount(ActivityFeeConstants.DEFAULT_SUPPORT_FEE);
        feeInfo.setSupportFeeDescription(ActivityFeeConstants.SUPPORT_FEE_DESCRIPTION);
        feeInfo.setVenueFeeAmount(ActivityFeeConstants.DEFAULT_VENUE_FEE);
        feeInfo.setVenueFeeDescription(ActivityFeeConstants.VENUE_FEE_DESCRIPTION);
        feeInfo.setLeagueFeePerPlayer(activityDO.getLeagueFeePerPlayer());

        // 根据活动类型计算特定费用
        if (Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType())) {
            calculateFriendlyFee(activityDO, teamId, feeInfo);
        }

        return feeInfo;
    }

    private void calculateFriendlyFee(ActivityDO activityDO, Long teamId, AppActivityRespVO.FeeInfoVO feeInfo) {
        // 友谊赛：计算每队费用和AA制预估人均费用
        if (activityDO.getTotalFee() != null && activityDO.getTotalFee() > 0) {
            // 每队费用：总费用的一半（向上取整）
            Integer teamFee = feeCalculationUtils.calculateHalfFee(
                    activityDO.getTotalFee(),
                    FeeCalculationUtils.RoundingMode.CEIL);
            feeInfo.setTeamFee(teamFee);

            // AA制预估人均费用：每队费用除以最低人数（向上取整）
            if (activityDO.getMinPlayersPerTeamFriendly() != null
                    && activityDO.getMinPlayersPerTeamFriendly() > 0) {
                Integer teamAAEstimatedFeePerPlayer = feeCalculationUtils.calculatePerPlayerFee(
                        teamFee,
                        activityDO.getMinPlayersPerTeamFriendly(),
                        FeeCalculationUtils.RoundingMode.CEIL);
                feeInfo.setTeamAAEstimatedFeePerPlayer(teamAAEstimatedFeePerPlayer);
            }
        }

        // 获取球队已确定的支付方式（友谊赛特有逻辑）
        if (teamId != null) {
            try {
                ActivityStrategy strategy = activityStrategyFactory.getStrategy(ActivityTypeEnum.FRIENDLY);
                Integer teamPaymentType = strategy.getTeamPaymentType(activityDO.getId(), teamId);
                feeInfo.setTeamPaymentType(teamPaymentType);
            } catch (Exception e) {
                log.warn("[buildFeeInfo] 获取球队支付方式失败: activityId={}, teamId={}",
                        activityDO.getId(), teamId, e);
                // 发生异常时不影响其他费用信息的返回
                feeInfo.setTeamPaymentType(null);
            }
        }
    }

    /**
     * 球队信息辅助类
     */
    @Getter
    private static class TeamInfo {
        private final String name;
        private final String color;

        public TeamInfo(String name, String color) {
            this.name = name;
            this.color = color;
        }

    }

    /**
     * 构建报名相关信息，包含用户上下文
     */
    private AppActivityRespVO.SignupRelatedInfoVO buildSignupRelatedInfo(ActivityDO activityDO, Long currentUserId) {
        // 查询报名记录
        List<RegistrationDO> registrations = getRegistrations(activityDO.getId());

        // 初始化数据结构
        RegistrationAnalysisResult analysisResult = analyzeRegistrations(registrations, activityDO, currentUserId);

        // 构建报名相关信息
        AppActivityRespVO.SignupRelatedInfoVO signupRelatedInfo = new AppActivityRespVO.SignupRelatedInfoVO();
        signupRelatedInfo.setCurrentPlayers(analysisResult.getCurrentPlayersCount());
        // 目前的业务场景，都是支持好友组队的
        signupRelatedInfo.setIsFriendGroupSignupAvailable(Boolean.TRUE);
        signupRelatedInfo.setEstimatedFeePerPlayer(calculateEstimatedFeePerPlayer(activityDO));
        signupRelatedInfo.setHomeTeamPlayers(analysisResult.getHomeTeamPlayers());
        signupRelatedInfo.setAwayTeamPlayers(analysisResult.getAwayTeamPlayers());
        signupRelatedInfo.setWaitlistPlayers(analysisResult.getWaitlistPlayers());
        signupRelatedInfo.setCurrentUserSignupStatus(analysisResult.getCurrentUserSignupStatus());
        signupRelatedInfo.setRegistrationId(analysisResult.getCurrentUserRegistrationId());
        signupRelatedInfo.setPayOrderId(analysisResult.getCurrentUserPayOrderId()); // 设置支付订单ID

        // 构建已报名队伍信息
        List<AppActivityRespVO.TeamSignupInfoVO> signedUpTeams = buildSignedUpTeams(activityDO,
                analysisResult.getTeamRegistrationsMap());
        signupRelatedInfo.setSignedUpTeams(signedUpTeams);

        // 设置报名成功的球队数量（满足最小人数要求的球队）
        if (isTeamBasedActivity(activityDO)) {
            long successfulTeamsCount = signedUpTeams.stream()
                    .filter(team -> Boolean.TRUE.equals(team.getIsRegistrationSuccessful()))
                    .count();
            signupRelatedInfo.setSuccessfulTeamsCount((int) successfulTeamsCount);
        } else {
            signupRelatedInfo.setSuccessfulTeamsCount(0);
        }

        // 构建好友组队房间信息
        FriendTeamRoomVO friendTeamRoom = friendGroupService.buildFriendTeamRoom(activityDO, currentUserId,
                registrations);
        signupRelatedInfo.setFriendTeamRoom(friendTeamRoom);

        return signupRelatedInfo;
    }

    /**
     * 获取活动的报名记录
     */
    private List<RegistrationDO> getRegistrations(Long activityId) {
        return registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>().eq(RegistrationDO::getActivityId, activityId));
    }

    /**
     * 分析报名记录并构建相关数据结构
     */
    private RegistrationAnalysisResult analyzeRegistrations(List<RegistrationDO> registrations,
                                                            ActivityDO activityDO,
                                                            Long currentUserId) {
        RegistrationAnalysisResult result = new RegistrationAnalysisResult();

        if (CollectionUtils.isEmpty(registrations)) {
            return result;
        }

        for (RegistrationDO reg : registrations) {
            // 检查当前用户的报名状态
            if (currentUserId != null && Objects.equals(reg.getUserId(), currentUserId)) {
                result.setCurrentUserSignupStatus(reg.getStatus());
                result.setCurrentUserRegistrationId(reg.getId());
                result.setCurrentUserPayOrderId(reg.getPayOrderId()); // 设置支付订单ID
            }

            // 🔥修复：优先判断是否为候补队员，避免候补队员被错误处理为正式队员
            if (isWaitlistRegistration(reg)) {
                AppActivityRespVO.PlayerSignupInfoVO playerInfo = RegistrationConvert.buildPlayerInfo(reg,
                        playerService);
                result.getWaitlistPlayers().add(playerInfo);

                log.debug("[analyzeRegistrations] 添加候补球员: userId={}, playerId={}, status={}, isWaitlist={}",
                        reg.getUserId(), reg.getPlayerId(), reg.getStatus(), reg.getIsWaitlist());
            }
            // 处理有效的报名记录（已支付或报名成功，且不是候补）
            else if (isActiveRegistration(reg)) {
                result.incrementCurrentPlayersCount();
                AppActivityRespVO.PlayerSignupInfoVO playerInfo = RegistrationConvert.buildPlayerInfo(reg,
                        playerService);

                // 根据活动类型分配球员到不同队伍
                assignPlayerToTeam(activityDO, reg, playerInfo, result);

                // 收集球队报名信息（友谊赛和联赛）
                collectTeamRegistrations(activityDO, reg, result);

                log.debug("[analyzeRegistrations] 添加正式队员: userId={}, playerId={}, status={}, teamAssigned={}",
                        reg.getUserId(), reg.getPlayerId(), reg.getStatus(), reg.getTeamAssigned());
            }
        }

        return result;
    }

    /**
     * 检查是否为有效的报名记录
     */
    private boolean isActiveRegistration(RegistrationDO reg) {
        return Objects.equals(reg.getStatus(), RegistrationStatusEnum.PAID.getStatus()) ||
                Objects.equals(reg.getStatus(), RegistrationStatusEnum.SUCCESSFUL.getStatus());
    }

    /**
     * 检查是否为候补报名记录
     */
    private boolean isWaitlistRegistration(RegistrationDO reg) {
        // 🔥修复：候补球员必须同时满足两个条件：
        // 1) 被标记为候补 (isWaitlist = true)
        // 2) 已支付 (status = PAID 或 SUCCESSFUL)
        // 未支付的球员不应该显示在候补队员列表中
        return Boolean.TRUE.equals(reg.getIsWaitlist()) && isActiveRegistration(reg);
    }

    /**
     * 根据活动类型将球员分配到队伍
     */
    private void assignPlayerToTeam(ActivityDO activityDO,
                                    RegistrationDO reg,
                                    AppActivityRespVO.PlayerSignupInfoVO playerInfo,
                                    RegistrationAnalysisResult result) {
        if (!Objects.equals(activityDO.getType(), ActivityTypeEnum.RANKING.getType())) {
            return;
        }

        if (Objects.equals(reg.getTeamAssigned(), activityDO.getHomeTeamId())) { // 主队
            result.getHomeTeamPlayers().add(playerInfo);
        } else if (Objects.equals(reg.getTeamAssigned(), activityDO.getGuestTeamId())) { // 客队
            result.getAwayTeamPlayers().add(playerInfo);
        }
    }

    /**
     * 收集球队报名信息
     */
    private void collectTeamRegistrations(ActivityDO activityDO,
                                          RegistrationDO reg,
                                          RegistrationAnalysisResult result) {
        if (reg.getTeamId() == null) {
            return;
        }

        if (Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType()) ||
                Objects.equals(activityDO.getType(), ActivityTypeEnum.LEAGUE.getType())) {
            result.getTeamRegistrationsMap()
                    .computeIfAbsent(reg.getTeamId(), k -> new ArrayList<>())
                    .add(reg);
        }
    }

    /**
     * 计算预估个人费用，统一使用 FeeCalculationUtils
     */
    private Integer calculateEstimatedFeePerPlayer(ActivityDO activityDO) {
        if (Objects.equals(activityDO.getType(), ActivityTypeEnum.RANKING.getType())) {
            // 排位赛：总费用除以最低开赛人数
            if (activityDO.getTotalFee() != null &&
                    activityDO.getMinPlayersPerGame() != null &&
                    activityDO.getMinPlayersPerGame() > 0) {
                return feeCalculationUtils.calculatePerPlayerFee(
                        activityDO.getTotalFee(),
                        activityDO.getMinPlayersPerGame(),
                        FeeCalculationUtils.RoundingMode.CEIL);
            }
        } else if (Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType())) {
            // 友谊赛：每队费用除以最低人数（AA制预估）
            if (activityDO.getTotalFee() != null &&
                    activityDO.getMinPlayersPerTeamFriendly() != null &&
                    activityDO.getMinPlayersPerTeamFriendly() > 0) {
                // 先计算每队费用
                Integer teamFee = feeCalculationUtils.calculateHalfFee(
                        activityDO.getTotalFee(),
                        FeeCalculationUtils.RoundingMode.CEIL);
                // 再计算AA制人均费用
                return feeCalculationUtils.calculatePerPlayerFee(
                        teamFee,
                        activityDO.getMinPlayersPerTeamFriendly(),
                        FeeCalculationUtils.RoundingMode.CEIL);
            }
        }
        return null;
    }

    /**
     * 报名记录分析结果
     */
    @Data
    private static class RegistrationAnalysisResult {
        // Getter methods
        private int currentPlayersCount = 0;
        private Long currentUserRegistrationId;
        private Integer currentUserSignupStatus = 0;
        private Long currentUserPayOrderId;
        private final List<AppActivityRespVO.PlayerSignupInfoVO> homeTeamPlayers = new ArrayList<>();
        private final List<AppActivityRespVO.PlayerSignupInfoVO> awayTeamPlayers = new ArrayList<>();
        private final List<AppActivityRespVO.PlayerSignupInfoVO> waitlistPlayers = new ArrayList<>();
        private final Map<Long, List<RegistrationDO>> teamRegistrationsMap = new HashMap<>();

        public void incrementCurrentPlayersCount() {
            this.currentPlayersCount++;
        }

    }

    /**
     * 构建已报名队伍信息
     * 返回所有报名的球队，并标明哪些球队满足最小人数要求（报名成功）
     */
    private List<AppActivityRespVO.TeamSignupInfoVO> buildSignedUpTeams(ActivityDO activityDO,
                                                                        Map<Long, List<RegistrationDO>> teamRegistrationsMap) {
        if (!isTeamBasedActivity(activityDO)) {
            return new ArrayList<>();
        }

        List<AppActivityRespVO.TeamSignupInfoVO> signedUpTeams = new ArrayList<>();

        // 获取活动类型对应的最小人数要求
        Integer minPlayersRequired = getMinPlayersPerTeam(activityDO);

        for (Map.Entry<Long, List<RegistrationDO>> entry : teamRegistrationsMap.entrySet()) {
            Long teamId = entry.getKey();
            List<RegistrationDO> teamRegs = entry.getValue();

            // 统计该球队已支付的球员数量
            long paidPlayersCount = teamRegs.stream()
                    .filter(this::isActiveRegistration)
                    .count();

            // 构建球队信息（不管是否满足最小人数要求）
            AppActivityRespVO.TeamSignupInfoVO teamInfoVO = buildTeamSignupInfo(teamId, teamRegs);

            // 设置是否报名成功（满足最小人数要求）
            teamInfoVO.setIsRegistrationSuccessful(paidPlayersCount >= minPlayersRequired);

            signedUpTeams.add(teamInfoVO);
        }

        return signedUpTeams;
    }

    /**
     * 获取活动类型对应的最小人数要求
     */
    private Integer getMinPlayersPerTeam(ActivityDO activityDO) {
        if (Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType())) {
            // 友谊赛：使用 minPlayersPerTeamFriendly，默认6人
            return activityDO.getMinPlayersPerTeamFriendly() != null ?
                    activityDO.getMinPlayersPerTeamFriendly() : 6;
        } else if (Objects.equals(activityDO.getType(), ActivityTypeEnum.LEAGUE.getType())) {
            // 联赛：使用 minPlayersPerTeamLeague，默认8人
            return activityDO.getMinPlayersPerTeamLeague() != null ?
                    activityDO.getMinPlayersPerTeamLeague() : 8;
        }
        return 5; // 默认值
    }

    /**
     * 检查是否为基于队伍的活动
     */
    private boolean isTeamBasedActivity(ActivityDO activityDO) {
        return Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType()) ||
                Objects.equals(activityDO.getType(), ActivityTypeEnum.LEAGUE.getType());
    }

    /**
     * 构建单个队伍的报名信息
     */
    private AppActivityRespVO.TeamSignupInfoVO buildTeamSignupInfo(Long teamId, List<RegistrationDO> teamRegs) {
        AppActivityRespVO.TeamSignupInfoVO teamInfoVO = new AppActivityRespVO.TeamSignupInfoVO();
        teamInfoVO.setId(teamId);

        // 设置队伍基本信息
        setTeamBasicInfo(teamInfoVO, teamId);

        // 构建队伍成员列表
        List<AppActivityRespVO.PlayerSignupInfoVO> teamPlayers = buildTeamPlayers(teamRegs);
        teamInfoVO.setCurrentPlayers(teamPlayers.size());
        teamInfoVO.setPlayers(teamPlayers);

        return teamInfoVO;
    }

    /**
     * 设置队伍基本信息
     */
    private void setTeamBasicInfo(AppActivityRespVO.TeamSignupInfoVO teamInfoVO, Long teamId) {
        TeamDO team = teamService.getTeam(teamId);
        if (team != null) {
            teamInfoVO.setName(team.getName());
            teamInfoVO.setLogo(team.getLogo());
        } else {
            teamInfoVO.setName("球队" + teamId);
            teamInfoVO.setLogo("");
        }
    }

    /**
     * 构建队伍成员列表
     */
    private List<AppActivityRespVO.PlayerSignupInfoVO> buildTeamPlayers(List<RegistrationDO> teamRegs) {
        List<AppActivityRespVO.PlayerSignupInfoVO> teamPlayers = new ArrayList<>();

        for (RegistrationDO reg : teamRegs) {
            if (isActiveRegistration(reg)) {
                AppActivityRespVO.PlayerSignupInfoVO playerInfo = RegistrationConvert.buildPlayerInfo(reg,
                        playerService);
                teamPlayers.add(playerInfo);
            }
        }

        return teamPlayers;
    }

    private void validateActivityTime(Long id, LocalDateTime startTime, LocalDateTime endTime,
                                      LocalDateTime registrationDeadline) {
        if (startTime == null || endTime == null || registrationDeadline == null) {
            throw exception(ErrorCodeConstants.ACTIVITY_TIME_INVALID, "活动时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw exception(ErrorCodeConstants.ACTIVITY_TIME_INVALID, "活动结束时间不能早于开始时间");
        }
        if (registrationDeadline.isAfter(startTime)) {
            throw exception(ErrorCodeConstants.ACTIVITY_TIME_INVALID, "报名截止时间不能晚于活动开始时间");
        }
    }

    @Override
    public PageResult<AppActivityCardResponseVO> getActivityCardPage(AppActivityCardReqVO pageVO) {
        LambdaQueryWrapper<ActivityDO> queryWrapper = new LambdaQueryWrapperX<ActivityDO>();

        // 根据传入的状态参数进行筛选
        if (pageVO.getStatus() != null) {
            // 如果指定了状态，只查询该状态的活动
            queryWrapper.eq(ActivityDO::getStatus, pageVO.getStatus());
        } else {
            // 如果没有指定状态，查询所有可展示的活动状态（排除草稿、退款中）
            queryWrapper.in(ActivityDO::getStatus,
                    ActivityStatusEnum.NOT_STARTED.getStatus(),
                    ActivityStatusEnum.REGISTRATION.getStatus(),
                    ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus(),
                    ActivityStatusEnum.IN_PROGRESS.getStatus(),
                    ActivityStatusEnum.COMPLETED.getStatus(),
                    ActivityStatusEnum.CANCELLED.getStatus(),
                    ActivityStatusEnum.GROUPING_FAILED.getStatus()); // 添加已取消和组局失败状态
        }

        // 活动类型筛选
        queryWrapper.eq(pageVO.getActivityType() != null, ActivityDO::getType, pageVO.getActivityType());

        // 关键词搜索
        if (pageVO.getKeyword() != null && !pageVO.getKeyword().trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(ActivityDO::getName, pageVO.getKeyword().trim())
                    .or()
                    .like(ActivityDO::getLocation, pageVO.getKeyword().trim()));
        }

        // 排序处理
        String orderBy = pageVO.getOrderBy();
        String orderDirection = pageVO.getOrderDirection();

        if ("start_time".equals(orderBy)) {
            if ("DESC".equalsIgnoreCase(orderDirection)) {
                queryWrapper.orderByDesc(ActivityDO::getStartTime);
            } else {
                queryWrapper.orderByAsc(ActivityDO::getStartTime);
            }
        } else {
            // 默认按开始时间升序排列
            queryWrapper.orderByAsc(ActivityDO::getStartTime);
        }

        PageResult<ActivityDO> activityPage = activityMapper.selectPage(pageVO, queryWrapper);

        // 使用提取的方法转换活动卡片数据
        List<AppActivityCardResponseVO> cardResponseList = activityPage.getList().stream()
                .map(this::convertToActivityCardResponseVO)
                .collect(Collectors.toList());

        return new PageResult<>(cardResponseList, activityPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void updateActivity(@Valid ActivityUpdateReqVO updateReqVO) {
        // 校验活动存在
        ActivityDO existingActivity = activityValidator.validateActivityExists(updateReqVO.getId());

        // 校验是否可以更新
        activityValidator.validateActivityCanBeUpdated(existingActivity);

        // 转换更新对象
        ActivityDO updateObj = ActivityConvert.INSTANCE.convert(updateReqVO);
        // 校验字段完整性（根据活动类型）
        activityValidator.validateFieldsByType(updateObj);

        // 校验时间逻辑
        activityValidator.validateUpdateTimeLogic(existingActivity, updateObj);

        // 设置状态
        LocalDateTime now = LocalDateTime.now();
        // 根据新的业务逻辑设置状态：只要比赛未开始就设置为报名中
        if (updateObj.getStartTime() != null && now.isBefore(updateObj.getStartTime())) {
            updateObj.setStatus(ActivityStatusEnum.REGISTRATION.getStatus());
        } else {
            // 比赛已开始，设置为已完成
            updateObj.setStatus(ActivityStatusEnum.COMPLETED.getStatus());
        }

        // 执行更新
        int updateCount = activityMapper.updateById(updateObj);
        if (updateCount == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_UPDATE_FAILED, "更新活动失败");
        }

        log.info("[updateActivity][活动 {} 更新成功]", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteActivity(Long id) {
        // 校验存在
        ActivityDO activity = validateActivityExists(id);
        // 校验活动状态，只有草稿和未开始状态才能删除
        if (!Objects.equals(activity.getStatus(), ActivityStatusEnum.DRAFT.getStatus()) &&
                !Objects.equals(activity.getStatus(), ActivityStatusEnum.NOT_STARTED.getStatus())) {
            throw exception(ErrorCodeConstants.ACTIVITY_STATUS_NOT_ALLOWED_FOR_DELETE);
        }
        // 校验是否有报名记录
        long count = registrationMapper.selectCount(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, id));
        if (count > 0) {
            throw exception(ErrorCodeConstants.ACTIVITY_CANNOT_DELETE_HAS_REGISTRATIONS);
        }

        // 执行删除 (逻辑删除)
        activityMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void updateActivityStatus(Long id, Integer status) {
        // 复用校验逻辑
        ActivityDO activity = activityValidator.validateActivityExists(id);

        // 使用ActivityValidator的状态转换校验
        activityValidator.validateStatusTransition(activity.getStatus(), status);

        // 特殊处理：管理员取消活动时需要先触发退款
        if (ActivityStatusEnum.CANCELLED.getStatus().equals(status)) {
            // 🔧 最终修复：简化设计，完全依赖定时任务处理后续逻辑
            handleActivityCancellation(activity); // 只设置活动状态为REFUNDING

            log.info("[updateActivityStatus] 活动 {} 状态已设为退款中，后续退款处理由定时任务 ActivityRefundCompletionJob 负责",
                    activity.getId());
            return; // 完全依赖定时任务进行退款处理和最终状态更新
        }

        // 更新状态
        ActivityDO updateObj = new ActivityDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        int updateCount = activityMapper.updateById(updateObj);

        if (updateCount == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_UPDATE_FAILED, "更新活动状态失败");
        }

        log.info("[updateActivityStatus][活动 {} 状态更新为 {}]", id, status);
    }

    public ActivityServiceImpl getSelf() {
        return this;
    }

    /**
     * 处理管理员取消活动的逻辑
     * 优化事务边界：先更新状态，再调用外部服务，避免长事务
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleActivityCancellation(ActivityDO activity) {
        log.info("[handleActivityCancellation] 开始处理活动取消，活动ID: {}", activity.getId());

        // 🔧 修复：增加状态检查，防止重复操作
        Integer currentStatus = activity.getStatus();
        if (ActivityStatusEnum.REFUNDING.getStatus().equals(currentStatus)) {
            log.warn("[handleActivityCancellation] 活动 {} 已在退款处理中，跳过重复操作", activity.getId());
            return;
        }

        if (ActivityStatusEnum.CANCELLED.getStatus().equals(currentStatus)) {
            log.warn("[handleActivityCancellation] 活动 {} 已取消，跳过重复操作", activity.getId());
            return;
        }

        // 🔧 修复：检查是否为允许取消的状态
        if (!isActivityCancellationAllowed(currentStatus)) {
            log.error("[handleActivityCancellation] 活动 {} 当前状态 {} 不允许取消",
                    activity.getId(), currentStatus);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_STATUS_NOT_ALLOW_CANCEL);
        }

        // 1. 草稿和未开始状态通常没有有效报名，直接取消
        if (ActivityStatusEnum.DRAFT.getStatus().equals(currentStatus) ||
                ActivityStatusEnum.NOT_STARTED.getStatus().equals(currentStatus)) {
            log.info("[handleActivityCancellation] 活动 {} 状态为草稿或未开始，直接取消", activity.getId());
            getSelf().updateActivityStatusInTransaction(activity.getId(), ActivityStatusEnum.CANCELLED.getStatus());
            return;
        }

        // 2. 其他状态需要检查是否有需要退款的报名记录
        boolean hasRefundableRegistrations = checkHasRefundableRegistrations(activity.getId());

        if (!hasRefundableRegistrations) {
            // 没有需要退款的记录，直接设为CANCELLED
            log.info("[handleActivityCancellation] 活动 {} 没有需要退款的报名记录，直接取消", activity.getId());
            getSelf().updateActivityStatusInTransaction(activity.getId(), ActivityStatusEnum.CANCELLED.getStatus());
            return;
        }

        // 3. 有需要退款的记录，先设为REFUNDING状态（在当前事务中完成）
        log.info("[handleActivityCancellation] 活动 {} 有需要退款的报名记录，设为退款中状态", activity.getId());
        getSelf().updateActivityStatusInTransaction(activity.getId(), ActivityStatusEnum.REFUNDING.getStatus());
        activityRefundService.processActivityCancellationRefunds(activity.getId(), SecurityFrameworkUtils.getLoginUserId(), "管理员取消");

    }


    /**
     * 🔧 修复：检查活动状态是否允许取消 - 与前端逻辑保持一致
     */
    private boolean isActivityCancellationAllowed(Integer activityStatus) {
        // 🔧 修复：与前端保持一致，前端条件是 status <= 3
        // 允许取消的状态：草稿(0)、未开始(1)、报名中(2)
        // 以及其他有效的进行中状态：组局成功(8)、进行中(4)
        return ActivityStatusEnum.DRAFT.getStatus().equals(activityStatus) ||
                ActivityStatusEnum.NOT_STARTED.getStatus().equals(activityStatus) ||
                ActivityStatusEnum.REGISTRATION.getStatus().equals(activityStatus) ||
                ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus().equals(activityStatus) ||
                ActivityStatusEnum.IN_PROGRESS.getStatus().equals(activityStatus);
    }

    /**
     * 在独立事务中更新活动状态
     * 短事务，只做状态更新，避免长时间持锁
     * 使用REQUIRES_NEW确保事务独立，避免与外层事务冲突
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityStatusInTransaction(Long activityId, Integer status) {
        ActivityDO updateObj = new ActivityDO();
        updateObj.setId(activityId);
        updateObj.setStatus(status);
        int updateCount = activityMapper.updateById(updateObj);

        if (updateCount == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_UPDATE_FAILED, "更新活动状态失败");
        }

        log.info("[updateActivityStatusInTransaction][活动 {} 状态更新为 {}]", activityId, status);
    }

    /**
     * 检查活动是否有需要退款的报名记录
     */
    private boolean checkHasRefundableRegistrations(Long activityId) {
        // 使用注入的activityRefundService来检查
        return !activityRefundService.isActivityRefundCompleted(activityId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void completeActivity(Long id, String remark) {
        // 校验活动是否存在
        ActivityDO activity = activityValidator.validateActivityExists(id);

        // 校验活动状态，只有"进行中"的活动才能手动结束
        if (!ActivityStatusEnum.IN_PROGRESS.getStatus().equals(activity.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_STATUS_ERROR,
                    "只有进行中的活动才能手动结束，当前状态：" + activity.getStatus());
        }

        // 更新活动状态为已完成
        ActivityDO updateObj = new ActivityDO();
        updateObj.setId(id);
        updateObj.setStatus(ActivityStatusEnum.COMPLETED.getStatus());
        updateObj.setRemark(activity.getRemark() +
                (remark != null ? ("\n【管理员结束】" + remark) : "\n【管理员手动结束】"));

        int updateCount = activityMapper.updateById(updateObj);

        if (updateCount == 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_UPDATE_FAILED, "结束活动失败");
        }

        // 如果有关联的比赛，同步更新比赛状态
        if (activity.getGameId() != null) {
            try {
                completeGameIfNotFinished(activity.getGameId(), remark);
                log.info("[completeActivity][活动 {} 的比赛 {} 状态已同步更新为已完成]", id, activity.getGameId());
            } catch (Exception e) {
                log.error("[completeActivity][同步更新比赛状态失败] activityId={}, gameId={}",
                        id, activity.getGameId(), e);
                // 不影响活动状态更新，只记录日志
            }
        }

        log.info("[completeActivity][活动 {} 已手动结束] 备注: {}", id, remark);
    }

    /**
     * 如果比赛未结束，则结束比赛
     *
     * @param gameId 比赛ID
     * @param remark 结束备注
     */
    private void completeGameIfNotFinished(Long gameId, String remark) {
        try {
            // 获取比赛当前状态
            GameDO game = gameService.getGame(gameId);
            if (game == null) {
                log.warn("[completeGameIfNotFinished][比赛不存在] gameId={}", gameId);
                return;
            }

            // 检查比赛是否已经结束
            List<Integer> finishedStatusList = GameStatusEnum.getFinishedStatusList();
            if (finishedStatusList.contains(game.getStatus())) {
                log.info("[completeGameIfNotFinished][比赛已经结束，无需处理] gameId={}, currentStatus={}",
                        gameId, game.getStatus());
                return;
            }

            // 更新比赛状态为已结束
            gameService.updateGameStatusById(gameId, GameStatusEnum.END.getStatus());
            log.info("[completeGameIfNotFinished][比赛已结束] gameId={}, 备注: {}", gameId, remark);

        } catch (Exception e) {
            log.error("[completeGameIfNotFinished][结束比赛异常] gameId={}, remark={}", gameId, remark, e);
            // 重新抛出异常，让上层方法记录错误日志
            throw e;
        }
    }

    /**
     * 校验活动是否存在，如果不存在则抛出异常
     *
     * @param id 活动ID
     * @return 活动对象
     */
    private ActivityDO validateActivityExists(Long id) {
        ActivityDO activity = activityMapper.selectById(id);
        if (activity == null || Boolean.TRUE.equals(activity.getDeleted())) {
            throw exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }
        return activity;
    }

    @Override
    public TeamInfoRespVO getTeamInfo(Long teamId) {
        // 使用TeamService获取球队信息
        try {
            if (teamId == null) {
                return null;
            }

            // 通过TeamService获取球队详情
            TeamDO team = teamService.getTeam(teamId);
            if (team == null) {
                log.warn("[getTeamInfo][球队不存在] teamId={}", teamId);
                return null;
            }

            // 构建返回对象
            TeamInfoRespVO teamInfo = new TeamInfoRespVO();
            teamInfo.setId(team.getId());
            teamInfo.setName(team.getName());
            teamInfo.setHomeColor(team.getHomeColor() != null ? team.getHomeColor() : "红色");
            teamInfo.setGuestColor(team.getGuestColor() != null ? team.getGuestColor() : "蓝色");
            teamInfo.setCaptainId(team.getCaptain());
            teamInfo.setLogoUrl(team.getLogo());

            // 获取队长名称
            if (team.getCaptain() != null) {
                try {
                    PlayerDO captain = playerService.getPlayer(team.getCaptain());
                    if (captain != null) {
                        teamInfo.setCaptainName(captain.getName());
                    }
                } catch (Exception e) {
                    log.warn("[getTeamInfo][获取队长信息失败] captainId={}", team.getCaptain(), e);
                    teamInfo.setCaptainName("队长" + team.getCaptain());
                }
            }

            // 获取球队成员数量 - 这里简化处理，实际应该查询球队成员表
            teamInfo.setMemberCount(10); // 默认值，实际应该从数据库查询

            return teamInfo;
        } catch (Exception e) {
            log.error("[getTeamInfo][获取球队信息异常] teamId={}", teamId, e);
            // 返回默认值以保证服务稳定性
            TeamInfoRespVO defaultTeamInfo = new TeamInfoRespVO();
            defaultTeamInfo.setId(teamId);
            defaultTeamInfo.setName("默认球队 #" + teamId);
            defaultTeamInfo.setHomeColor("红色");
            defaultTeamInfo.setGuestColor("蓝色");
            defaultTeamInfo.setCaptainId(1001L);
            defaultTeamInfo.setCaptainName("队长名称");
            defaultTeamInfo.setLogoUrl("https://example.com/logo.jpg");
            defaultTeamInfo.setMemberCount(10);
            return defaultTeamInfo;
        }
    }

    @Override
    public PageResult<TeamInfoRespVO> getTeamInfoPage(@Valid TeamInfoPageReqVO pageVO) {
        // 使用TeamService获取球队分页信息
        try {
            // 将TeamInfoPageReqVO转换为TeamPageReqVO
            TeamPageReqVO teamPageReqVO = convertToTeamPageReqVO(pageVO);

            // 通过TeamService获取分页数据
            PageResult<TeamDO> teamPage = teamService.getTeamPage(teamPageReqVO);

            if (CollectionUtils.isEmpty(teamPage.getList())) {
                return new PageResult<>(new ArrayList<>(), teamPage.getTotal());
            }

            // 批量查询所有队长信息，避免N+1查询
            Set<Long> captainIds = teamPage.getList().stream()
                    .map(TeamDO::getCaptain)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, PlayerDO> captainMap = new HashMap<>();
            if (!captainIds.isEmpty()) {
                // 批量查询队长信息
                List<PlayerDO> captains = captainIds.stream()
                        .map(captainId -> {
                            try {
                                return playerService.getPlayer(captainId);
                            } catch (Exception e) {
                                log.warn("[getTeamInfoPage][批量获取队长信息失败] captainId={}", captainId, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                captainMap = captains.stream()
                        .collect(Collectors.toMap(PlayerDO::getId, player -> player));
            }

            // 转换为响应对象
            List<TeamInfoRespVO> teamInfoList = new ArrayList<>();
            for (TeamDO team : teamPage.getList()) {
                TeamInfoRespVO teamInfo = new TeamInfoRespVO();
                teamInfo.setId(team.getId());
                teamInfo.setName(team.getName());
                teamInfo.setHomeColor(team.getHomeColor() != null ? team.getHomeColor() : "红色");
                teamInfo.setGuestColor(team.getGuestColor() != null ? team.getGuestColor() : "蓝色");
                teamInfo.setCaptainId(team.getCaptain());
                teamInfo.setLogoUrl(team.getLogo());

                // 使用预查询的队长信息，避免重复查询
                if (team.getCaptain() != null) {
                    PlayerDO captain = captainMap.get(team.getCaptain());
                    if (captain != null) {
                        teamInfo.setCaptainName(captain.getName());
                    } else {
                        teamInfo.setCaptainName("队长" + team.getCaptain());
                    }
                } else {
                    teamInfo.setCaptainName("未设置");
                }

                // 获取球队成员数量 - 这里简化处理，实际应该查询球队成员表
                teamInfo.setMemberCount(10); // 默认值，实际应该从数据库查询

                teamInfoList.add(teamInfo);
            }

            return new PageResult<>(teamInfoList, teamPage.getTotal());
        } catch (Exception e) {
            log.error("[getTeamInfoPage][获取球队分页信息异常] pageVO={}", pageVO, e);
            // 返回空结果以保证服务稳定性
            return new PageResult<>(new ArrayList<>(), 0L);
        }
    }

    /**
     * 将TeamInfoPageReqVO转换为TeamPageReqVO
     *
     * @param teamInfoPageReqVO 球队信息分页请求对象
     * @return TeamPageReqVO 球队分页请求对象
     */
    private TeamPageReqVO convertToTeamPageReqVO(TeamInfoPageReqVO teamInfoPageReqVO) {
        TeamPageReqVO teamPageReqVO = new TeamPageReqVO();
        teamPageReqVO.setPageNo(teamInfoPageReqVO.getPageNo());
        teamPageReqVO.setPageSize(teamInfoPageReqVO.getPageSize());
        teamPageReqVO.setName(teamInfoPageReqVO.getName());
        // TeamPageReqVO可能有其他字段，根据实际情况映射
        // 注意：TeamInfoPageReqVO有captainName字段，但TeamPageReqVO可能没有对应字段
        // 这里只映射共同的字段
        return teamPageReqVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public Long createActivityFromTemplate(
            ActivityTemplateDO templateDO,
            String name,
            LocalDateTime startTime,
            LocalDateTime endTime,
            LocalDateTime registrationDeadline,
            String location,
            String picUrl,
            String remark,
            Long homeTeamId,
            Long guestTeamId,
            Integer totalFeeOverride,
            Integer leagueFeePerPlayerOverride,
            ActivityCreateFromTemplateReqVO createReqVO) {

        log.info("[createActivityFromTemplate][基于模板创建活动] 模板ID={}, 活动名称={}",
                templateDO.getId(), name);

        // 校验活动时间
        validateActivityTime(null, startTime, endTime, registrationDeadline);

        // 创建活动对象
        ActivityDO activity = new ActivityDO();

        // 基本信息
        activity.setName(name);
        activity.setType(templateDO.getType());
        activity.setStartTime(startTime);
        activity.setEndTime(endTime);
        activity.setRegistrationDeadline(registrationDeadline);
        activity.setLocation(location);
        activity.setPicUrl(picUrl);
        activity.setRemark(remark);

        // 费用配置：优先使用覆盖值，否则使用模板默认值
        activity.setTotalFee(totalFeeOverride != null ? totalFeeOverride : templateDO.getTotalFee());
        activity.setLeagueFeePerPlayer(
                leagueFeePerPlayerOverride != null ? leagueFeePerPlayerOverride : templateDO.getLeagueFeePerPlayer());

        // 人数配置：优先使用前端传入值，否则使用模板默认值
        activity.setMinPlayersPerGame(createReqVO.getMinPlayersPerGame() != null ?
                createReqVO.getMinPlayersPerGame() : templateDO.getMinPlayersPerGame());
        activity.setMaxPlayersPerGame(createReqVO.getMaxPlayersPerGame() != null ?
                createReqVO.getMaxPlayersPerGame() : templateDO.getMaxPlayersPerGame());
        activity.setMinTeamsFriendly(createReqVO.getMinTeamsFriendly() != null ?
                createReqVO.getMinTeamsFriendly() : templateDO.getMinTeamsFriendly());
        activity.setMinPlayersPerTeamFriendly(createReqVO.getMinPlayersPerTeamFriendly() != null ?
                createReqVO.getMinPlayersPerTeamFriendly() : templateDO.getMinPlayersPerTeamFriendly());
        activity.setMaxPlayersPerTeamFriendly(createReqVO.getMaxPlayersPerTeamFriendly() != null ?
                createReqVO.getMaxPlayersPerTeamFriendly() : templateDO.getMaxPlayersPerTeamFriendly());
        activity.setMinTeamsLeague(createReqVO.getMinTeamsLeague() != null ?
                createReqVO.getMinTeamsLeague() : templateDO.getMinTeamsLeague());
        activity.setMinPlayersPerTeamLeague(createReqVO.getMinPlayersPerTeamLeague() != null ?
                createReqVO.getMinPlayersPerTeamLeague() : templateDO.getMinPlayersPerTeamLeague());

        // 排位赛特有字段 - 只设置球队ID，不设置冗余信息
        activity.setHomeTeamId(homeTeamId);
        activity.setGuestTeamId(guestTeamId);

        // 设置初始状态
        LocalDateTime now = LocalDateTime.now();
        // 根据新的业务逻辑设置状态：只要比赛未开始就设置为报名中
        if (startTime != null && now.isBefore(startTime)) {
            activity.setStatus(ActivityStatusEnum.REGISTRATION.getStatus());
        } else {
            // 比赛已开始，设置为已完成
            activity.setStatus(ActivityStatusEnum.COMPLETED.getStatus());
        }

        // 插入数据库
        activityMapper.insert(activity);

        log.info("[createActivityFromTemplate][基于模板创建活动成功] 模板ID={}, 活动ID={}",
                templateDO.getId(), activity.getId());

        return activity.getId();
    }

    @Override
    @Cacheable(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES)
    public List<AppActivityCardResponseVO> getHomeRecommendedActivities(Long currentUserId, Integer limit) {
        // 构建查询条件：只查询报名中和组局成功的活动
        LambdaQueryWrapper<ActivityDO> queryWrapper = new LambdaQueryWrapperX<ActivityDO>()
                .in(ActivityDO::getStatus,
                        ActivityStatusEnum.REGISTRATION.getStatus(), // 报名中
                        ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus()) // 组局成功
                .orderByAsc(ActivityDO::getStartTime) // 按开始时间升序，最近的活动优先
                .last("LIMIT " + Math.max(1, Math.min(limit, 50))); // 限制数量，最少1个，最多50个

        // 查询活动列表
        List<ActivityDO> activities = activityMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(activities)) {
            return new ArrayList<>();
        }

        // 批量预查询报名数据，避免N+1查询
        List<Long> activityIds = activities.stream()
                .map(ActivityDO::getId)
                .collect(Collectors.toList());

        Map<Long, Integer> registrationCountMap = batchQueryRegistrationCounts(activityIds);
        Map<Long, Integer> teamCountMap = batchQueryTeamCounts(activityIds);

        // 转换为响应VO - 使用预查询数据
        List<AppActivityCardResponseVO> result = activities.stream()
                .map(activity -> convertToActivityCardResponseVOWithPreloadedData(activity, registrationCountMap,
                        teamCountMap))
                .collect(Collectors.toList());


        // 个性化推荐逻辑已预留扩展点，可根据业务需求添加：
        // 1. 根据用户历史报名记录推荐相似类型活动
        // 2. 根据用户位置推荐附近活动
        // 3. 根据用户偏好时间推荐合适时间段活动
        // 4. 根据用户技能水平推荐相应难度活动
        // 当前使用基础的时间和状态排序，确保推荐活动的时效性

        return result;
    }

    /**
     * 批量查询活动报名人数
     * 一次查询获取多个活动的报名统计，避免N+1查询
     */
    private Map<Long, Integer> batchQueryRegistrationCounts(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return new HashMap<>();
        }

        try {
            // 🔧 优化：使用统一的状态查询服务进行批量统计
            Map<Long, Integer> result = new HashMap<>();
            for (Long activityId : activityIds) {
                long count = registrationStatusQueryService.countActiveRegistrations(activityId);
                result.put(activityId, (int) count);
            }
            return result;
        } catch (Exception e) {
            log.error("[batchQueryRegistrationCounts] 批量查询报名人数失败 activityIds={}", activityIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 批量查询活动球队数量（友谊赛和联赛）
     * 一次查询获取多个活动的球队统计，避免N+1查询
     * 只统计满足最小人数要求的球队
     */
    private Map<Long, Integer> batchQueryTeamCounts(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return new HashMap<>();
        }

        try {
            // 查询所有相关报名记录，包含队伍信息
            List<RegistrationDO> teamRegistrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<RegistrationDO>()
                            .in(RegistrationDO::getActivityId, activityIds)
                            .in(RegistrationDO::getStatus,
                                    RegistrationStatusEnum.PAID.getStatus(),
                                    RegistrationStatusEnum.SUCCESSFUL.getStatus())
                            .isNotNull(RegistrationDO::getTeamId)
            );

            // 获取所有相关活动，用于获取最小人数配置
            List<ActivityDO> activities = activityMapper.selectBatchIds(activityIds);
            Map<Long, ActivityDO> activityMap = activities.stream()
                    .collect(Collectors.toMap(ActivityDO::getId, activity -> activity));

            // 按活动ID分组，然后按球队分组，统计满足最小人数要求的球队
            Map<Long, Integer> result = new HashMap<>();

            teamRegistrations.stream()
                    .collect(Collectors.groupingBy(RegistrationDO::getActivityId))
                    .forEach((activityId, activityRegs) -> {
                        ActivityDO activity = activityMap.get(activityId);
                        if (activity == null) {
                            result.put(activityId, 0);
                            return;
                        }

                        Integer minPlayersRequired = getMinPlayersPerTeam(activity);

                        // 按球队分组，统计每个球队的人数
                        Map<Long, List<RegistrationDO>> teamGroupMap = activityRegs.stream()
                                .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

                        // 统计满足最小人数要求的球队数量
                        long qualifiedTeamsCount = teamGroupMap.entrySet().stream()
                                .filter(entry -> {
                                    List<RegistrationDO> teamRegs = entry.getValue();
                                    long paidPlayersCount = teamRegs.stream()
                                            .filter(this::isActiveRegistration)
                                            .count();
                                    return paidPlayersCount >= minPlayersRequired;
                                })
                                .count();

                        result.put(activityId, (int) qualifiedTeamsCount);
                    });

            // 确保所有活动ID都有对应的值（即使是0）
            for (Long activityId : activityIds) {
                result.putIfAbsent(activityId, 0);
            }

            return result;
        } catch (Exception e) {
            log.error("[batchQueryTeamCounts] 批量查询球队数量失败 activityIds={}", activityIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 将ActivityDO转换为AppActivityCardResponseVO
     * 提取重复代码，统一处理活动卡片的数据转换逻辑
     *
     * @param activityDO 活动实体对象
     * @return 活动卡片响应VO
     */
    private AppActivityCardResponseVO convertToActivityCardResponseVO(ActivityDO activityDO) {
        AppActivityCardResponseVO cardVO = new AppActivityCardResponseVO();
        cardVO.setId(activityDO.getId());
        cardVO.setTitle(activityDO.getName());
        cardVO.setActivityType(activityDO.getType());
        cardVO.setLocation(activityDO.getLocation());
        cardVO.setStartTime(activityDO.getStartTime());
        cardVO.setRegistrationEndTime(activityDO.getRegistrationDeadline());
        cardVO.setGameType(activityDO.getGameType());
        cardVO.setPicUrl(activityDO.getPicUrl());
        cardVO.setStatus(activityDO.getStatus()); // 设置活动状态

        // 计算价格 - 根据活动类型区分处理
        Integer price = null;
        Integer maxRegisters = null;
        String priceUnit = null;

        if (ActivityTypeEnum.isRanking(activityDO.getType())) {
            // 排位赛：显示人均费用
            price = feeCalculationUtils.calculatePerPlayerFee(
                    activityDO.getTotalFee(),
                    activityDO.getMinPlayersPerGame(),
                    FeeCalculationUtils.RoundingMode.CEIL);
            priceUnit = "/人";
            maxRegisters = activityDO.getMaxPlayersPerGame();

            // 统计当前报名人数（包括候补）
            long currentRegsCount = registrationMapper.selectCount(new LambdaQueryWrapper<RegistrationDO>()
                    .eq(RegistrationDO::getActivityId, activityDO.getId())
                    .in(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus(),
                            RegistrationStatusEnum.SUCCESSFUL.getStatus()));
            cardVO.setCurrentRegisters((int) currentRegsCount);

        } else if (ActivityTypeEnum.isFriendly(activityDO.getType())) {
            // 友谊赛：显示每队费用（总费用的一半）
            if (activityDO.getTotalFee() != null) {
                price = activityDO.getTotalFee() / 2;
            }
            priceUnit = "/队";
            maxRegisters = 2; // 友谊赛最大2支球队
            cardVO.setMaxTeams(2);

            // 统计当前报名球队数量并填充球队信息 - 友谊赛
            fillTeamInfoForCard(activityDO, cardVO);

        } else if (ActivityTypeEnum.isLeague(activityDO.getType())) {
            // 联赛：显示人均费用
            price = activityDO.getLeagueFeePerPlayer();
            priceUnit = "/人";
            maxRegisters = activityDO.getMinTeamsLeague(); // 联赛显示最少球队数
            // 联赛最大队伍数通常是最小队伍数的2-4倍，这里使用16作为默认值
            cardVO.setMaxTeams(activityDO.getMinTeamsLeague() != null ? activityDO.getMinTeamsLeague() * 4 : 16);

            // 统计当前报名球队数量并填充球队信息 - 联赛
            fillTeamInfoForCard(activityDO, cardVO);
        }

        cardVO.setPrice(price);
        cardVO.setPriceUnit(priceUnit);
        cardVO.setMaxRegisters(maxRegisters);

        return cardVO;
    }

    /**
     * 将ActivityDO转换为AppActivityCardResponseVO（使用预加载数据）
     * 避免N+1查询，提高性能的版本
     *
     * @param activityDO           活动实体对象
     * @param registrationCountMap 预查询的报名人数映射
     * @param teamCountMap         预查询的球队数量映射
     * @return 活动卡片响应VO
     */
    private AppActivityCardResponseVO convertToActivityCardResponseVOWithPreloadedData(
            ActivityDO activityDO,
            Map<Long, Integer> registrationCountMap,
            Map<Long, Integer> teamCountMap) {

        AppActivityCardResponseVO cardVO = new AppActivityCardResponseVO();
        cardVO.setId(activityDO.getId());
        cardVO.setTitle(activityDO.getName());
        cardVO.setActivityType(activityDO.getType());
        cardVO.setLocation(activityDO.getLocation());
        cardVO.setStartTime(activityDO.getStartTime());
        cardVO.setRegistrationEndTime(activityDO.getRegistrationDeadline());
        cardVO.setGameType(activityDO.getGameType());
        cardVO.setPicUrl(activityDO.getPicUrl());
        cardVO.setStatus(activityDO.getStatus());

        // 计算价格 - 根据活动类型区分处理
        Integer price = null;
        Integer maxRegisters = null;
        String priceUnit = null;

        if (ActivityTypeEnum.isRanking(activityDO.getType())) {
            // 排位赛：显示人均费用
            price = feeCalculationUtils.calculatePerPlayerFee(
                    activityDO.getTotalFee(),
                    activityDO.getMinPlayersPerGame(),
                    FeeCalculationUtils.RoundingMode.CEIL);
            priceUnit = "/人";
            maxRegisters = activityDO.getMaxPlayersPerGame();

            // 使用预查询的报名人数，避免重复查询
            Integer currentRegsCount = registrationCountMap.getOrDefault(activityDO.getId(), 0);
            cardVO.setCurrentRegisters(currentRegsCount);

        } else if (ActivityTypeEnum.isFriendly(activityDO.getType())) {
            // 友谊赛：显示每队费用（总费用的一半）
            if (activityDO.getTotalFee() != null) {
                price = activityDO.getTotalFee() / 2;
            }
            priceUnit = "/队";
            maxRegisters = 2; // 友谊赛最大2支球队

            // 使用预查询的球队数量，避免重复查询
            Integer currentTeamsCount = teamCountMap.getOrDefault(activityDO.getId(), 0);
            cardVO.setCurrentRegisters(currentTeamsCount);

        } else if (ActivityTypeEnum.isLeague(activityDO.getType())) {
            // 联赛：显示人均费用
            price = activityDO.getLeagueFeePerPlayer();
            priceUnit = "/人";
            maxRegisters = activityDO.getMinTeamsLeague(); // 联赛显示最少球队数

            // 使用预查询的球队数量，避免重复查询
            Integer currentTeamsCount = teamCountMap.getOrDefault(activityDO.getId(), 0);
            cardVO.setCurrentRegisters(currentTeamsCount);
        }

        cardVO.setPrice(price);
        cardVO.setPriceUnit(priceUnit);
        cardVO.setMaxRegisters(maxRegisters);

        return cardVO;
    }

    private void teamCounts(ActivityDO activityDO, AppActivityCardResponseVO cardVO) {
        // 获取所有已支付的报名记录，按球队分组
        List<RegistrationDO> teamRegistrations = registrationMapper
                .selectList(new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityDO.getId())
                        .in(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .isNotNull(RegistrationDO::getTeamId));

        // 按球队分组
        Map<Long, List<RegistrationDO>> teamRegistrationsMap = teamRegistrations.stream()
                .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        // 使用与 buildSignedUpTeams 相同的逻辑，只统计满足最小人数要求的球队
        Integer minPlayersRequired = getMinPlayersPerTeam(activityDO);

        long qualifiedTeamsCount = teamRegistrationsMap.entrySet().stream()
                .filter(entry -> {
                    List<RegistrationDO> teamRegs = entry.getValue();
                    long paidPlayersCount = teamRegs.stream()
                            .filter(this::isActiveRegistration)
                            .count();
                    return paidPlayersCount >= minPlayersRequired;
                })
                .count();

        cardVO.setCurrentRegisters((int) qualifiedTeamsCount);
    }

    /**
     * 为活动卡片填充球队信息（友谊赛和联赛）
     * 包含已报名成功的球队列表和统计数据
     */
    private void fillTeamInfoForCard(ActivityDO activityDO, AppActivityCardResponseVO cardVO) {
        // 获取所有已支付的报名记录，按球队分组
        List<RegistrationDO> teamRegistrations = registrationMapper
                .selectList(new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityDO.getId())
                        .in(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .isNotNull(RegistrationDO::getTeamId));

        // 按球队分组
        Map<Long, List<RegistrationDO>> teamRegistrationsMap = teamRegistrations.stream()
                .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        // 获取最小人数要求
        Integer minPlayersRequired = getMinPlayersPerTeam(activityDO);

        // 构建球队信息列表，只包含满足最小人数要求的球队
        List<AppActivityCardResponseVO.TeamSignupInfoVO> signedUpTeams = new ArrayList<>();

        for (Map.Entry<Long, List<RegistrationDO>> entry : teamRegistrationsMap.entrySet()) {
            Long teamId = entry.getKey();
            List<RegistrationDO> teamRegs = entry.getValue();

            // 统计该球队已支付的球员数量
            long paidPlayersCount = teamRegs.stream()
                    .filter(this::isActiveRegistration)
                    .count();

            // 只有当已支付球员数量达到最小要求时，才算作报名成功的球队
            if (paidPlayersCount >= minPlayersRequired) {
                AppActivityCardResponseVO.TeamSignupInfoVO teamInfo = new AppActivityCardResponseVO.TeamSignupInfoVO();
                teamInfo.setId(teamId);
                teamInfo.setCurrentPlayers((int) paidPlayersCount);

                // 获取球队基本信息
                try {
                    TeamDO team = teamService.getTeam(teamId);
                    if (team != null) {
                        teamInfo.setName(team.getName());
                        teamInfo.setLogo(team.getLogo());
                    } else {
                        teamInfo.setName("球队" + teamId);
                        teamInfo.setLogo("");
                    }
                } catch (Exception e) {
                    log.warn("[fillTeamInfoForCard] 获取球队信息失败: teamId={}", teamId, e);
                    teamInfo.setName("球队" + teamId);
                    teamInfo.setLogo("");
                }

                signedUpTeams.add(teamInfo);
            }
        }

        // 设置到响应对象
        cardVO.setSignedUpTeams(signedUpTeams);
        cardVO.setCurrentRegisters(signedUpTeams.size());
    }

    @Override
    public ActivityDO getActivityByGameId(Long gameId) {
        if (gameId == null) {
            return null;
        }

        LambdaQueryWrapper<ActivityDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityDO::getGameId, gameId);

        return activityMapper.selectOne(queryWrapper);
    }

    // =============== 数据管理相关方法实现 ===============

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncPlayersToGame(Long activityId) {
        ActivityDO activity = validateActivityExists(activityId);

        // 检查活动是否已组局成功
        if (activity.getGameId() == null) {
            // 未组局的活动也可以同步，但只是准备数据，不实际同步到比赛
            return "活动尚未组局成功，暂无比赛记录可同步";
        }

        // 获取所有有效的报名记录
        List<RegistrationDO> validRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus()));

        if (validRegistrations.isEmpty()) {
            return "该活动暂无有效的报名记录";
        }

        // 调用GamePlayerSyncService进行实际同步
        try {
            gamePlayerSyncService.syncAllPlayersToGame(activityId, activity.getGameId());
            log.info("[syncPlayersToGame][球员同步成功] activityId={}, gameId={}, 同步球员数={}",
                    activityId, activity.getGameId(), validRegistrations.size());
            return String.format("成功同步 %d 名球员到比赛记录", validRegistrations.size());
        } catch (Exception e) {
            log.error("[syncPlayersToGame][球员同步失败] activityId={}, gameId={}",
                    activityId, activity.getGameId(), e);
            return String.format("球员同步失败：%s", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String rebalanceTeams(Long activityId) {
        ActivityDO activity = validateActivityExists(activityId);

        // 只有排位赛才支持重新平衡
        if (!ActivityTypeEnum.RANKING.getType().equals(activity.getType())) {
            throw exception(ErrorCodeConstants.ACTIVITY_TYPE_NOT_SUPPORT_REBALANCE);
        }

        StringBuilder result = new StringBuilder();

        try {
            // 1. 处理候补队列晋升
            log.info("[rebalanceTeams][开始处理候补队列] activityId={}", activityId);
            int promotedCount = processWaitlistPromotion(activityId);
            result.append(String.format("候补晋升: %d人; ", promotedCount));

            // 2. 获取所有有效的报名记录（包括刚刚晋升的，但排除候补球员）
            List<RegistrationDO> validRegistrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<RegistrationDO>()
                            .eq(RegistrationDO::getActivityId, activityId)
                            .in(RegistrationDO::getStatus,
                                    RegistrationStatusEnum.PAID.getStatus(),
                                    RegistrationStatusEnum.SUCCESSFUL.getStatus())
                            // 关键修复：排除候补球员，确保重分队不包含候补球员
                            .and(wrapper -> wrapper
                                    .isNull(RegistrationDO::getIsWaitlist)
                                    .or()
                                    .eq(RegistrationDO::getIsWaitlist, false)
                            ));

            if (validRegistrations.isEmpty()) {
                result.append("无有效报名记录，无需重新分队");
                return result.toString();
            }

            if (validRegistrations.size() < 2) {
                result.append("报名人数不足2人，无需重新分队");
                return result.toString();
            }

            // 3. 执行重新分队算法
            log.info("[rebalanceTeams][开始重新分队] activityId={}, 有效报名数={}", activityId, validRegistrations.size());

            // 先检查是否需要重新分队
            if (!teamAssignmentService.needsReassignment(activityId)) {
                result.append("队伍平衡度良好，无需重新分队");
            } else {
                // 执行重新分队
                TeamAssignmentResult assignmentResult = teamAssignmentService.assignAllPlayers(activityId);
                result.append(String.format("重新分队: 完成(平衡度评分:%d); ", assignmentResult.getBalanceScore()));

                log.info("[rebalanceTeams][队伍重新分队成功] activityId={}, 处理球员数={}",
                        activityId, validRegistrations.size());
            }

            // 4. 同步比赛数据（如果活动已组局）
            if (activity.getGameId() != null) {
                gamePlayerSyncService.batchUpdatePlayerTeamAssignment(validRegistrations);
                result.append("比赛数据同步: 完成; ");
                log.info("[rebalanceTeams][比赛数据同步完成] activityId={}, gameId={}",
                        activityId, activity.getGameId());
            }

            // 移除数据一致性检查调用，避免错误修改已取消报名的状态
            // 重新平衡队伍不应该涉及修复数据一致性
            result.append("重新平衡完成");

            log.info("[rebalanceTeams][重新平衡完成] activityId={}, result={}", activityId, result.toString());
            return result.toString();

        } catch (Exception e) {
            log.error("[rebalanceTeams][重新平衡失败] activityId={}", activityId, e);
            result.append("处理失败: ").append(e.getMessage());
            return result.toString();
        }
    }

    /**
     * 处理候补队列晋升
     *
     * @param activityId 活动ID
     * @return 晋升人数
     */
    private int processWaitlistPromotion(Long activityId) {
        try {
            // 获取活动信息
            ActivityDO activity = getActivity(activityId);
            if (activity == null) {
                log.warn("[processWaitlistPromotion] 活动不存在，activityId={}", activityId);
                return 0;
            }

            // 调用候补服务处理晋升
            List<RegistrationDO> promoted = waitlistService.promoteFromWaitlist(activity);

            if (promoted.isEmpty()) {
                log.info("[processWaitlistPromotion] 无候补用户需要晋升，activityId={}", activityId);
                return 0;
            }

            // 为晋升的球员执行实时分队
            for (RegistrationDO registration : promoted) {
                try {
                    teamAssignmentService.assignPlayerToTeam(activityId, registration.getId());
                    log.info("[processWaitlistPromotion] 候补球员分队成功，registrationId={}, playerId={}",
                            registration.getId(), registration.getPlayerId());
                } catch (Exception e) {
                    log.error("[processWaitlistPromotion] 候补球员分队失败，registrationId={}, playerId={}",
                            registration.getId(), registration.getPlayerId(), e);
                }
            }

            log.info("[processWaitlistPromotion] 候补晋升处理完成，activityId={}, 晋升人数={}",
                    activityId, promoted.size());
            return promoted.size();

        } catch (Exception e) {
            log.error("[processWaitlistPromotion] 候补晋升处理异常，activityId={}", activityId, e);
            return 0;
        }
    }

    @Override
    public TeamBalanceReportVO getTeamBalanceReport(Long activityId) {
        ActivityDO activity = validateActivityExists(activityId);

        // 只有排位赛才有平衡度报告
        if (!ActivityTypeEnum.RANKING.getType().equals(activity.getType())) {
            throw exception(ErrorCodeConstants.ACTIVITY_TYPE_NOT_SUPPORT_BALANCE_REPORT);
        }

        try {
            // 复用优化后的数据准备逻辑，一次性获取所有数据
            TeamRebalanceDataContext context = prepareTeamRebalanceData(activityId);

            if (context.getCurrentEvaluation() == null || context.getCurrentAssignment() == null) {
                // 返回空数据的报告
                TeamBalanceReportVO emptyReport = new TeamBalanceReportVO();
                emptyReport.setActivityInfo(buildActivityBasicInfo(activity));
                emptyReport.setOverallBalanceScore(0);
                emptyReport.setReportStatus("error");
                emptyReport.setOptimizationSuggestions(Arrays.asList("暂无分队数据，请先进行球员分队"));
                return emptyReport;
            }

            // 使用预加载的数据生成报告，避免重复查询
            return generateBalanceReportFromContext(context, context.getCurrentAssignment());

        } catch (Exception e) {
            log.error("[getTeamBalanceReport][生成平衡度报告失败] activityId={}", activityId, e);

            // 返回错误状态的报告
            TeamBalanceReportVO errorReport = new TeamBalanceReportVO();
            errorReport.setActivityInfo(buildActivityBasicInfo(activity));
            errorReport.setOverallBalanceScore(0);
            errorReport.setReportStatus("error");
            errorReport.setOptimizationSuggestions(Arrays.asList("生成报告失败: " + e.getMessage()));
            return errorReport;
        }
    }

    /**
     * 构建活动基本信息
     */
    private TeamBalanceReportVO.ActivityBasicInfo buildActivityBasicInfo(ActivityDO activity) {
        TeamBalanceReportVO.ActivityBasicInfo basicInfo = new TeamBalanceReportVO.ActivityBasicInfo();
        basicInfo.setActivityId(activity.getId());
        basicInfo.setName(activity.getName());
        basicInfo.setType(activity.getType());
        return basicInfo;
    }

    /**
     * 构建队伍信息
     */
    private TeamBalanceReportVO.TeamInfo buildTeamInfo(String teamName, List<?> players,
                                                       TeamAssignmentResult.TeamStats teamStats) {
        TeamBalanceReportVO.TeamInfo teamInfo = new TeamBalanceReportVO.TeamInfo();
        teamInfo.setTeamName(teamName);
        teamInfo.setPlayerCount(players.size());

        // 构建球员列表
        List<TeamBalanceReportVO.PlayerInfo> playerInfos = new ArrayList<>();
        double totalAbility = 0;
        double totalHeight = 0;

        if (players != null && !players.isEmpty()) {
            for (Object player : players) {
                if (player instanceof TeamBalanceReportVO.PlayerInfo) {
                    // 处理已转换的球员信息对象
                    TeamBalanceReportVO.PlayerInfo playerInfo = (TeamBalanceReportVO.PlayerInfo) player;

                    // 累计统计数据
                    if (playerInfo.getRatings() != null) {
                        totalAbility += playerInfo.getRatings();
                    }
                    if (playerInfo.getHeight() != null) {
                        totalHeight += playerInfo.getHeight();
                    }

                    playerInfos.add(playerInfo);

                    log.debug("[buildTeamInfo] 添加已转换球员信息 - 队伍: {}, 球员ID: {}, 姓名: {}, 能力值: {}",
                            teamName, playerInfo.getPlayerId(), playerInfo.getName(), playerInfo.getRatings());

                } else if (player instanceof PlayerRegistrationInfo) {
                    // 处理原始的球员注册信息对象（向下兼容）
                    PlayerRegistrationInfo pri = (PlayerRegistrationInfo) player;
                    TeamBalanceReportVO.PlayerInfo playerInfo = new TeamBalanceReportVO.PlayerInfo();

                    if (pri.getPlayer() != null) {
                        playerInfo.setPlayerId(pri.getPlayer().getId());
                        playerInfo.setName(pri.getPlayer().getName());

                        // 处理能力值
                        int ratings = pri.getPlayer().getRatings() != null ? pri.getPlayer().getRatings().intValue()
                                : 0;
                        playerInfo.setRatings(ratings);
                        totalAbility += ratings;

                        // 处理身高
                        int height = pri.getPlayer().getHeight() != null ? pri.getPlayer().getHeight().intValue() : 175;
                        playerInfo.setHeight(height);
                        totalHeight += height;

                        playerInfo.setPosition(
                                pri.getPlayer().getPosition() != null ? pri.getPlayer().getPosition().toString() : "0");

                        // 获取球员胜率，如果没有比赛记录则默认为0
                        Double winRate = getPlayerWinRate(pri.getPlayer().getId());
                        playerInfo.setWinRate(winRate != null ? winRate : 0.0);

                        log.debug("[buildTeamInfo] 转换球员注册信息 - 队伍: {}, 球员ID: {}, 姓名: {}, 能力值: {}",
                                teamName, playerInfo.getPlayerId(), playerInfo.getName(), playerInfo.getRatings());
                    }

                    playerInfos.add(playerInfo);
                } else {
                    log.warn("[buildTeamInfo] 未知的球员对象类型: {} - 队伍: {}", player.getClass().getSimpleName(), teamName);
                }
            }
        }

        teamInfo.setPlayers(playerInfos);

        log.info("[buildTeamInfo] 队伍信息构建完成 - 队伍: {}, 球员数量: {}, 统计能力值: {}, 统计身高: {}",
                teamName, playerInfos.size(), totalAbility, totalHeight);

        // 设置队伍统计数据（优先使用传入的teamStats，确保数据一致性）
        TeamBalanceReportVO.TeamStats stats = new TeamBalanceReportVO.TeamStats();

        if (teamStats != null) {
            // 使用传入的teamStats，确保与算法计算的结果一致
            stats.setAverageAbility(
                    Math.round(teamStats.getAverageAbility() * OperationConstants.Precision.DECIMAL_SCALE_MULTIPLIER)
                            / OperationConstants.Precision.DECIMAL_SCALE_MULTIPLIER); // 保留两位小数
            stats.setAverageHeight(
                    Math.round(teamStats.getAverageHeight() * OperationConstants.Precision.DECIMAL_SCALE_MULTIPLIER)
                            / OperationConstants.Precision.DECIMAL_SCALE_MULTIPLIER); // 保留两位小数
            stats.setSLevelPlayerCount(teamStats.getSLevelPlayerCount());
            stats.setALevelPlayerCount(teamStats.getALevelPlayerCount());
            stats.setBLevelPlayerCount(teamStats.getBLevelPlayerCount());
            stats.setCLevelPlayerCount(teamStats.getCLevelPlayerCount());
            stats.setDLevelPlayerCount(teamStats.getDLevelPlayerCount());
            stats.setTotalAbility(
                    Math.round(teamStats.getAverageAbility() * teamInfo.getPlayerCount() * 100.0) / 100.0);
            stats.setTotalHeight(Math.round(teamStats.getAverageHeight() * teamInfo.getPlayerCount() * 100.0) / 100.0);
        } else if (teamInfo.getPlayerCount() > 0) {
            // 如果没有传入teamStats，基于实际球员数据计算，但这种情况应该避免
            double avgAbility = totalAbility / teamInfo.getPlayerCount();
            double avgHeight = totalHeight / teamInfo.getPlayerCount();

            stats.setAverageAbility(Math.round(avgAbility * 100.0) / 100.0); // 保留两位小数
            stats.setAverageHeight(Math.round(avgHeight * 100.0) / 100.0); // 保留两位小数
            stats.setTotalAbility(Math.round(totalAbility * 100.0) / 100.0);
            stats.setTotalHeight(Math.round(totalHeight * 100.0) / 100.0);

            // 重新计算所有等级球员数量（按照设计文档标准）
            int sLevelCount = 0;
            int aLevelCount = 0;
            int bLevelCount = 0;
            int cLevelCount = 0;
            int dLevelCount = 0;

            for (Object player : players) {
                if (player instanceof PlayerRegistrationInfo) {
                    PlayerRegistrationInfo pri = (PlayerRegistrationInfo) player;
                    if (pri.getPlayer() != null) {
                        int ratings = pri.getPlayer().getRatings() != null ? pri.getPlayer().getRatings().intValue()
                                : 0;
                        int height = pri.getPlayer().getHeight() != null ? pri.getPlayer().getHeight().intValue() : 0;

                        // 使用工具类进行等级判断
                        PlayerLevelEnum playerLevel = PlayerLevelUtils.getPlayerLevel(pri);

                        boolean isSLevel = playerLevel == PlayerLevelEnum.S_LEVEL;
                        boolean isALevel = playerLevel == PlayerLevelEnum.A_LEVEL;
                        boolean isBLevel = playerLevel == PlayerLevelEnum.B_LEVEL;
                        boolean isCLevel = playerLevel == PlayerLevelEnum.C_LEVEL;

                        if (isSLevel) {
                            sLevelCount++;
                        } else if (isALevel) {
                            aLevelCount++;
                        } else if (isBLevel) {
                            bLevelCount++;
                        } else if (isCLevel) {
                            cLevelCount++;
                        } else {
                            dLevelCount++; // 不满足以上任何条件的为D级
                        }
                    }
                }
            }
            stats.setSLevelPlayerCount(sLevelCount);
            stats.setALevelPlayerCount(aLevelCount);
            stats.setBLevelPlayerCount(bLevelCount);
            stats.setCLevelPlayerCount(cLevelCount);
            stats.setDLevelPlayerCount(dLevelCount);
        } else {
            // 默认值
            stats.setAverageAbility(0.0);
            stats.setAverageHeight(0.0);
            stats.setSLevelPlayerCount(0);
            stats.setALevelPlayerCount(0);
            stats.setBLevelPlayerCount(0);
            stats.setCLevelPlayerCount(0);
            stats.setDLevelPlayerCount(0);
            stats.setTotalAbility(0.0);
            stats.setTotalHeight(0.0);
        }

        teamInfo.setTeamStats(stats);

        return teamInfo;
    }

    /**
     * 生成优化建议
     */
    private List<String> generateOptimizationSuggestions(TeamAssignmentResult.BalanceEvaluation balanceEvaluation,
                                                         int overallScore) {
        List<String> suggestions = new ArrayList<>();

        if (overallScore >= 80) {
            suggestions.add("当前分队平衡度较好，各维度指标均在合理范围内");
            return suggestions;
        }

        if (balanceEvaluation.getAbilityBalance() < 70) {
            suggestions.add("能力值差距较大，建议调整高能力值球员分配");
        }
        if (balanceEvaluation.getHeightBalance() < 70) {
            suggestions.add("身高差距明显，建议平衡高个子球员分布");
        }
        if (balanceEvaluation.getPositionBalance() < 70) {
            suggestions.add("位置分布不均，建议调整各位置球员配比");
        }
        if (balanceEvaluation.getFriendGroupBalance() < 70) {
            suggestions.add("好友组分配不均，建议重新评估好友组分队");
        }
        if (balanceEvaluation.getWinRateBalance() < 70) {
            suggestions.add("胜率差距较大，建议平衡强弱球员分布");
        }

        if (suggestions.isEmpty()) {
            suggestions.add("分队平衡度有待提升，建议重新进行分队优化");
        }

        return suggestions;
    }

    /**
     * 确定报告状态
     */
    private String determineReportStatus(int overallScore) {
        if (overallScore >= 80) {
            return "good";
        } else if (overallScore >= 60) {
            return "warning";
        } else {
            return "error";
        }
    }

    /**
     * 获取球员胜率
     * TODO: 后续需要实现真实的胜率计算逻辑，从比赛记录中统计
     */
    private Double getPlayerWinRate(Long playerId) {
        // 暂时返回0，后续需要从比赛记录中计算真实胜率
        // 可以通过查询 sd_game 和 sd_team_player 表来计算球员的胜率
        return 0.0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String repairDataConsistency(Long activityId) {
        ActivityDO activity = validateActivityExists(activityId);

        List<String> repairActions = new ArrayList<>();
        int repairCount = 0;

        if (activity.getGameId() != null) {
            try {
                int repairedGameRecords = gamePlayerSyncService.repairGamePlayerConsistency(activityId);
                if (repairedGameRecords > 0) {
                    repairActions.add("修复了 " + repairedGameRecords + " 条比赛球员记录的不一致问题");
                    repairCount += repairedGameRecords;
                } else {
                    repairActions.add("比赛球员名单与报名记录一致，无需修复");
                }
            } catch (Exception e) {
                log.warn("[repairDataConsistency][比赛记录一致性检查失败] activityId={}", activityId, e);
                repairActions.add("比赛记录一致性检查失败：" + e.getMessage());
            }
        } else {
            repairActions.add("活动尚未组局，无比赛记录需要检查");
        }

        if (repairCount > 0) {
            return String.format("数据一致性修复完成，共修复 %d 条记录：\n%s",
                    repairCount, String.join("\n", repairActions));
        } else {
            return "数据一致性检查完成，未发现需要修复的问题";
        }
    }

    /**
     * 数据上下文类 - 用于在方法间传递数据，避免重复查询
     */
    @Data
    private static class TeamRebalanceDataContext {
        private ActivityDO activity;
        private List<RegistrationDO> allRegistrations;
        private List<PlayerRegistrationInfo> allPlayerInfos;
        private Map<Long, PlayerDO> playerMap;
        private Map<Long, FriendGroupDO> friendGroupMap;
        private TeamAssignmentResult currentAssignment;
        private TeamAssignmentResult.BalanceEvaluation currentEvaluation;
    }

    @Override
    public TeamRebalancePreviewVO previewTeamRebalance(Long activityId) {
        log.info("[previewTeamRebalance] 开始预览活动{}的重新分队效果", activityId);

        try {
            // 1. 一次性准备所有需要的数据，避免后续重复查询
            TeamRebalanceDataContext context = prepareTeamRebalanceData(activityId);

            // 构建预览结果
            TeamRebalancePreviewVO preview = new TeamRebalancePreviewVO();

            // 设置活动信息
            TeamRebalancePreviewVO.ActivityBasicInfo activityInfo = new TeamRebalancePreviewVO.ActivityBasicInfo();
            activityInfo.setActivityId(context.getActivity().getId());
            activityInfo.setName(context.getActivity().getName());
            activityInfo.setType(context.getActivity().getType());
            preview.setActivityInfo(activityInfo);

            // 2. 使用预加载的数据生成当前平衡报告
            TeamBalanceReportVO currentReport = generateBalanceReportFromContext(context, context.getCurrentAssignment());
            preview.setCurrentBalance(convertToTeamBalanceInfo(currentReport));

            log.info("[previewTeamRebalance] 当前平衡报告生成完成 - 活动ID: {}, 当前分数: {}, 主队人数: {}, 客队人数: {}",
                    activityId, currentReport.getOverallBalanceScore(),
                    currentReport.getHomeTeam() != null ? currentReport.getHomeTeam().getPlayerCount() : 0,
                    currentReport.getGuestTeam() != null ? currentReport.getGuestTeam().getPlayerCount() : 0);

            // 3. 使用预加载的数据模拟执行分队算法
            TeamAssignmentResult rebalanceResult = simulateTeamRebalanceWithContext(context);

            if (rebalanceResult != null && rebalanceResult.isSuccess()) {
                log.info("[previewTeamRebalance] 分队算法执行成功 - 活动ID: {}, 主队人数: {}, 客队人数: {}",
                        activityId,
                        rebalanceResult.getHomeTeamPlayers() != null ? rebalanceResult.getHomeTeamPlayers().size() : 0,
                        rebalanceResult.getGuestTeamPlayers() != null ? rebalanceResult.getGuestTeamPlayers().size() : 0);

                // 4. 使用预加载的数据生成新的平衡报告
                TeamBalanceReportVO proposedReport = generateBalanceReportFromContext(context, rebalanceResult);
                preview.setProposedBalance(convertToTeamBalanceInfo(proposedReport));

                // 5. 计算改善分数
                int currentBalanceScore = currentReport.getOverallBalanceScore();
                int proposedScore = proposedReport.getOverallBalanceScore();
                int improvementScore = proposedScore - currentBalanceScore;
                preview.setOverallImprovementScore(improvementScore);

                // 6. 生成调整计划
                List<TeamRebalancePreviewVO.PlayerChangeInfo> changes = generatePlayerChanges(currentReport, proposedReport);
                preview.setChanges(changes);

                // 7. 设置推荐等级和摘要
                preview.setRecommendationLevel(determineRecommendationLevel(improvementScore, changes.size()));
                preview.setSummary(generatePreviewSummary(improvementScore, changes.size()));

                log.info("[previewTeamRebalance] 分队算法预览完成 - 活动ID: {}, 当前分数: {}, 预期分数: {}, 改善分数: {}, 调整人数: {}",
                        activityId, currentBalanceScore, proposedScore, improvementScore, changes.size());

            } else {
                log.warn("[previewTeamRebalance] 分队算法返回失败结果 - 活动ID: {}, 原因: {}",
                        activityId, rebalanceResult != null ? rebalanceResult.getFailureReason() : "算法返回null");
                // 算法失败时使用当前状态
                preview.setProposedBalance(preview.getCurrentBalance());
                preview.setChanges(new ArrayList<>());
                preview.setOverallImprovementScore(0);
                preview.setRecommendationLevel(TeamRebalancePreviewVO.RecommendationLevel.NOT_RECOMMENDED);
                preview.setSummary("分队算法执行失败，当前分队状态已经很好，不建议调整");
            }

            return preview;

        } catch (Exception e) {
            log.error("[previewTeamRebalance] 分队算法预览异常 - 活动ID: {}", activityId, e);

            // 异常情况下返回基本信息
            TeamRebalancePreviewVO preview = new TeamRebalancePreviewVO();
            TeamRebalancePreviewVO.ActivityBasicInfo activityInfo = new TeamRebalancePreviewVO.ActivityBasicInfo();
            activityInfo.setActivityId(activityId);
            activityInfo.setName("活动 " + activityId);
            activityInfo.setType(1); // 默认类型
            preview.setActivityInfo(activityInfo);

            // 创建空的平衡信息
            TeamRebalancePreviewVO.TeamBalanceInfo emptyBalance = new TeamRebalancePreviewVO.TeamBalanceInfo();
            emptyBalance.setOverallScore(0);
            emptyBalance.setHomeTeam(new TeamRebalancePreviewVO.TeamInfo());
            emptyBalance.setGuestTeam(new TeamRebalancePreviewVO.TeamInfo());

            preview.setCurrentBalance(emptyBalance);
            preview.setProposedBalance(emptyBalance);
            preview.setChanges(new ArrayList<>());
            preview.setOverallImprovementScore(0);
            preview.setRecommendationLevel(TeamRebalancePreviewVO.RecommendationLevel.NOT_RECOMMENDED);
            preview.setSummary("分队算法执行异常，当前分队状态保持不变");

            return preview;
        }
    }

    /**
     * 一次性准备所有团队重新分配需要的数据
     * 避免在后续方法中重复查询数据库
     */
    private TeamRebalanceDataContext prepareTeamRebalanceData(Long activityId) {
        log.info("[prepareTeamRebalanceData] 开始准备团队重新分配数据 - 活动ID: {}", activityId);

        TeamRebalanceDataContext context = new TeamRebalanceDataContext();

        // 1. 查询活动信息
        context.setActivity(validateActivityExists(activityId));

        // 2. 查询所有报名记录（排除候补球员）
        context.setAllRegistrations(registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus, Arrays.asList(
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus()))
                        // 关键修复：排除候补球员，确保预览重分队不包含候补球员
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false)
                        )
        ));

        // 3. 批量查询所有球员信息
        Set<Long> userIds = context.getAllRegistrations().stream()
                .map(RegistrationDO::getUserId)
                .collect(Collectors.toSet());

        if (!userIds.isEmpty()) {
            // 使用批量查询方法
            List<PlayerDO> players = playerService.getPlayersByUserIds(new ArrayList<>(userIds));
            context.setPlayerMap(players.stream()
                    .collect(Collectors.toMap(PlayerDO::getMemberUserId, player -> player)));
        } else {
            context.setPlayerMap(new HashMap<>());
        }

        // 4. 批量查询所有好友组信息
        Set<Long> friendGroupIds = context.getAllRegistrations().stream()
                .map(RegistrationDO::getFriendGroupId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (!friendGroupIds.isEmpty()) {
            Map<Long, FriendGroupDO> friendGroupMap = new HashMap<>();
            // 批量查询好友组
            for (Long friendGroupId : friendGroupIds) {
                try {
                    FriendGroupDO friendGroup = friendGroupService.getFriendGroup(friendGroupId);
                    if (friendGroup != null) {
                        friendGroupMap.put(friendGroupId, friendGroup);
                    }
                } catch (Exception e) {
                    log.warn("[prepareTeamRebalanceData] 获取好友组信息失败, friendGroupId: {}", friendGroupId, e);
                }
            }
            context.setFriendGroupMap(friendGroupMap);
        } else {
            context.setFriendGroupMap(new HashMap<>());
        }

        // 5. 构建球员注册信息列表
        List<PlayerRegistrationInfo> playerInfos = context.getAllRegistrations().stream()
                .map(registration -> {
                    PlayerDO player = context.getPlayerMap().get(registration.getUserId());
                    if (player == null) {
                        log.warn("[prepareTeamRebalanceData] 找不到球员信息, userId: {}", registration.getUserId());
                        return null;
                    }
                    FriendGroupDO friendGroup = null;
                    if (registration.getFriendGroupId() != null) {
                        friendGroup = context.getFriendGroupMap().get(registration.getFriendGroupId());
                    }
                    return new PlayerRegistrationInfo(registration, player, friendGroup);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        context.setAllPlayerInfos(playerInfos);

        // 6. 获取当前队伍分配状态（只查询一次）
        context.setCurrentAssignment(getCurrentTeamAssignmentFromContext(context));

        // 7. 计算当前平衡度评估（只计算一次）
        context.setCurrentEvaluation(evaluateTeamBalanceFromContext(context));

        log.info("[prepareTeamRebalanceData] 数据准备完成 - 活动ID: {}, 报名人数: {}, 球员信息: {}, 好友组: {}",
                activityId, context.getAllRegistrations().size(), context.getPlayerMap().size(),
                context.getFriendGroupMap().size());

        return context;
    }

    /**
     * 基于数据上下文获取当前队伍分配状态，避免重复查询
     */
    private TeamAssignmentResult getCurrentTeamAssignmentFromContext(TeamRebalanceDataContext context) {
        try {
            ActivityDO activity = context.getActivity();

            // 分离主队和客队球员
            List<PlayerRegistrationInfo> homeTeam = context.getAllPlayerInfos().stream()
                    .filter(player -> Objects.equals(player.getRegistration().getTeamAssigned(), activity.getHomeTeamId()))
                    .collect(Collectors.toList());

            List<PlayerRegistrationInfo> guestTeam = context.getAllPlayerInfos().stream()
                    .filter(player -> Objects.equals(player.getRegistration().getTeamAssigned(), activity.getGuestTeamId()))
                    .collect(Collectors.toList());

            List<PlayerRegistrationInfo> unassigned = context.getAllPlayerInfos().stream()
                    .filter(player -> player.getRegistration().getTeamAssigned() == null)
                    .collect(Collectors.toList());

            return TeamAssignmentResult.builder()
                    .success(true)
                    .homeTeamPlayers(homeTeam)
                    .guestTeamPlayers(guestTeam)
                    .unassignedPlayers(unassigned)
                    .balanceScore(0) // 将在评估时设置
                    .build();

        } catch (Exception e) {
            log.error("[getCurrentTeamAssignmentFromContext] 获取队伍分配状态失败: activityId={}",
                    context.getActivity().getId(), e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "获取队伍分配状态失败");
        }
    }

    /**
     * 基于数据上下文评估队伍平衡度，避免重复查询和计算
     */
    private TeamAssignmentResult.BalanceEvaluation evaluateTeamBalanceFromContext(TeamRebalanceDataContext context) {
        try {
            TeamAssignmentResult currentAssignment = context.getCurrentAssignment();
            return algorithmManager.evaluateTeamBalance(
                    context.getActivity(),
                    currentAssignment.getHomeTeamPlayers(),
                    currentAssignment.getGuestTeamPlayers()
            );
        } catch (Exception e) {
            log.error("[evaluateTeamBalanceFromContext] 评估队伍平衡度异常 - 活动ID: {}",
                    context.getActivity().getId(), e);
            return null;
        }
    }

    /**
     * 使用数据上下文模拟执行重新分队，避免重复查询数据库
     */
    private TeamAssignmentResult simulateTeamRebalanceWithContext(TeamRebalanceDataContext context) {
        log.info("[simulateTeamRebalanceWithContext] 开始模拟重新分队 - 活动ID: {}", context.getActivity().getId());

        try {
            // 直接使用预加载的数据，无需重新查询
            if (context.getAllPlayerInfos().isEmpty()) {
                log.warn("[simulateTeamRebalanceWithContext] 活动 {} 没有已支付的球员", context.getActivity().getId());
                return TeamAssignmentResult.failure("没有需要分配的球员");
            }

            // 执行批量分队算法 - 使用预加载的数据
            long startTime = System.currentTimeMillis();
            TeamAssignmentResult result = algorithmManager.assignAllPlayers(context.getActivity(), context.getAllPlayerInfos());
            long executionTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isSuccess()) {
                log.info("[simulateTeamRebalanceWithContext] 模拟分队计算完成 - 活动ID: {}, 主队人数: {}, 客队人数: {}, 平衡分数: {}, 耗时: {}ms",
                        context.getActivity().getId(),
                        result.getHomeTeamPlayers() != null ? result.getHomeTeamPlayers().size() : 0,
                        result.getGuestTeamPlayers() != null ? result.getGuestTeamPlayers().size() : 0,
                        result.getBalanceScore(),
                        executionTime);
            } else if (result != null) {
                log.warn("[simulateTeamRebalanceWithContext] 模拟分队计算失败 - 活动ID: {}, 原因: {}",
                        context.getActivity().getId(), result.getFailureReason());
            } else {
                log.error("[simulateTeamRebalanceWithContext] 模拟算法返回空结果 - 活动ID: {}", context.getActivity().getId());
                return TeamAssignmentResult.failure("模拟分队算法执行异常");
            }

            return result;

        } catch (Exception e) {
            log.error("[simulateTeamRebalanceWithContext] 模拟分队异常 - 活动ID: {}", context.getActivity().getId(), e);
            return TeamAssignmentResult.failure("模拟分队处理异常: " + e.getMessage());
        }
    }

    /**
     * 基于数据上下文生成平衡报告，避免重复查询和计算
     */
    private TeamBalanceReportVO generateBalanceReportFromContext(TeamRebalanceDataContext context, TeamAssignmentResult assignmentResult) {
        log.info("[generateBalanceReportFromContext] 开始生成平衡报告 - 活动ID: {}", context.getActivity().getId());

        try {
            TeamBalanceReportVO report = new TeamBalanceReportVO();

            // 设置活动基本信息
            report.setActivityInfo(buildActivityBasicInfo(context.getActivity()));

            // 使用预加载的评估结果或重新计算
            TeamAssignmentResult.BalanceEvaluation evaluation;
            if (assignmentResult == context.getCurrentAssignment() && context.getCurrentEvaluation() != null) {
                // 如果是当前分配状态，直接使用预加载的评估结果
                evaluation = context.getCurrentEvaluation();
                log.info("[generateBalanceReportFromContext] 使用预加载的评估结果 - 活动ID: {}", context.getActivity().getId());
            } else {
                // 如果是新的分配结果，需要重新评估
                evaluation = algorithmManager.evaluateTeamBalance(
                        context.getActivity(),
                        assignmentResult.getHomeTeamPlayers(),
                        assignmentResult.getGuestTeamPlayers()
                );
                log.info("[generateBalanceReportFromContext] 重新计算评估结果 - 活动ID: {}", context.getActivity().getId());
            }

            // 设置平衡分数 - 使用加权计算
            if (evaluation != null) {
                int overallScore = TeamBalanceScoreCalculator.calculateWeightedOverallScore(evaluation);

                TeamBalanceReportVO.BalanceScores scores = new TeamBalanceReportVO.BalanceScores();
                scores.setAbilityBalance(evaluation.getAbilityBalance());
                scores.setHeightBalance(evaluation.getHeightBalance());
                scores.setPositionBalance(evaluation.getPositionBalance());
                scores.setFriendGroupBalance(evaluation.getFriendGroupBalance());
                scores.setWinRateBalance(evaluation.getWinRateBalance());
                report.setBalanceScores(scores);
                report.setOverallBalanceScore(overallScore);

                // 记录权重计算详情
                log.debug("[generateBalanceReportFromContext] {} - 活动ID: {}",
                        TeamBalanceScoreCalculator.calculateContributionBreakdown(evaluation),
                        context.getActivity().getId());
            } else {
                // 使用默认值
                TeamBalanceReportVO.BalanceScores scores = new TeamBalanceReportVO.BalanceScores();
                scores.setAbilityBalance(70);
                scores.setHeightBalance(70);
                scores.setPositionBalance(70);
                scores.setFriendGroupBalance(70);
                scores.setWinRateBalance(70);
                report.setBalanceScores(scores);
                report.setOverallBalanceScore(70);
            }

            // 构建队伍信息 - 使用预加载的球员信息
            List<TeamBalanceReportVO.PlayerInfo> homeTeamPlayerInfos = convertPlayersToPlayerInfos(
                    assignmentResult.getHomeTeamPlayers(), context.getPlayerMap());
            List<TeamBalanceReportVO.PlayerInfo> guestTeamPlayerInfos = convertPlayersToPlayerInfos(
                    assignmentResult.getGuestTeamPlayers(), context.getPlayerMap());

            TeamAssignmentResult.TeamStats homeStats = evaluation != null ? evaluation.getHomeTeamStats() : null;
            TeamAssignmentResult.TeamStats guestStats = evaluation != null ? evaluation.getGuestTeamStats() : null;

            report.setHomeTeam(buildTeamInfo("主队", homeTeamPlayerInfos, homeStats));
            report.setGuestTeam(buildTeamInfo("客队", guestTeamPlayerInfos, guestStats));

            // 设置报告状态和建议
            report.setReportStatus(determineReportStatus(report.getOverallBalanceScore()));
            if (evaluation != null) {
                report.setOptimizationSuggestions(
                        generateOptimizationSuggestions(evaluation, report.getOverallBalanceScore()));
            } else {
                report.setOptimizationSuggestions(Arrays.asList("当前分队状态良好"));
            }

            log.info("[generateBalanceReportFromContext] 平衡报告生成完成 - 活动ID: {}, 综合分数: {}",
                    context.getActivity().getId(), report.getOverallBalanceScore());

            return report;

        } catch (Exception e) {
            log.error("[generateBalanceReportFromContext] 生成平衡报告异常 - 活动ID: {}", context.getActivity().getId(), e);
            // 返回一个基本的报告
            TeamBalanceReportVO report = new TeamBalanceReportVO();
            report.setActivityInfo(buildActivityBasicInfo(context.getActivity()));
            report.setOverallBalanceScore(70);
            return report;
        }
    }

    /**
     * 批量转换球员信息，使用预加载的球员数据
     */
    private List<TeamBalanceReportVO.PlayerInfo> convertPlayersToPlayerInfos(
            List<PlayerRegistrationInfo> players, Map<Long, PlayerDO> playerMap) {
        if (players == null || players.isEmpty()) {
            return new ArrayList<>();
        }

        return players.stream()
                .map(playerReg -> {
                    TeamBalanceReportVO.PlayerInfo playerInfo = new TeamBalanceReportVO.PlayerInfo();

                    if (playerReg.getPlayer() != null) {
                        PlayerDO player = playerReg.getPlayer();
                        playerInfo.setPlayerId(player.getId());
                        playerInfo.setName(player.getName());
                        playerInfo.setRatings(player.getRatings());
                        playerInfo.setHeight(player.getHeight() != null ? player.getHeight().intValue() : 175);
                        playerInfo.setPosition(player.getPosition() != null ? player.getPosition().toString() : "0");
                        // 暂时使用固定胜率，避免额外查询
                        playerInfo.setWinRate(0.0);
                    }

                    return playerInfo;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeTeamRebalance(Long activityId) {
        // 获取预览方案
        TeamRebalancePreviewVO preview = previewTeamRebalance(activityId);

        // 记录执行日志
        if (preview.getRecommendationLevel() == TeamRebalancePreviewVO.RecommendationLevel.NOT_RECOMMENDED) {
            log.warn("[executeTeamRebalance] 强制执行重新分队 - 活动ID: {}, 当前分队已经很平衡但管理员选择强制执行", activityId);
        } else {
            log.info("[executeTeamRebalance] 执行推荐的重新分队 - 活动ID: {}, 推荐等级: {}", activityId,
                    preview.getRecommendationLevel());
        }

        // 执行实际的重新分队操作（允许强制执行）
        rebalanceTeams(activityId);
    }

    /**
     * 转换为队伍平衡信息
     */
    private TeamRebalancePreviewVO.TeamBalanceInfo convertToTeamBalanceInfo(TeamBalanceReportVO report) {
        TeamRebalancePreviewVO.TeamBalanceInfo balanceInfo = new TeamRebalancePreviewVO.TeamBalanceInfo();
        balanceInfo.setOverallScore(report.getOverallBalanceScore());

        // 转换平衡分数
        TeamRebalancePreviewVO.BalanceScores scores = new TeamRebalancePreviewVO.BalanceScores();
        scores.setAbilityBalance(report.getBalanceScores().getAbilityBalance());
        scores.setHeightBalance(report.getBalanceScores().getHeightBalance());
        scores.setPositionBalance(report.getBalanceScores().getPositionBalance());
        scores.setFriendGroupBalance(report.getBalanceScores().getFriendGroupBalance());
        scores.setWinRateBalance(report.getBalanceScores().getWinRateBalance());
        balanceInfo.setBalanceScores(scores);

        // 转换队伍信息
        balanceInfo.setHomeTeam(convertToTeamInfo(report.getHomeTeam()));
        balanceInfo.setGuestTeam(convertToTeamInfo(report.getGuestTeam()));

        return balanceInfo;
    }

    /**
     * 转换队伍信息
     */
    private TeamRebalancePreviewVO.TeamInfo convertToTeamInfo(TeamBalanceReportVO.TeamInfo teamInfo) {
        TeamRebalancePreviewVO.TeamInfo info = new TeamRebalancePreviewVO.TeamInfo();
        info.setTeamName(teamInfo.getTeamName());
        info.setPlayerCount(teamInfo.getPlayerCount());

        // 转换队伍统计
        if (teamInfo.getTeamStats() != null) {
            TeamRebalancePreviewVO.TeamStats stats = new TeamRebalancePreviewVO.TeamStats();
            stats.setAverageAbility(teamInfo.getTeamStats().getAverageAbility());
            stats.setAverageHeight(teamInfo.getTeamStats().getAverageHeight());
            stats.setSLevelPlayerCount(teamInfo.getTeamStats().getSLevelPlayerCount());
            stats.setALevelPlayerCount(teamInfo.getTeamStats().getALevelPlayerCount());
            stats.setBLevelPlayerCount(teamInfo.getTeamStats().getBLevelPlayerCount());
            stats.setCLevelPlayerCount(teamInfo.getTeamStats().getCLevelPlayerCount());
            stats.setDLevelPlayerCount(teamInfo.getTeamStats().getDLevelPlayerCount());
            stats.setTotalAbility(teamInfo.getTeamStats().getTotalAbility().longValue());
            stats.setTotalHeight(teamInfo.getTeamStats().getTotalHeight().longValue());
            info.setTeamStats(stats);
        }

        // 转换球员列表 - 防止空指针异常
        List<TeamRebalancePreviewVO.PlayerInfo> players = new ArrayList<>();
        if (teamInfo.getPlayers() != null) {
            players = teamInfo.getPlayers().stream()
                    .map(this::convertToPlayerInfo)
                    .collect(Collectors.toList());
        }
        info.setPlayers(players);

        return info;
    }

    /**
     * 转换球员信息
     */
    private TeamRebalancePreviewVO.PlayerInfo convertToPlayerInfo(TeamBalanceReportVO.PlayerInfo playerInfo) {
        TeamRebalancePreviewVO.PlayerInfo info = new TeamRebalancePreviewVO.PlayerInfo();
        info.setPlayerId(playerInfo.getPlayerId());
        info.setName(playerInfo.getName());
        info.setRatings(playerInfo.getRatings());
        info.setHeight(playerInfo.getHeight());
        info.setPosition(playerInfo.getPosition());
        info.setWinRate(playerInfo.getWinRate());
        return info;
    }

    /**
     * 确定推荐等级
     */
    private TeamRebalancePreviewVO.RecommendationLevel determineRecommendationLevel(int improvementScore,
                                                                                    int changeCount) {
        if (improvementScore <= 0 && changeCount == 0) {
            return TeamRebalancePreviewVO.RecommendationLevel.NOT_RECOMMENDED;
        } else if (improvementScore >= 10) {
            return TeamRebalancePreviewVO.RecommendationLevel.HIGH;
        } else if (improvementScore >= 5) {
            return TeamRebalancePreviewVO.RecommendationLevel.MEDIUM;
        } else {
            return TeamRebalancePreviewVO.RecommendationLevel.LOW;
        }
    }

    /**
     * 生成预览摘要
     */
    private String generatePreviewSummary(int improvementScore, int changeCount) {
        if (improvementScore <= 0 && changeCount == 0) {
            return "当前分队已经很平衡，不建议进行调整";
        } else if (changeCount == 0) {
            return "当前分队状态良好，无需调整";
        } else {
            return String.format("建议调整 %d 名球员，预期可提升平衡度 %d 分", changeCount, improvementScore);
        }
    }

    /**
     * 生成球员调整计划
     */
    private List<TeamRebalancePreviewVO.PlayerChangeInfo> generatePlayerChanges(
            TeamBalanceReportVO currentReport, TeamBalanceReportVO proposedReport) {

        List<TeamRebalancePreviewVO.PlayerChangeInfo> changes = new ArrayList<>();

        try {
            // 获取当前和预期的球员分配
            Map<Long, String> currentAssignments = extractPlayerAssignments(currentReport);
            Map<Long, String> proposedAssignments = extractPlayerAssignments(proposedReport);

            // 比较分配差异
            for (Map.Entry<Long, String> entry : proposedAssignments.entrySet()) {
                Long playerId = entry.getKey();
                String proposedTeam = entry.getValue();
                String currentTeam = currentAssignments.get(playerId);

                if (currentTeam != null && !currentTeam.equals(proposedTeam)) {
                    // 找到了需要调整的球员
                    TeamRebalancePreviewVO.PlayerChangeInfo change = new TeamRebalancePreviewVO.PlayerChangeInfo();
                    change.setPlayerId(playerId);
                    change.setPlayerName(getPlayerName(playerId, currentReport, proposedReport));
                    // 转换字符串到枚举类型
                    change.setFromTeam("主队".equals(currentTeam) ? TeamRebalancePreviewVO.PlayerChangeInfo.TeamType.HOME
                            : TeamRebalancePreviewVO.PlayerChangeInfo.TeamType.GUEST);
                    change.setToTeam("主队".equals(proposedTeam) ? TeamRebalancePreviewVO.PlayerChangeInfo.TeamType.HOME
                            : TeamRebalancePreviewVO.PlayerChangeInfo.TeamType.GUEST);
                    change.setReason("优化队伍平衡度");
                    change.setImpact("预期提升平衡度");
                    changes.add(change);
                }
            }

            log.info("[generatePlayerChanges] 生成调整计划完成，共 {} 项调整", changes.size());

        } catch (Exception e) {
            log.error("[generatePlayerChanges] 生成调整计划异常", e);
        }

        return changes;
    }

    /**
     * 提取球员分配信息
     */
    private Map<Long, String> extractPlayerAssignments(TeamBalanceReportVO report) {
        Map<Long, String> assignments = new HashMap<>();

        if (report.getHomeTeam() != null && report.getHomeTeam().getPlayers() != null) {
            for (TeamBalanceReportVO.PlayerInfo player : report.getHomeTeam().getPlayers()) {
                assignments.put(player.getPlayerId(), "主队");
            }
        }

        if (report.getGuestTeam() != null && report.getGuestTeam().getPlayers() != null) {
            for (TeamBalanceReportVO.PlayerInfo player : report.getGuestTeam().getPlayers()) {
                assignments.put(player.getPlayerId(), "客队");
            }
        }

        return assignments;
    }

    /**
     * 获取球员姓名
     */
    private String getPlayerName(Long playerId, TeamBalanceReportVO currentReport, TeamBalanceReportVO proposedReport) {
        // 先从当前报告中查找
        String name = findPlayerNameInReport(playerId, currentReport);
        if (name != null) {
            return name;
        }

        // 再从预期报告中查找
        name = findPlayerNameInReport(playerId, proposedReport);
        if (name != null) {
            return name;
        }

        // 最后从数据库查找
        try {
            PlayerDO player = playerService.getPlayer(playerId);
            return player != null ? player.getName() : "未知球员";
        } catch (Exception e) {
            log.warn("[getPlayerName] 获取球员姓名失败 - 球员ID: {}", playerId, e);
            return "未知球员";
        }
    }

    /**
     * 在报告中查找球员姓名
     */
    private String findPlayerNameInReport(Long playerId, TeamBalanceReportVO report) {
        if (report.getHomeTeam() != null && report.getHomeTeam().getPlayers() != null) {
            for (TeamBalanceReportVO.PlayerInfo player : report.getHomeTeam().getPlayers()) {
                if (Objects.equals(player.getPlayerId(), playerId)) {
                    return player.getName();
                }
            }
        }

        if (report.getGuestTeam() != null && report.getGuestTeam().getPlayers() != null) {
            for (TeamBalanceReportVO.PlayerInfo player : report.getGuestTeam().getPlayers()) {
                if (Objects.equals(player.getPlayerId(), playerId)) {
                    return player.getName();
                }
            }
        }

        return null;
    }

    @Override
    public void manualProcessDifferenceRefund(Long activityId) {
        // 获取活动信息
        ActivityDO activity = getActivity(activityId);
        if (activity == null) {
            throw exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 校验活动状态
        if (!ActivityStatusEnum.COMPLETED.getStatus().equals(activity.getStatus())) {
            throw exception(ErrorCodeConstants.ACTIVITY_STATUS_ERROR,
                    "只有已结束的活动才能手动触发差额退款，当前状态：" + activity.getStatus());
        }

        // 校验活动类型，只有排位赛和友谊赛才有差额退款
        if (!ActivityTypeEnum.RANKING.getType().equals(activity.getType()) &&
                !ActivityTypeEnum.FRIENDLY.getType().equals(activity.getType())) {
            throw exception(ErrorCodeConstants.ACTIVITY_TYPE_ERROR,
                    "只有排位赛和友谊赛支持差额退款");
        }

        log.info("[manualProcessDifferenceRefund] 手动处理活动 {} 的差额退款", activityId);

        try {
            // 获取对应的活动策略
            ActivityStrategy strategy = activityStrategyFactory.getStrategy(activity.getType());

            // 调用策略的进入"进行中"状态时的差额退款处理方法
            strategy.processStatusToInProgress(activity);

            log.info("[manualProcessDifferenceRefund] 活动 {} 的差额退款处理完成", activityId);
        } catch (Exception e) {
            log.error("[manualProcessDifferenceRefund] 活动 {} 的差额退款处理失败", activityId, e);
            throw exception(ErrorCodeConstants.ACTIVITY_STATUS_ERROR,
                    "差额退款处理失败：" + e.getMessage());
        }
    }
}