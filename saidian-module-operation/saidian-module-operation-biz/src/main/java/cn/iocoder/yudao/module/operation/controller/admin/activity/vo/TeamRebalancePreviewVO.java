package cn.iocoder.yudao.module.operation.controller.admin.activity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 队伍重新平衡预览 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 队伍重新平衡预览 Response VO")
@Data
public class TeamRebalancePreviewVO {

    @Schema(description = "活动基本信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ActivityBasicInfo activityInfo;

    @Schema(description = "当前平衡状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private TeamBalanceInfo currentBalance;

    @Schema(description = "预期平衡状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private TeamBalanceInfo proposedBalance;

    @Schema(description = "球员调整计划", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PlayerChangeInfo> changes;

    @Schema(description = "整体改善分数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer overallImprovementScore;

    @Schema(description = "推荐等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private RecommendationLevel recommendationLevel;

    @Schema(description = "预览摘要", requiredMode = Schema.RequiredMode.REQUIRED)
    private String summary;

    /**
     * 推荐等级枚举
     */
    public enum RecommendationLevel {
        HIGH,           // 强烈推荐
        MEDIUM,         // 推荐
        LOW,            // 轻微推荐
        NOT_RECOMMENDED // 不推荐
    }

    /**
     * 球员调整信息
     */
    @Schema(description = "球员调整信息")
    @Data
    public static class PlayerChangeInfo {
        @Schema(description = "球员ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long playerId;

        @Schema(description = "球员姓名", requiredMode = Schema.RequiredMode.REQUIRED)
        private String playerName;

        @Schema(description = "原队伍", requiredMode = Schema.RequiredMode.REQUIRED)
        private TeamType fromTeam;

        @Schema(description = "目标队伍", requiredMode = Schema.RequiredMode.REQUIRED)
        private TeamType toTeam;

        @Schema(description = "调整原因", requiredMode = Schema.RequiredMode.REQUIRED)
        private String reason;

        @Schema(description = "预期影响", requiredMode = Schema.RequiredMode.REQUIRED)
        private String impact;

        /**
         * 队伍类型枚举
         */
        public enum TeamType {
            HOME,   // 主队
            GUEST   // 客队
        }
    }

    /**
     * 队伍平衡信息
     */
    @Schema(description = "队伍平衡信息")
    @Data
    public static class TeamBalanceInfo {
        @Schema(description = "整体平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer overallScore;

        @Schema(description = "各维度平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private BalanceScores balanceScores;

        @Schema(description = "主队信息", requiredMode = Schema.RequiredMode.REQUIRED)
        private TeamInfo homeTeam;

        @Schema(description = "客队信息", requiredMode = Schema.RequiredMode.REQUIRED)
        private TeamInfo guestTeam;
    }

    /**
     * 各维度平衡分数
     */
    @Schema(description = "各维度平衡分数")
    @Data
    public static class BalanceScores {
        @Schema(description = "能力平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer abilityBalance;

        @Schema(description = "身高平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer heightBalance;

        @Schema(description = "位置平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer positionBalance;

        @Schema(description = "好友组平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer friendGroupBalance;

        @Schema(description = "胜率平衡分数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer winRateBalance;
    }

    /**
     * 队伍信息
     */
    @Schema(description = "队伍信息")
    @Data
    public static class TeamInfo {
        @Schema(description = "队伍名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String teamName;

        @Schema(description = "球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer playerCount;

        @Schema(description = "队伍统计信息")
        private TeamStats teamStats;

        @Schema(description = "球员列表", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<PlayerInfo> players;
    }

    /**
     * 队伍统计信息
     */
    @Schema(description = "队伍统计信息")
    @Data
    public static class TeamStats {
        @Schema(description = "平均能力值", requiredMode = Schema.RequiredMode.REQUIRED)
        private Double averageAbility;

        @Schema(description = "平均身高", requiredMode = Schema.RequiredMode.REQUIRED)
        private Double averageHeight;

        @Schema(description = "S级球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer sLevelPlayerCount;

        @Schema(description = "A级球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer aLevelPlayerCount;

        @Schema(description = "B级球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer bLevelPlayerCount;

        @Schema(description = "C级球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer cLevelPlayerCount;

        @Schema(description = "D级球员数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer dLevelPlayerCount;

        @Schema(description = "总能力值", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long totalAbility;

        @Schema(description = "总身高", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long totalHeight;
    }

    /**
     * 球员信息
     */
    @Schema(description = "球员信息")
    @Data
    public static class PlayerInfo {
        @Schema(description = "球员ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long playerId;

        @Schema(description = "球员姓名", requiredMode = Schema.RequiredMode.REQUIRED)
        private String name;

        @Schema(description = "能力评分", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer ratings;

        @Schema(description = "身高", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer height;

        @Schema(description = "位置", requiredMode = Schema.RequiredMode.REQUIRED)
        private String position;

        @Schema(description = "胜率", requiredMode = Schema.RequiredMode.REQUIRED)
        private Double winRate;
    }

    /**
     * 活动基本信息
     */
    @Schema(description = "活动基本信息")
    @Data
    public static class ActivityBasicInfo {
        @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long activityId;

        @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String name;

        @Schema(description = "活动类型", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer type;
    }
} 