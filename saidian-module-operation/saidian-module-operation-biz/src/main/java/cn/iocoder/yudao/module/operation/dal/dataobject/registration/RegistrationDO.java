package cn.iocoder.yudao.module.operation.dal.dataobject.registration;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 活动报名记录表 DO
 *
 * <AUTHOR>
 */
@TableName("sd_registration")
@KeySequence("sd_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegistrationDO extends BaseDO {

    /**
     * 报名ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 球员ID
     */
    private Long playerId;
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动类型 (冗余自sd_activity)
     * 枚举 {@link ActivityTypeEnum}
     */
    private Integer activityType;
    /**
     * 报名时间
     */
    private LocalDateTime registrationTime;
    /**
     * 所属球队ID (友谊赛/联赛)
     */
    private Long teamId;
    /**
     * 所属好友组ID (排位赛好友组队)
     */
    private Long friendGroupId;
    /**
     * 是否为好友组队发起人
     */
    private Boolean isGroupLeader;
    /**
     * 支付方式 (友谊赛): 1-球队整体支付, 2-球员个人支付AA
     */
    private Integer paymentType;
    /**
     * 报名状态: 1-待支付, 2-已支付(待处理/待组局), 3-报名成功(已组局/已分队), 4-已取消(未支付超时/组局失败/用户退赛), 5-已完成(活动结束)
     * 枚举 {@link RegistrationStatusEnum}
     */
    private Integer status;
    /**
     * 支付状态: 1-未支付, 2-已支付
     * 枚举 {@link PayStatusEnum}
     */
    private Integer paymentStatus;
    /**
     * 关联的 pay_order ID
     */
    private Long payOrderId;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 应付金额(分)
     */
    private Integer shouldPayPrice;
    /**
     * 实付金额(分, 考虑优惠)
     */
    private Integer actualPayPrice;
    /**
     * 使用的优惠券ID (如果活动支持优惠券)
     */
    private Long couponId;
    /**
     * 优惠券抵扣金额(分)
     */
    private Integer couponDiscountPrice;
    /**
     * 使用的积分数量
     */
    private Integer pointsUsed;
    /**
     * 积分抵扣金额(分)
     */
    private Integer pointsDiscountPrice;
    /**
     * 退款状态: 0-未退款, 1-部分退款(差额), 2-全额退款, 3-退款中
     * 枚举 {@link RefundStatusEnum}
     */
    private Integer refundStatus;
    /**
     * 累计退款金额(分)
     */
    private Integer totalRefundPrice;
    /**
     * 取消原因
     */
    private String cancelReason;
    /**
     * 分配到的具体球队ID (关联 sd_team.id)，而非1/2标识 (冗余字段, 方便查询)
     */
    private Long teamAssigned;
    /**
     * 本场比赛使用的球服颜色
     */
    private String jerseyColor;
    /**
     * 是否为候补报名
     */
    private Boolean isWaitlist;
    /**
     * 候补队列位置
     */
    private Integer waitlistPosition;
}