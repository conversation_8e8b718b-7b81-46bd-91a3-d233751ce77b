package cn.iocoder.yudao.module.operation.service.discount.handler;

import cn.iocoder.yudao.module.operation.service.discount.model.DiscountContext;

/**
 * 优惠处理器接口
 */
public interface DiscountHandler {

    /**
     * 处理折扣计算并更新上下文。
     * 该方法直接修改传入的 DiscountContext 对象，记录应用的优惠和更新后的价格。
     *
     * @param context 折扣上下文，包含计算所需的所有信息，并在处理过程中被修改。
     */
    void calculateAndApply(DiscountContext context);
} 