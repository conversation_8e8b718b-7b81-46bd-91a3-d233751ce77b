package cn.iocoder.yudao.module.operation.controller.admin.game;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.*;
import cn.iocoder.yudao.module.operation.controller.admin.team.vo.TeamRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.service.activity.ActivityService;
import cn.iocoder.yudao.module.operation.service.daily.PlayerRegistrationService;
import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.game.PlayerGameRelatedService;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants.*;

@Tag(name = "管理后台 - 比赛")
@RestController
@RequestMapping("/operation/game")
@Validated
@Slf4j
public class GameController {

    @Resource
    private GameService gameService;

    @Resource
    private TeamService teamService;

    @Resource
    private PlayerService playerService;

    @Resource
    private PlayerRegistrationService playerRegistrationService;

    @Resource
    private PlayerGameRelatedService playerGameRelatedService;

    @Resource
    private GamePlayerSyncService gamePlayerSyncService;

    @Resource
    private TeamAssignmentService teamAssignmentService;

    @Resource
    private ActivityService activityService;

    @PostMapping("/create")
    @Operation(summary = "创建比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:create')")
    public CommonResult<Long> createGame(@Valid @RequestBody GameSaveReqVO createReqVO) {
        return success(gameService.createGame(createReqVO));
    }

    @PostMapping("/player/add")
    @Operation(summary = "添加球员到比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:create')")
    public CommonResult<Boolean> addPlayerToGame(@Valid @RequestBody GamePlayerReqVO gamePlayerReqVO) {
        gameService.addPlayerToGame(gamePlayerReqVO);
        return success(true);
    }

    @DeleteMapping("/player/delete")
    @Operation(summary = "删除某场比赛的球员")
    @PreAuthorize("@ss.hasPermission('operation:game:delete')")
    public CommonResult<Boolean> deletePlayerFromGame(Long playerId, Long id) {
        gameService.deletePlayerFromGame(playerId, id);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> updateGame(@Valid @RequestBody GameSaveReqVO updateReqVO) {
        gameService.updateGame(updateReqVO);
        return success(true);
    }

    @PutMapping("/change-team")
    @Operation(summary = "换队")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> changeTeam(@Valid @RequestBody ChangeTeamReqVO changeTeamReqVO) {
        gameService.changeTeam(changeTeamReqVO.getGameId(), changeTeamReqVO.getPlayerId(), changeTeamReqVO.getNewTeamId());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除比赛")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('operation:game:delete')")
    public CommonResult<Boolean> deleteGame(@RequestParam("id") Long id) {
        gameService.deleteGame(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得比赛")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<GameRespVO> getGame(@RequestParam("id") Long id) {
        GameDO game = gameService.getGame(id);
        return success(BeanUtils.toBean(game, GameRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得比赛分页")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<PageResult<GameRespVO>> getGamePage(@Valid GamePageReqVO pageReqVO) {
        PageResult<GameRespVO> pageResult = gameService.getGamePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GameRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出比赛 Excel")
    @PreAuthorize("@ss.hasPermission('operation:game:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGameExcel(@Valid GamePageReqVO pageReqVO,
                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GameRespVO> list = gameService.getGamePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "比赛.xls", "数据", GameRespVO.class,
                BeanUtils.toBean(list, GameRespVO.class));
    }

    @PutMapping("/update-attend")
    @Operation(summary = "更新出席结果")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> updateGameAttend(@Valid @RequestBody GameDetailSaveReqVO updateReqVO) {
        gameService.updateGameAttend(updateReqVO);
        return success(true);
    }


    @GetMapping("/detail/get")
    @Operation(summary = "获得比赛结果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<GameDetailSaveRespVO> getGameDetail(@RequestParam("id") Long id) {
        GameDO game = gameService.getGame(id);

        List<PlayerGameRelatedRespVO> players = playerGameRelatedService.getPlayersOfGame(id);

        GameDetailSaveRespVO result = new GameDetailSaveRespVO();
        result.setId(game.getId());
        result.setGuestTeamId(game.getGuestTeamId());
        result.setHomeTeamId(game.getHomeTeamId());
        result.setHomeScore(game.getHomeTeamPoints());
        result.setGuestScore(game.getGuestTeamPoints());
        result.setType(game.getType());

        result.setHomeTeam(BeanUtils.toBean(teamService.getTeam(game.getHomeTeamId()), TeamRespVO.class));
        result.setGuestTeam(BeanUtils.toBean(teamService.getTeam(game.getGuestTeamId()), TeamRespVO.class));

        result.setHomePlayers(players.stream().filter(playerGameRelatedRespVO -> playerGameRelatedRespVO.getTeamId().equals(game.getHomeTeamId()))
                .collect(Collectors.toList()));

        result.setGuestPlayers(players.stream().filter(playerGameRelatedRespVO -> playerGameRelatedRespVO.getTeamId().equals(game.getGuestTeamId()))
                .collect(Collectors.toList()));

        return success(result);
    }
}