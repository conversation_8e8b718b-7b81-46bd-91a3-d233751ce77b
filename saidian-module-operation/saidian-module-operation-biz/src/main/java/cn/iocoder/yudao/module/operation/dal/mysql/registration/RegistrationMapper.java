package cn.iocoder.yudao.module.operation.dal.mysql.registration;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationPageReqVO;
import cn.iocoder.yudao.module.operation.api.registration.dto.AppRegistrationPageReqDTO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import org.apache.ibatis.annotations.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 活动报名记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RegistrationMapper extends BaseMapperX<RegistrationDO> {

    Logger log = LoggerFactory.getLogger(RegistrationMapper.class);

    default RegistrationDO selectByUserIdAndActivityId(Long userId, Long activityId) {
        return selectOne(RegistrationDO::getUserId, userId, RegistrationDO::getActivityId, activityId);
    }
    /**
     * 统计指定活动和状态的报名记录数量
     * 
     * @param activityId 活动ID
     * @param status 报名状态
     * @return 记录数量
     */
    default Long selectCountByActivityIdAndStatus(Long activityId, Integer status) {
        return selectCount(new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getStatus, status));
    }

    /**
     * 根据活动ID和状态查询报名记录列表
     * 
     * @param activityId 活动ID
     * @param status 报名状态
     * @return 报名记录列表
     */
    default List<RegistrationDO> selectByActivityIdAndStatus(Long activityId, Integer status) {
        return selectList(new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getStatus, status));
    }

    /**
     * 根据活动ID和状态查询报名记录列表，排除指定的报名ID
     * 
     * @param activityId 活动ID
     * @param status 报名状态
     * @param excludeRegistrationId 要排除的报名ID
     * @return 报名记录列表
     */
    default List<RegistrationDO> selectByActivityIdAndStatusExcluding(Long activityId, Integer status, Long excludeRegistrationId) {
        return selectList(new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getStatus, status)
                .ne(RegistrationDO::getId, excludeRegistrationId));
    }

    /**
     * 更新报名记录的队伍分配
     * 
     * @param registrationId 报名ID
     * @param teamId 队伍ID
     * @return 是否更新成功
     */
    default boolean updateTeamAssignment(Long registrationId, Long teamId) {
        return update(RegistrationDO.builder().teamId(teamId).build(),
                new LambdaQueryWrapperX<RegistrationDO>().eq(RegistrationDO::getId, registrationId)) > 0;
    }

    /**
     * 更新报名记录的状态
     * 
     * @param registrationId 报名ID
     * @param status 新状态
     * @return 是否更新成功
     */
    default boolean updateRegistrationStatus(Long registrationId, Integer status) {
        return update(RegistrationDO.builder().status(status).build(),
                new LambdaQueryWrapperX<RegistrationDO>().eq(RegistrationDO::getId, registrationId)) > 0;
    }


    default RegistrationDO selectByMerchantOrderNo(String merchantOrderNo) {
        try {
            Long registrationId = Long.parseLong(merchantOrderNo);
            return selectById(registrationId);
        } catch (NumberFormatException e) {
            log.error("[selectByMerchantOrderNo][商家订单号 {} 格式错误，无法解析报名ID]", merchantOrderNo, e);
            return null;
        }
    }
    
    /**
     * 根据用户ID分页查询报名记录
     * 
     * @param pageReqVO 分页查询条件
     * @param userId 用户ID
     * @return 分页结果
     */
    default PageResult<RegistrationDO> selectPage(AppRegistrationPageReqVO pageReqVO, Long userId) {
        return selectPage(pageReqVO, new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getUserId, userId)
                .eqIfPresent(RegistrationDO::getStatus, pageReqVO.getStatus())
                .orderByDesc(RegistrationDO::getRegistrationTime));
    }

    /**
     * API专用：根据用户ID分页查询报名记录
     * 
     * @param reqDTO API分页查询条件
     * @return 分页结果
     */
    default PageResult<RegistrationDO> selectPageForApi(AppRegistrationPageReqDTO reqDTO) {
        LambdaQueryWrapperX<RegistrationDO> wrapper = new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getUserId, reqDTO.getUserId())
                .eqIfPresent(RegistrationDO::getStatus, reqDTO.getStatus())
                .eqIfPresent(RegistrationDO::getActivityType, reqDTO.getActivityType())
                .orderByDesc(RegistrationDO::getRegistrationTime);
        
        // 如果有状态列表，使用in查询
        if (reqDTO.getStatusList() != null && !reqDTO.getStatusList().isEmpty()) {
            wrapper.in(RegistrationDO::getStatus, reqDTO.getStatusList());
        }
        
        return selectPage(reqDTO, wrapper);
    }

    /**
     * API专用：根据用户ID和状态统计报名数量
     * 
     * @param userId 用户ID
     * @param status 报名状态，null表示全部
     * @return 报名数量
     */
    default Long selectCountByUserIdAndStatus(Long userId, Integer status) {
        LambdaQueryWrapperX<RegistrationDO> wrapper = new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getUserId, userId);
        
        if (status != null) {
            wrapper.eq(RegistrationDO::getStatus, status);
        }
        
        return selectCount(wrapper);
    }

    /**
     * API专用：根据用户ID和状态列表统计报名数量
     * 
     * @param userId 用户ID
     * @param statusList 报名状态列表
     * @return 报名数量
     */
    default Long selectCountByUserIdAndStatusList(Long userId, List<Integer> statusList) {
        return selectCount(new LambdaQueryWrapperX<RegistrationDO>()
                .eq(RegistrationDO::getUserId, userId)
                .in(RegistrationDO::getStatus, statusList));
    }
} 