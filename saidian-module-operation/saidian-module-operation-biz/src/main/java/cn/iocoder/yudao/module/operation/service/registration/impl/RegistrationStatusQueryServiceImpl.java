package cn.iocoder.yudao.module.operation.service.registration.impl;

import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationStatusQueryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报名状态查询服务实现类
 *
 * 🎯 核心价值：
 * 1. 将复杂的状态组合查询逻辑集中管理
 * 2. 提供语义化的查询方法，提高代码可读性
 * 3. 统一状态判断标准，避免不一致
 * 4. 便于后续状态逻辑的维护和优化
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RegistrationStatusQueryServiceImpl implements RegistrationStatusQueryService {

    @Resource
    private RegistrationMapper registrationMapper;

    // ==================== 基础状态查询 ====================

    @Override
    public List<RegistrationDO> getActiveRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.COMPLETED.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    @Override
    public List<RegistrationDO> getWaitlistRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .orderByAsc(RegistrationDO::getWaitlistPosition)
        );
    }

    @Override
    public List<RegistrationDO> getPromotableWaitlistRegistrations(Long activityId, int limit) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .orderByAsc(RegistrationDO::getWaitlistPosition)
                        .last("LIMIT " + limit)
        );
    }

    @Override
    public List<RegistrationDO> getFormalRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false))
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.COMPLETED.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    // ==================== 退款相关查询 ====================

    @Override
    public List<RegistrationDO> getRefundableRegistrations(Long activityId, RefundScenario scenario) {
        LambdaQueryWrapper<RegistrationDO> wrapper = Wrappers.<RegistrationDO>lambdaQuery()
                .eq(RegistrationDO::getActivityId, activityId)
                .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                // 排除正在退款中的记录
                .and(w -> w
                        .isNull(RegistrationDO::getRefundStatus)
                        .or()
                        .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus()))
                // 排除已全额退款的记录
                .and(w -> w
                        .isNull(RegistrationDO::getRefundStatus)
                        .or()
                        .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
                // 仍有剩余金额可退
                .and(w -> w
                        .isNull(RegistrationDO::getTotalRefundPrice)
                        .or()
                        .apply("actual_pay_price > IFNULL(total_refund_price, 0)"));

        // 根据退款场景添加报名状态条件
        switch (scenario) {
            case USER_CANCEL:
                wrapper.in(RegistrationDO::getStatus,
                        RegistrationStatusEnum.PAID.getStatus(),
                        RegistrationStatusEnum.SUCCESSFUL.getStatus());
                break;
            case GROUPING_FAILED:
                wrapper.eq(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus());
                break;
            case DIFFERENCE:
                wrapper.in(RegistrationDO::getStatus,
                        RegistrationStatusEnum.PAID.getStatus(),
                        RegistrationStatusEnum.SUCCESSFUL.getStatus());
                break;
            case ADMIN_OPERATION:
                wrapper.in(RegistrationDO::getStatus,
                        RegistrationStatusEnum.PAID.getStatus(),
                        RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                        RegistrationStatusEnum.COMPLETED.getStatus(),
                        RegistrationStatusEnum.WAIT_LIST.getStatus());
                break;
            case SYSTEM_AUTO:
            case WAIT_FAILED:
                wrapper.in(RegistrationDO::getStatus,
                        RegistrationStatusEnum.PAID.getStatus(),
                        RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                        RegistrationStatusEnum.COMPLETED.getStatus());
                break;
            default:
                log.warn("[getRefundableRegistrations] 未知的退款场景: {}", scenario);
                return List.of();
        }

        return registrationMapper.selectList(wrapper);
    }

    @Override
    public List<RegistrationDO> getRefundingRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus())
        );
    }

    @Override
    public List<RegistrationDO> getRefundedRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getRefundStatus,
                                RefundStatusEnum.FULL_REFUNDED.getStatus(),
                                RefundStatusEnum.PARTIAL_REFUNDED.getStatus())
        );
    }

    // ==================== 分队相关查询 ====================

    @Override
    public List<RegistrationDO> getTeamAssignableRegistrations(Long activityId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 🔧 关键：排除候补球员
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false))
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    @Override
    public List<RegistrationDO> getTeamRegistrations(Long activityId, Long teamId) {
        return registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getTeamId, teamId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.COMPLETED.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 排除候补球员（球队报名不应包含候补）
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false))
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    // ==================== 统计查询 ====================

    @Override
    public long countActiveRegistrations(Long activityId) {
        return registrationMapper.selectCount(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.COMPLETED.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    @Override
    public long countWaitlistRegistrations(Long activityId) {
        return registrationMapper.selectCount(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
        );
    }

    @Override
    public long countTeamRegistrations(Long activityId, Long teamId) {
        return registrationMapper.selectCount(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getTeamId, teamId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.COMPLETED.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 排除候补球员
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false))
                        // 排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus)
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()))
        );
    }

    // ==================== 状态判断 ====================

    @Override
    public boolean isActiveRegistration(RegistrationDO registration) {
        if (registration == null) {
            return false;
        }

        // 检查报名状态
        Integer status = registration.getStatus();
        boolean validStatus = RegistrationStatusEnum.PAID.getStatus().equals(status) ||
                RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(status) ||
                RegistrationStatusEnum.COMPLETED.getStatus().equals(status);

        // 检查支付状态
        boolean isPaid = PayStatusEnum.PAID.getStatus().equals(registration.getPaymentStatus());

        // 检查是否已全额退款
        boolean isFullRefunded = RefundStatusEnum.FULL_REFUNDED.getStatus().equals(registration.getRefundStatus());

        return validStatus && isPaid && !isFullRefunded;
    }

    @Override
    public boolean isWaitlistRegistration(RegistrationDO registration) {
        if (registration == null) {
            return false;
        }

        // 🔧 修复：候补球员必须同时满足两个条件：
        // 1) 被标记为候补 (isWaitlist = true)
        // 2) 已支付且状态有效
        return Boolean.TRUE.equals(registration.getIsWaitlist()) && isActiveRegistration(registration);
    }

    @Override
    public boolean canRefund(RegistrationDO registration, RefundScenario scenario) {
        if (registration == null || scenario == null) {
            return false;
        }

        // 基础条件检查
        if (!PayStatusEnum.PAID.getStatus().equals(registration.getPaymentStatus())) {
            return false;
        }

        // 检查是否正在退款中
        if (RefundStatusEnum.REFUNDING.getStatus().equals(registration.getRefundStatus())) {
            return false;
        }

        // 检查是否已全额退款
        if (RefundStatusEnum.FULL_REFUNDED.getStatus().equals(registration.getRefundStatus())) {
            // 管理员操作允许在部分退款基础上继续退款
            if (RefundScenario.ADMIN_OPERATION.equals(scenario) &&
                    RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(registration.getRefundStatus())) {
                return true;
            }
            return false;
        }

        // 检查是否还有剩余金额可退
        Integer actualPaid = registration.getActualPayPrice() != null ? registration.getActualPayPrice() : 0;
        Integer alreadyRefunded = registration.getTotalRefundPrice() != null ? registration.getTotalRefundPrice() : 0;
        if (actualPaid <= alreadyRefunded) {
            return false;
        }

        // 根据退款场景检查报名状态
        Integer currentStatus = registration.getStatus();
        switch (scenario) {
            case USER_CANCEL:
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus);
            case GROUPING_FAILED:
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus);
            case DIFFERENCE:
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus);
            case ADMIN_OPERATION:
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.COMPLETED.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.WAIT_LIST.getStatus().equals(currentStatus);
            case SYSTEM_AUTO:
            case WAIT_FAILED:
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus) ||
                        RegistrationStatusEnum.COMPLETED.getStatus().equals(currentStatus);
            default:
                log.warn("[canRefund] 未知的退款场景: {}", scenario);
                return false;
        }
    }

    @Override
    public boolean isRefunding(RegistrationDO registration) {
        return registration != null &&
                RefundStatusEnum.REFUNDING.getStatus().equals(registration.getRefundStatus());
    }

    @Override
    public boolean isRefunded(RegistrationDO registration) {
        return registration != null &&
                (RefundStatusEnum.FULL_REFUNDED.getStatus().equals(registration.getRefundStatus()) ||
                        RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(registration.getRefundStatus()));
    }
}
