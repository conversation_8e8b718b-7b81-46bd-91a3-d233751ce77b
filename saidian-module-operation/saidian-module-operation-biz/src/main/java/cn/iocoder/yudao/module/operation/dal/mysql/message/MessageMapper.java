package cn.iocoder.yudao.module.operation.dal.mysql.message;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.admin.message.vo.MessagePageReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.message.MessageDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 消息通知 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageMapper extends BaseMapperX<MessageDO> {

    default PageResult<MessageDO> selectPage(MessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MessageDO>()
                .eqIfPresent(MessageDO::getSenderId, reqVO.getSenderId())
                .eqIfPresent(MessageDO::getReceiverId, reqVO.getReceiverId())
                .eqIfPresent(MessageDO::getTeamId, reqVO.getTeamId())
                .eqIfPresent(MessageDO::getContractId, reqVO.getContractId())
                .eqIfPresent(MessageDO::getContent, reqVO.getContent())
                .eqIfPresent(MessageDO::getType, reqVO.getType())
                .eqIfPresent(MessageDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MessageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MessageDO::getId));
    }

}