package cn.iocoder.yudao.module.operation.service.refund.impl;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.activity.ActivityQueryService;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import cn.iocoder.yudao.module.operation.service.registration.event.RegistrationCancelledEvent;

import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;

import java.util.Collections;

import cn.iocoder.yudao.module.pay.api.notify.dto.PayRefundNotifyReqDTO;
import cn.iocoder.yudao.module.pay.api.refund.PayRefundApi;
import cn.iocoder.yudao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.iocoder.yudao.module.pay.api.refund.dto.PayRefundRespDTO;
import cn.iocoder.yudao.module.pay.enums.refund.PayRefundStatusEnum;
import cn.iocoder.yudao.module.promotion.api.coupon.CouponApi;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculator;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculatorFactory;
import cn.hutool.extra.spring.SpringUtil;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * 活动退款 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ActivityRefundServiceImpl implements ActivityRefundService {

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private ActivityQueryService activityQueryService;

    @Resource
    private PayRefundApi payRefundApi;

    @Resource
    private CouponApi couponApi;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private RefundCalculatorFactory refundCalculatorFactory;

    @Resource
    private WaitlistService waitlistService;

    @Resource
    private TeamAssignmentService teamAssignmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processRefund(@NotNull Long registrationId, RefundScenario scenario,
                              Long operatorId, String reason) {
        log.info("[processRefund][开始处理退款] 报名ID: {}, 退款场景: {}, 操作者: {}, 原因: {}",
                registrationId, scenario, operatorId, reason);

        try {
            // 🔧 修正：采用内部优先模式，防止重复退款
            // 1. 预检查阶段：验证退款条件，计算退款金额
            RefundPreCheckResult preCheckResult = performRefundPreCheck(registrationId, scenario);

            // 2. 处理0元退款：无需外部调用，直接处理到最终状态
            if (preCheckResult.getRefundAmount() <= 0) {
                log.info("[processRefund][检测到0元退款，直接处理] 报名ID: {}, 退款场景: {}",
                        registrationId, scenario);
                processZeroRefundDirectly(preCheckResult.getRegistration(), preCheckResult.getActivity(),
                        scenario, operatorId, reason);
                log.info("[processRefund][0元退款处理完成] 报名ID: {}", registrationId);
                return;
            }

            // 3. 🔧 关键：先更新内部状态为REFUNDING，防止重复退款
            updateInternalRefundStatus(registrationId);
            log.info("[processRefund][内部状态已更新为退款中，防止重复操作] 报名ID: {}", registrationId);

            // 4. 调用外部接口创建退款订单（在同一事务中，失败会自动回滚内部状态）
            Long refundId = createExternalRefundOrder(preCheckResult, scenario, reason);
            log.info("[processRefund][外部退款订单创建成功] 报名ID: {}, 退款ID: {}, 退款金额: {}",
                    registrationId, refundId, preCheckResult.getRefundAmount());

            log.info("[processRefund][退款处理完成，等待支付回调] 报名ID: {}, 退款场景: {}, 外部退款ID: {}",
                    registrationId, scenario, refundId);

        } catch (Exception e) {
            log.error("[processRefund][退款处理异常，事务将回滚] 报名ID: {}, 退款场景: {}", registrationId, scenario, e);
            // 事务回滚会自动恢复内部状态，避免数据不一致
            throw e;
        }
    }

    /**
     * 🔧 新增：退款预检查，只读操作不更新状态
     * 将原来的校验逻辑提取出来，确保在调用外部接口前完成所有验证
     */
    private RefundPreCheckResult performRefundPreCheck(@NotNull Long registrationId, RefundScenario scenario) {
        // 1. 校验报名记录
        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            log.error("[performRefundPreCheck][报名记录不存在] 报名ID: {}", registrationId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
        }

        // 2. 校验退款条件
        if (!isRefundAllowed(registration, scenario)) {
            log.error("[performRefundPreCheck][报名记录状态不允许退款] 报名ID: {}, 当前状态: {}, 退款场景: {}",
                    registrationId, registration.getStatus(), scenario);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
        }

        // 3. 校验支付状态
        if (!PayStatusEnum.PAID.getStatus().equals(registration.getPaymentStatus())) {
            log.error("[performRefundPreCheck][支付状态不正确，无法退款] 报名ID: {}, 支付状态: {}",
                    registrationId, registration.getPaymentStatus());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
        }

        // 4. 校验支付订单ID
        if (registration.getPayOrderId() == null || registration.getPayOrderId() <= 0) {
            log.error("[performRefundPreCheck][支付订单ID不存在或无效] 报名ID: {}, 支付订单ID: {}",
                    registrationId, registration.getPayOrderId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
        }

        // 5. 校验退款状态，避免重复退款
        Integer currentRefundStatus = registration.getRefundStatus();
        if (currentRefundStatus != null) {
            if (RefundStatusEnum.REFUNDING.getStatus().equals(currentRefundStatus)) {
                // 检查是否为超时未完成的退款，允许重试
                LocalDateTime lastUpdateTime = registration.getUpdateTime();
                if (lastUpdateTime != null && lastUpdateTime.plusMinutes(30).isBefore(LocalDateTime.now())) {
                    log.warn("[performRefundPreCheck][退款超时未完成，允许重试] 报名ID: {}, 最后更新时间: {}",
                            registrationId, lastUpdateTime);
                } else {
                    log.warn("[performRefundPreCheck][报名记录正在退款中，拒绝重复退款] 报名ID: {}, 当前退款状态: {}",
                            registrationId, currentRefundStatus);
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
                }
            }

            // 校验最终退款状态
            if (RefundStatusEnum.isFinalStatus(currentRefundStatus)) {
                if (RefundScenario.ADMIN_OPERATION.equals(scenario) &&
                        RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(currentRefundStatus)) {
                    log.info("[performRefundPreCheck][管理员操作，允许在部分退款基础上继续退款] 报名ID: {}",
                            registrationId);
                } else {
                    log.warn("[performRefundPreCheck][报名记录已处于最终退款状态，拒绝新的退款] 报名ID: {}, 退款状态: {}",
                            registrationId, currentRefundStatus);
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
                }
            }
        }

        // 6. 获取活动信息
        ActivityDO activity = activityQueryService.getActivity(registration.getActivityId());
        if (activity == null) {
            log.error("[performRefundPreCheck][活动不存在] 活动ID: {}", registration.getActivityId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 7. 计算退款金额
        Integer refundAmount = determineRefundAmount(registration, activity, scenario);

        return new RefundPreCheckResult(registration, activity, refundAmount);
    }

    /**
     * 🔧 新增：创建外部退款订单（无事务环境）
     */
    private Long createExternalRefundOrder(RefundPreCheckResult preCheckResult, RefundScenario scenario, String reason) {
        RegistrationDO registration = preCheckResult.getRegistration();
        ActivityDO activity = preCheckResult.getActivity();
        Integer refundAmount = preCheckResult.getRefundAmount();

        String userIp = ServletUtils.getClientIP();
        if (userIp == null || userIp.trim().isEmpty()) {
            userIp = "127.0.0.1"; // 系统自动操作使用本地IP
        }

        String merchantRefundId = "REFUND_" + registration.getId() + "_" + System.currentTimeMillis();
        PayRefundCreateReqDTO refundCreateReq = new PayRefundCreateReqDTO();
        refundCreateReq.setAppKey(getAppKey(activity));
        refundCreateReq.setMerchantRefundId(merchantRefundId);
        refundCreateReq.setMerchantOrderId(String.valueOf(registration.getId()));
        refundCreateReq.setReason("活动报名退款: " + activity.getName() + " - " + scenario.getDesc() + " - " + reason);
        refundCreateReq.setPrice(refundAmount);
        refundCreateReq.setUserIp(userIp);

        log.info("[createExternalRefundOrder][准备创建退款订单] 报名ID: {}, 退款金额: {}, 退款场景: {}",
                registration.getId(), refundAmount, scenario);

        // 调用支付模块创建退款订单（相信支付模块的健壮性）
        return payRefundApi.createRefund(refundCreateReq);
    }

    /**
     * 🔧 新增：更新内部退款状态（在事务中执行）
     */
    private void updateInternalRefundStatus(@NotNull Long registrationId) {
        boolean updateSuccess = registrationMapper.update(null,
                Wrappers.<RegistrationDO>lambdaUpdate()
                        .eq(RegistrationDO::getId, registrationId)
                        .set(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus())
                        .set(RegistrationDO::getUpdateTime, LocalDateTime.now())
        ) > 0;

        if (!updateSuccess) {
            log.error("[updateInternalRefundStatus][更新报名记录退款状态失败] 报名ID: {}, 目标状态: {}",
                    registrationId, RefundStatusEnum.REFUNDING);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_UPDATE_FAILED);
        }

        log.info("[updateInternalRefundStatus][退款状态更新成功] 报名ID: {}, 状态: {}",
                registrationId, RefundStatusEnum.REFUNDING);
    }

    /**
     * 🔧 新增：退款预检查结果对象
     */
    @Getter
    private static class RefundPreCheckResult {
        private final RegistrationDO registration;
        private final ActivityDO activity;
        private final Integer refundAmount;

        public RefundPreCheckResult(RegistrationDO registration, ActivityDO activity, Integer refundAmount) {
            this.registration = registration;
            this.activity = activity;
            this.refundAmount = refundAmount;
        }

    }

    /**
     * 判断报名记录是否允许退款
     * 根据不同的退款场景，允许的报名状态不同
     */
    private boolean isRefundAllowed(RegistrationDO registration, RefundScenario scenario) {
        Integer currentStatus = registration.getStatus();

        switch (scenario) {
            case USER_CANCEL:
                // 用户主动取消：允许PAID状态（组局前）和SUCCESSFUL状态（组局后，会有违约费）
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus);

            case GROUPING_FAILED:
                // 组局失败：允许PAID状态
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus);

            case DIFFERENCE:
                // 差额退款：允许PAID和SUCCESSFUL状态（组局成功后仍然需要差额退款）
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus);

            case ADMIN_OPERATION:
                // 管理员操作：允许所有已支付的状态（PAID、SUCCESSFUL、COMPLETED等）
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.COMPLETED.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.WAIT_LIST.getStatus().equals(currentStatus);

            case SYSTEM_AUTO:
                // 系统自动退款：允许大部分状态
                return RegistrationStatusEnum.PAID.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus) ||
                       RegistrationStatusEnum.COMPLETED.getStatus().equals(currentStatus);

            default:
                log.warn("[isRefundAllowed] 未知的退款场景: {}, 报名ID: {}", scenario, registration.getId());
                return false;
        }
    }

    @Override
    public void returnUsedCoupon(RegistrationDO registration) {
        try {
            // 检查是否有使用优惠券
            if (registration.getCouponId() != null && registration.getCouponId() > 0) {
                log.info("[returnUsedCoupon] 开始退还优惠券: registrationId={}, couponId={}",
                        registration.getId(), registration.getCouponId());

                // 调用优惠券API退还已使用的优惠券
                couponApi.returnUsedCoupon(registration.getCouponId());

                log.info("[returnUsedCoupon] 优惠券退还成功: registrationId={}, couponId={}",
                        registration.getId(), registration.getCouponId());
            }
        } catch (Exception e) {
            log.error("[returnUsedCoupon] 退还优惠券异常: registrationId={}, couponId={}",
                    registration.getId(), registration.getCouponId(), e);
            // 优惠券退还失败不影响退款流程，只记录日志
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processGroupingFailedRefund(@NotNull Long registrationId, String reason) {
        processRefund(registrationId, RefundScenario.GROUPING_FAILED, 0L, reason);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleRefundNotify(PayRefundNotifyReqDTO notifyReqDTO) {
        log.info("[handleRefundNotify][notifyData={}] 接收到退款回调", JsonUtils.toJsonString(notifyReqDTO));

        Long payRefundId = notifyReqDTO.getPayRefundId();
        if (payRefundId == null) {
            log.error("[handleRefundNotify] 退款回调中未包含 payRefundId，通知内容: {}", JsonUtils.toJsonString(notifyReqDTO));
            return;
        }

        PayRefundRespDTO refundDetails = payRefundApi.getRefund(payRefundId);
        if (refundDetails == null) {
            log.error("[handleRefundNotify][payRefundId={}] 未查询到对应的退款单详情", payRefundId);
            return;
        }
        log.info("[handleRefundNotify][payRefundId={}] 查询到退款单详情: {}", payRefundId, JsonUtils.toJsonString(refundDetails));

        Long registrationId;
        try {
            registrationId = Long.parseLong(refundDetails.getMerchantOrderId());
        } catch (NumberFormatException e) {
            log.error("[handleRefundNotify][payRefundId={}] 退款单详情中的 merchantOrderId ({}) 解析为Long失败",
                    payRefundId, refundDetails.getMerchantOrderId(), e);
            return;
        }

        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            log.error("[handleRefundNotify][registrationId={}] (来自退款单merchantOrderId) 对应的报名记录不存在", registrationId);
            return;
        }

        // 🔧 关键修复：增加基于退款金额的幂等性检查，防止重复处理相同金额的回调
        if (isRefundAlreadyProcessed(registration, payRefundId, refundDetails.getRefundPrice())) {
            log.warn("[handleRefundNotify][registrationId={}] 退款订单 {} (金额: {}) 已处理过，跳过重复处理",
                    registrationId, payRefundId, refundDetails.getRefundPrice());
            return;
        }

        if (RefundStatusEnum.isFinalStatus(registration.getRefundStatus())) {
            log.warn("[handleRefundNotify][registrationId={}] 报名记录退款状态已是最终状态 ({})，无需重复处理",
                    registrationId, registration.getRefundStatus());
            return;
        }
        if (!RefundStatusEnum.REFUNDING.getStatus().equals(registration.getRefundStatus())) {
            log.error("[handleRefundNotify][registrationId={}] 报名记录退款状态不是 '退款中' ({})，回调逻辑异常",
                    registrationId, registration.getRefundStatus());
            return;
        }

        // 判断退款状态
        boolean isRefundSuccess = PayRefundStatusEnum.SUCCESS.getStatus().equals(refundDetails.getStatus());
        RefundStatusEnum newRegRefundStatus;

        if (isRefundSuccess) {
            // 🔧 关键修复：原子性处理整个状态更新，避免分步骤导致的不一致
            Integer actualPaid = registration.getActualPayPrice() != null ? registration.getActualPayPrice() : 0;
            Integer refundAmount = refundDetails.getRefundPrice();
            Integer currentTotalRefund = registration.getTotalRefundPrice() != null ? registration.getTotalRefundPrice() : 0;
            Integer newTotalRefund = currentTotalRefund + refundAmount;

            // 根据累计退款金额判断最终状态
            if (newTotalRefund >= actualPaid) {
                newRegRefundStatus = RefundStatusEnum.FULL_REFUNDED;
                //全额退款，需要返还优惠券
                returnUsedCoupon(registration);
                log.info("[handleRefundNotify][registrationId={}] 全额退款完成，实付: {}, 累计退款: {}",
                        registrationId, actualPaid, newTotalRefund);
            } else {
                newRegRefundStatus = RefundStatusEnum.PARTIAL_REFUNDED;
                log.info("[handleRefundNotify][registrationId={}] 部分退款完成，实付: {}, 累计退款: {}, 剩余: {}",
                        registrationId, actualPaid, newTotalRefund, actualPaid - newTotalRefund);
            }

            // 🔧 修复：根据原始报名状态决定全额退款后的状态转换
            RegistrationStatusEnum targetRegistrationStatus = determineTargetRegistrationStatus(
                    registration.getStatus(), newRegRefundStatus);
            PayStatusEnum targetPaymentStatus = determineTargetPaymentStatus(newRegRefundStatus);

            // 原子性更新所有状态
            boolean updateSuccess = registrationMapper.update(null,
                    Wrappers.<RegistrationDO>lambdaUpdate()
                            .eq(RegistrationDO::getId, registrationId)
                            .eq(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus()) // 确保状态正确
                            .set(RegistrationDO::getTotalRefundPrice, newTotalRefund)
                            .set(RegistrationDO::getRefundStatus, newRegRefundStatus.getStatus())
                            .set(RegistrationDO::getStatus, targetRegistrationStatus.getStatus())
                            .set(RegistrationDO::getPaymentStatus, targetPaymentStatus.getStatus())
                            .set(RegistrationDO::getUpdateTime, LocalDateTime.now())
            ) > 0;

            if (!updateSuccess) {
                log.error("[handleRefundNotify][registrationId={}] 原子性状态更新失败，可能存在并发修改", registrationId);
                return;
            }

            log.info("[handleRefundNotify][registrationId={}] 状态更新成功: 退款状态={}, 报名状态={}, 支付状态={}",
                    registrationId, newRegRefundStatus, targetRegistrationStatus, targetPaymentStatus);

            // 发布退款完成事件（仅在退款成功时发布）
            // 重新查询最新状态用于事件发布
            RegistrationDO updatedRegistration = registrationMapper.selectById(registrationId);
            publishRefundCompletedEventSynchronously(updatedRegistration);

        } else if (PayRefundStatusEnum.FAILURE.getStatus().equals(refundDetails.getStatus())) {
            // 退款失败：只更新退款状态，不影响报名状态和支付状态
            newRegRefundStatus = RefundStatusEnum.FAILURE;

            boolean updateSuccess = registrationMapper.update(null,
                    Wrappers.<RegistrationDO>lambdaUpdate()
                            .eq(RegistrationDO::getId, registrationId)
                            .eq(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus())
                            .set(RegistrationDO::getRefundStatus, newRegRefundStatus.getStatus())
                            .set(RegistrationDO::getUpdateTime, LocalDateTime.now())
            ) > 0;

            if (!updateSuccess) {
                log.error("[handleRefundNotify][registrationId={}] 退款失败状态更新失败，可能存在并发修改", registrationId);
                return;
            }

            log.info("[handleRefundNotify][registrationId={}] 退款失败状态更新成功", registrationId);
        } else {
            log.warn("[handleRefundNotify][registrationId={}] 未知退款状态({}), 暂不处理",
                    registrationId, refundDetails.getStatus());
        }
    }


    @Override
    public int processActivityCancellationRefunds(@NotNull Long activityId, Long operatorId, String reason) {
        log.info("[processActivityCancellationRefunds] 开始处理活动取消的批量退款，活动ID: {}, 操作员: {}, 原因: {}",
                activityId, operatorId, reason);

        // 1. 查询活动信息（避免在循环中重复查询）
        ActivityDO activity = activityQueryService.getActivity(activityId);
        if (activity == null) {
            log.error("[processActivityCancellationRefunds][活动不存在] 活动ID: {}", activityId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 🔧 修复：优化查询条件，排除已在退款中和已完全退款的记录
        List<RegistrationDO> refundableRegistrations = registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                            RegistrationStatusEnum.PAID.getStatus(),
                            RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                            RegistrationStatusEnum.WAIT_LIST.getStatus(),
                            RegistrationStatusEnum.COMPLETED.getStatus())
                        // 🚫 移除CANCELLED状态，避免对已取消的记录重复退款
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        // 🔧 关键修复：排除正在退款中的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus) // 从未退款过
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus()) // 非退款中
                        )
                        // 🔧 关键修复：排除已全额退款的记录
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus) // 从未退款过
                                .or()
                                .ne(RegistrationDO::getRefundStatus, RefundStatusEnum.FULL_REFUNDED.getStatus()) // 非全额退款
                        )
                        // 仍有剩余金额可退
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getTotalRefundPrice) // 未退款过
                                .or()
                                .apply("actual_pay_price > IFNULL(total_refund_price, 0)") // 或还有剩余金额可退
                        )
        );

        if (refundableRegistrations.isEmpty()) {
            log.info("[processActivityCancellationRefunds] 活动 {} 没有需要退款的报名记录", activityId);
            return 0;
        }

        log.info("[processActivityCancellationRefunds] 活动 {} 找到 {} 条需要退款的报名记录",
                activityId, refundableRegistrations.size());

        // 3. 为每个报名记录计算并触发剩余金额退款（每个使用独立事务）
        int successCount = 0;
        String refundReason = "管理员取消活动：" + (reason != null ? reason : "无具体原因");

        for (RegistrationDO registration : refundableRegistrations) {
            try {
                // 计算应退款金额（实付金额 - 已退款金额）
                Integer actualPaid = registration.getActualPayPrice() != null ? registration.getActualPayPrice() : 0;
                Integer alreadyRefunded = registration.getTotalRefundPrice() != null ? registration.getTotalRefundPrice() : 0;
                Integer remainingAmount = actualPaid - alreadyRefunded;

                if (remainingAmount <= 0) {
                    log.info("[processActivityCancellationRefunds] 报名记录 {} 无剩余金额可退，跳过", registration.getId());
                    continue;
                }

                log.info("[processActivityCancellationRefunds] 报名记录 {} 的退款，实付: {}, 已退: {}, 剩余: {}, 当前状态: {}",
                        registration.getId(), actualPaid, alreadyRefunded, remainingAmount, registration.getStatus());

                // 使用独立事务处理单个退款，传入已查询好的数据避免重复查询
                getSelf().processAdminCancellationRefundWithData(registration, activity, operatorId, refundReason, remainingAmount);
                successCount++;

                log.info("[processActivityCancellationRefunds] 报名记录 {} 退款处理成功", registration.getId());
            } catch (Exception e) {
                log.error("[processActivityCancellationRefunds] 报名记录 {} 退款处理失败", registration.getId(), e);
                // 继续处理其他记录，不中断整个流程
            }
        }

        log.info("[processActivityCancellationRefunds] 活动 {} 批量退款处理完成，成功: {}, 总数: {}",
                activityId, successCount, refundableRegistrations.size());

        return successCount;
    }

    /**
     * 处理管理员取消活动的单个报名退款（使用已查询的数据，避免重复查询）
     * 🔧 关键修复：使用REQUIRES_NEW独立事务，确保单个退款失败不影响其他退款
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processAdminCancellationRefundWithData(RegistrationDO registration, ActivityDO activity,
                                                       Long operatorId, String reason, Integer refundAmount) {
        Long registrationId = registration.getId();

        // 1. 重新查询最新的报名记录（避免使用过期数据，但只查询一次）
        RegistrationDO latestRegistration = registrationMapper.selectById(registrationId);
        if (latestRegistration == null) {
            log.error("[processAdminCancellationRefundWithData][报名记录不存在] 报名ID: {}", registrationId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
        }

        // 2. 校验支付状态 - 只有已支付的才能退款
        if (!PayStatusEnum.PAID.getStatus().equals(latestRegistration.getPaymentStatus())) {
            log.error("[processAdminCancellationRefundWithData][支付状态不正确，无法退款] 报名ID: {}, 支付状态: {}",
                    registrationId, latestRegistration.getPaymentStatus());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
        }

        // 3. 校验支付订单ID是否存在
        if (latestRegistration.getPayOrderId() == null || latestRegistration.getPayOrderId() <= 0) {
            log.error("[processAdminCancellationRefundWithData][支付订单ID不存在或无效] 报名ID: {}, 支付订单ID: {}",
                    registrationId, latestRegistration.getPayOrderId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND);
        }

        // 🔧 修复：加强退款状态检查，防止重复退款
        Integer currentRefundStatus = latestRegistration.getRefundStatus();

        // 4.1 如果已经在退款中，跳过
        if (RefundStatusEnum.REFUNDING.getStatus().equals(currentRefundStatus)) {
            log.warn("[processAdminCancellationRefundWithData] 报名记录 {} 已在退款中，跳过重复处理", registrationId);
            return;
        }

        // 4.2 如果已全额退款，跳过
        if (RefundStatusEnum.FULL_REFUNDED.getStatus().equals(currentRefundStatus)) {
            log.info("[processAdminCancellationRefundWithData] 报名记录 {} 已全额退款，跳过处理", registrationId);
            return;
        }

        // 4.3 如果是退款失败状态，需要检查是否允许重试（超过30分钟）
        if (RefundStatusEnum.FAILURE.getStatus().equals(currentRefundStatus)) {
            LocalDateTime lastUpdateTime = latestRegistration.getUpdateTime();
            if (lastUpdateTime != null && lastUpdateTime.plusMinutes(30).isAfter(LocalDateTime.now())) {
                log.warn("[processAdminCancellationRefundWithData] 报名记录 {} 退款失败时间过短，禁止立即重试", registrationId);
                return;
            }
            log.info("[processAdminCancellationRefundWithData] 报名记录 {} 退款失败超过30分钟，允许重试", registrationId);
        }

        // 4.4 二次确认剩余退款金额
        Integer actualPaid = latestRegistration.getActualPayPrice() != null ? latestRegistration.getActualPayPrice() : 0;
        Integer alreadyRefunded = latestRegistration.getTotalRefundPrice() != null ? latestRegistration.getTotalRefundPrice() : 0;
        Integer actualRemaining = actualPaid - alreadyRefunded;

        if (actualRemaining <= 0) {
            log.info("[processAdminCancellationRefundWithData] 报名记录 {} 无剩余金额可退，实付: {}, 已退: {}",
                    registrationId, actualPaid, alreadyRefunded);
            return;
        }

        // 4.5 验证传入的退款金额是否合理
        if (refundAmount > actualRemaining) {
            log.warn("[processAdminCancellationRefundWithData] 报名记录 {} 退款金额 {} 超过剩余金额 {}，调整为剩余金额",
                    registrationId, refundAmount, actualRemaining);
            refundAmount = actualRemaining;
        }

        // 5. 创建退款订单（使用传入的活动数据，避免重复查询）
        String userIp = ServletUtils.getClientIP();
        if (userIp == null || userIp.trim().isEmpty()) {
            userIp = "127.0.0.1"; // 管理员操作使用本地IP
        }
        String merchantRefundId = "ADMIN_REFUND_" + registrationId + "_" + System.currentTimeMillis();
        PayRefundCreateReqDTO refundCreateReq = new PayRefundCreateReqDTO();
        refundCreateReq.setAppKey(getAppKey(activity));
        refundCreateReq.setMerchantRefundId(merchantRefundId);
        refundCreateReq.setMerchantOrderId(String.valueOf(registrationId));
        refundCreateReq.setReason("管理员取消活动退款: " + activity.getName() + " - " + reason);
        refundCreateReq.setPrice(refundAmount);
        refundCreateReq.setUserIp(userIp);

        log.info("[processAdminCancellationRefundWithData][准备创建退款订单] 报名ID: {}, 退款金额: {}, 商户退款号: {}, 当前报名状态: {}",
                registrationId, refundAmount, merchantRefundId, latestRegistration.getStatus());

        // 6. 调用支付模块创建退款订单
        Long refundId = payRefundApi.createRefund(refundCreateReq);
        log.info("[processAdminCancellationRefundWithData][创建退款订单成功] 报名ID: {}, 退款ID: {}, 退款金额: {}",
                registrationId, refundId, refundAmount);

        // 7. 退还优惠券（如果有）
        returnUsedCoupon(latestRegistration);

        // 8. 更新报名记录
        // 注意：这里不累加退款金额，真正的金额累加在退款回调handleRefundNotify中进行
        boolean updateSuccess = registrationMapper.update(null,
                Wrappers.<RegistrationDO>lambdaUpdate()
                        .eq(RegistrationDO::getId, registrationId)
                        .set(RegistrationDO::getRefundStatus, RefundStatusEnum.REFUNDING.getStatus()) // 退款中
                        .set(RegistrationDO::getCancelReason, reason)
                        .set(RegistrationDO::getUpdateTime, LocalDateTime.now())
        ) > 0;

        if (!updateSuccess) {
            log.error("[processAdminCancellationRefundWithData][更新报名记录失败，可能存在并发修改] 报名ID: {}",
                    registrationId);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_UPDATE_FAILED);
        }

        // 9. 发布事件通知
        publishRefundEvent(latestRegistration, RefundScenario.ADMIN_OPERATION, operatorId);
    }

    /**
     * 检查退款是否已经处理过（幂等性检查）
     *
     * @param registration 报名记录
     * @param payRefundId  支付退款ID
     * @param refundAmount 退款金额
     * @return true-已处理过，false-未处理过
     */
    private boolean isRefundAlreadyProcessed(RegistrationDO registration, Long payRefundId, Integer refundAmount) {
        // 简单的幂等性检查：如果累计退款金额已经包含了这个退款金额，可能已处理过
        Integer currentTotal = registration.getTotalRefundPrice() != null ? registration.getTotalRefundPrice() : 0;
        int actualPaid = registration.getActualPayPrice() != null ? registration.getActualPayPrice() : 0;

        // 如果当前累计退款加上本次退款会超过实付金额，可能是重复处理
        if (currentTotal + refundAmount > actualPaid && currentTotal > 0) {
            log.warn("[isRefundAlreadyProcessed] 可能重复处理：累计退款={}, 本次退款={}, 实付金额={}",
                    currentTotal, refundAmount, actualPaid);
            return true;
        }

        return false;
    }

    /**
     * 根据原始报名状态和退款类型确定目标报名状态
     *
     * @param currentStatus 当前报名状态
     * @param refundStatus  退款状态
     * @return 目标报名状态
     */
    private RegistrationStatusEnum determineTargetRegistrationStatus(Integer currentStatus, RefundStatusEnum refundStatus) {
        // 部分退款：保持原状态
        if (RefundStatusEnum.PARTIAL_REFUNDED.equals(refundStatus)) {
            return getRegistrationStatusByValue(currentStatus);
        }

        // 全额退款：根据原状态决定
        if (RefundStatusEnum.FULL_REFUNDED.equals(refundStatus)) {
            // COMPLETED状态的全额退款保持COMPLETED
            if (RegistrationStatusEnum.COMPLETED.getStatus().equals(currentStatus)) {
                return RegistrationStatusEnum.COMPLETED;
            }

            // SUCCESSFUL状态全额退款改为CANCELLED
            if (RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(currentStatus)) {
                return RegistrationStatusEnum.CANCELLED;
            }

            // 其他状态（PAID, WAIT_LIST等）全额退款改为CANCELLED
            return RegistrationStatusEnum.CANCELLED;
        }

        // 默认保持原状态
        return getRegistrationStatusByValue(currentStatus);
    }

    /**
     * 🔧 简化：根据状态值获取报名状态枚举
     */
    private RegistrationStatusEnum getRegistrationStatusByValue(Integer status) {
        for (RegistrationStatusEnum statusEnum : RegistrationStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return RegistrationStatusEnum.PENDING_PAYMENT; // 默认值
    }

    /**
     * 根据退款状态确定目标支付状态
     *
     * @param refundStatus 退款状态
     * @return 目标支付状态
     */
    private PayStatusEnum determineTargetPaymentStatus(RefundStatusEnum refundStatus) {
        if (RefundStatusEnum.FULL_REFUNDED.equals(refundStatus)) {
            return PayStatusEnum.REFUND;
        } else {
            // 部分退款保持已支付状态
            return PayStatusEnum.PAID;
        }
    }

    /**
     * 🔧 简化：根据状态值获取支付状态枚举
     */
    private PayStatusEnum getPayStatusByValue(Integer status) {
        for (PayStatusEnum statusEnum : PayStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return PayStatusEnum.PAID; // 默认值
    }

    /**
     * 获得自身的代理对象，解决事务传播问题
     */
    private ActivityRefundServiceImpl getSelf() {
        return SpringUtil.getBean(ActivityRefundServiceImpl.class);
    }

    @Override
    public boolean isActivityRefundCompleted(@NotNull Long activityId) {
        log.info("[isActivityRefundCompleted] 检查活动 {} 的退款完成状态", activityId);

        // 查询活动下所有已支付的报名记录（包括各种状态）
        List<RegistrationDO> allPaidRegistrations = registrationMapper.selectList(
                Wrappers.<RegistrationDO>lambdaQuery()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                            RegistrationStatusEnum.PAID.getStatus(),
                            RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                            RegistrationStatusEnum.WAIT_LIST.getStatus(),
                            RegistrationStatusEnum.COMPLETED.getStatus(),
                            RegistrationStatusEnum.CANCELLED.getStatus()) // 包含所有可能的已支付状态
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
        );

        if (allPaidRegistrations.isEmpty()) {
            log.info("[isActivityRefundCompleted] 活动 {} 没有已支付的报名记录，认为退款已完成", activityId);
            return true;
        }

        // 检查是否还有记录需要退款或正在退款中
        for (RegistrationDO registration : allPaidRegistrations) {
            Integer actualPaid = registration.getActualPayPrice() != null ? registration.getActualPayPrice() : 0;
            Integer alreadyRefunded = registration.getTotalRefundPrice() != null ? registration.getTotalRefundPrice() : 0;
            Integer remainingAmount = actualPaid - alreadyRefunded;
            Integer refundStatus = registration.getRefundStatus();

            // 检查是否还在退款中
            if (RefundStatusEnum.REFUNDING.getStatus().equals(refundStatus)) {
                log.info("[isActivityRefundCompleted] 活动 {} 中报名记录 {} 正在退款中", activityId, registration.getId());
                return false;
            }

            // 检查是否还有剩余金额需要退款
            if (remainingAmount > 0) {
                log.info("[isActivityRefundCompleted] 活动 {} 中报名记录 {} 还有剩余金额需要退款: 实付 {}, 已退 {}, 剩余 {}",
                        activityId, registration.getId(), actualPaid, alreadyRefunded, remainingAmount);
                return false;
            }

            // 记录详细的状态信息用于调试
            log.debug("[isActivityRefundCompleted] 报名记录 {} 状态: 退款状态={}, 实付={}, 已退={}, 剩余={}",
                    registration.getId(), refundStatus, actualPaid, alreadyRefunded, remainingAmount);
        }

        log.info("[isActivityRefundCompleted] 活动 {} 所有退款已完成，总记录数: {}", activityId, allPaidRegistrations.size());
        return true;
    }

    // ---------- 私有方法 ----------

    /**
     * 确定退款金额
     */
    private Integer determineRefundAmount(RegistrationDO registration, ActivityDO activity, RefundScenario scenario) {
        // 获取适合的退款计算器
        RefundCalculator calculator = refundCalculatorFactory.getCalculator(activity.getType());

        // 根据不同的退款场景选择不同的计算方法
        switch (scenario) {
            case USER_CANCEL:
                return calculator.calculateCancelRefundAmount(activity, registration);
            case GROUPING_FAILED:
                return calculator.calculateGroupingFailedRefundAmount(activity, registration);
            case DIFFERENCE:
                return calculator.calculateDifferenceRefundAmount(activity, registration);
            case WAIT_FAILED:
                return registration.getActualPayPrice();
            default:
                log.warn("[determineRefundAmount] 未知的退款场景: {}, 报名记录: {}", scenario, registration.getId());
                return registration.getActualPayPrice(); // 默认全额退款
        }
    }

    /**
     * 根据活动类型获取支付应用key
     */
    private String getAppKey(ActivityDO activity) {
        // 这里可以根据活动类型返回不同的payAppKey，简化处理先返回统一的key
        return "activity";
    }

    /**
     * 发布退款相关事件
     */
    private void publishRefundEvent(RegistrationDO registration, RefundScenario scenario, Long operatorId) {
        // 发布报名取消事件
        RegistrationCancelledEvent event = new RegistrationCancelledEvent(
                this, // 使用当前对象作为source
                registration.getId(),
                operatorId,
                "退款场景：" + scenario.getDesc() // 使用退款场景作为取消原因
        );
        eventPublisher.publishEvent(event);
    }

    /**
     * 同步处理退款完成事件，确保关键业务逻辑的事务一致性
     */
    private void publishRefundCompletedEventSynchronously(RegistrationDO registration) {
        // 判断是否需要重新分队
        boolean needsTeamRebalance = isUserInitiatedWithdrawal(registration);

        log.info("[publishRefundCompletedEventSynchronously] 同步处理退款完成事件: registrationId={}, needsTeamRebalance={}",
                registration.getId(), needsTeamRebalance);

        // 同步处理关键业务逻辑（如重新分队）
        if (needsTeamRebalance) {
            handleTeamRebalanceDirectly(registration);
        }
    }

    /**
     * 直接处理重新分队逻辑，在同一事务中执行
     */
    private void handleTeamRebalanceDirectly(RegistrationDO registration) {
        log.info("[handleTeamRebalanceDirectly] 开始同步处理重新分队: registrationId={}", registration.getId());

        try {
            // 1. 获取活动信息
            ActivityDO activity = activityQueryService.getActivity(registration.getActivityId());
            if (activity == null) {
                log.error("[handleTeamRebalanceDirectly] 活动不存在 - 活动ID: {}", registration.getActivityId());
                return;
            }

            // 2. 检查活动类型 - 只有排位赛才有重新分队的概念
            if (!ActivityTypeEnum.RANKING.getType().equals(activity.getType())) {
                log.debug("[handleTeamRebalanceDirectly] 活动类型不需要重新分队 - 活动ID: {}, 类型: {}",
                        activity.getId(), activity.getType());
                return;
            }

            // 3. 检查活动状态 - 只有"组局成功"和"报名中"状态才处理重新分队
            if (!shouldProcessTeamRebalanceForActivity(activity)) {
                log.debug("[handleTeamRebalanceDirectly] 活动状态不支持重新分队 - 活动ID: {}, 状态: {}",
                        activity.getId(), activity.getStatus());
                return;
            }

            log.info("[handleTeamRebalanceDirectly] 开始执行退赛后重新分队 - 活动ID: {}, 报名ID: {}",
                    activity.getId(), registration.getId());

            // 4. 处理候补晋升（在退款申请时立即提升，不等回调）
            List<RegistrationDO> promotedPlayers = waitlistService.promoteFromWaitlist(activity);
            int promotedCount = promotedPlayers.size();

            log.info("[handleTeamRebalanceDirectly] 退赛后候补提升完成 - 活动ID: {}, 提升人数: {}",
                    activity.getId(), promotedCount);

            // 5. 执行重新分队，传入报名记录ID而非球员ID
            processTeamRebalanceInternal(activity, registration.getId(), promotedCount > 0);

            log.info("[handleTeamRebalanceDirectly] 同步重新分队处理完成: registrationId={}, 候补晋升数: {}",
                    registration.getId(), promotedCount);

        } catch (Exception e) {
            log.error("[handleTeamRebalanceDirectly] 同步重新分队处理失败: registrationId={}", registration.getId(), e);
            throw e; // 重新抛出异常，确保事务回滚
        }
    }

    /**
     * 检查活动状态是否支持重新分队
     */
    private boolean shouldProcessTeamRebalanceForActivity(ActivityDO activity) {
        Integer activityStatus = activity.getStatus();
        // 只有"报名中"和"组局成功"状态才允许重新分队
        return ActivityStatusEnum.REGISTRATION.getStatus().equals(activityStatus) ||
                ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus().equals(activityStatus);
    }


    /**
     * 处理重新分队（内部方法）
     *
     * @param activity                活动信息
     * @param withdrawnRegistrationId 退赛报名记录ID
     * @param hasNewPromotions        是否有新的候补晋升
     */
    private void processTeamRebalanceInternal(ActivityDO activity, Long withdrawnRegistrationId, boolean hasNewPromotions) {
        log.info("[processTeamRebalanceInternal] 开始重新分队 - 活动ID: {}, 退赛报名: {}, 有新晋升: {}",
                activity.getId(), withdrawnRegistrationId, hasNewPromotions);

        try {
            if (hasNewPromotions) {
                log.info("[processTeamRebalanceInternal] 有候补晋升，需要重新分队平衡 - 活动ID: {}", activity.getId());
            } else {
                log.info("[processTeamRebalanceInternal] 无候补晋升，退赛处理完成 - 活动ID: {}", activity.getId());
            }

            // 调用分队服务进行重新平衡，传入报名记录ID
            TeamAssignmentResult rebalanceResult = teamAssignmentService.rebalanceAfterWithdrawal(
                    activity.getId(),
                    Collections.singletonList(withdrawnRegistrationId)
            );

            if (rebalanceResult != null && rebalanceResult.isSuccess()) {
                log.info("[processTeamRebalanceInternal] 重新分队成功 - 活动ID: {}, 平衡分数: {}",
                        activity.getId(), rebalanceResult.getBalanceScore());
            } else {
                log.warn("[processTeamRebalanceInternal] 重新分队失败 - 活动ID: {}, 原因: {}",
                        activity.getId(), rebalanceResult != null ? rebalanceResult.getFailureReason() : "未知原因");
            }

        } catch (Exception e) {
            log.error("[processTeamRebalanceInternal] 重新分队处理异常 - 活动ID: {}", activity.getId(), e);
            throw e; // 重新抛出异常，确保事务回滚
        }
    }


    /**
     * 判断是否需要触发重新分队和候补晋升
     * 根据业务要求：所有报名取消都需要触发重新分队，无论是用户主动还是管理员操作
     */
    private boolean isUserInitiatedWithdrawal(RegistrationDO registration) {
        // 🔧 简化修复：所有取消都触发候补晋升逻辑
        // 业务确认：不管是用户主动取消还是管理员取消，都需要重新分队
        log.info("[isUserInitiatedWithdrawal] 所有取消都触发候补晋升 - 报名ID: {}, 取消原因: {}",
                registration.getId(), registration.getCancelReason());
        return true;
    }

    /**
     * 🔧 优化：直接处理0元退款到最终状态
     * 无需走REFUNDING中间状态，一步到位
     */
    @Transactional(rollbackFor = Exception.class)
    public void processZeroRefundDirectly(RegistrationDO registration, ActivityDO activity,
                                          RefundScenario scenario, Long operatorId, String reason) {
        log.info("[processZeroRefundDirectly][开始直接处理0元退款] 报名ID: {}, 退款场景: {}",
                registration.getId(), scenario);

        // 1. 退还优惠券（如果需要）
        if (shouldReturnCoupon(scenario)) {
            returnUsedCoupon(registration);
        }

        // 2. 确定最终状态（直接到最终状态，无需中间状态）
        RefundStatusEnum finalRefundStatus;
        RegistrationStatusEnum finalRegistrationStatus;
        PayStatusEnum finalPaymentStatus;

        if (RefundScenario.DIFFERENCE.equals(scenario)) {
            // 差额退款为0：保持原状态
            finalRefundStatus = RefundStatusEnum.PARTIAL_REFUNDED;
            finalRegistrationStatus = getRegistrationStatusByValue(registration.getStatus());
            finalPaymentStatus = getPayStatusByValue(registration.getPaymentStatus());
        } else if (RefundScenario.ADMIN_OPERATION.equals(scenario) &&
                RegistrationStatusEnum.COMPLETED.getStatus().equals(registration.getStatus())) {
            // 管理员操作且活动已完成：保持COMPLETED状态
            finalRefundStatus = RefundStatusEnum.PARTIAL_REFUNDED;
            finalRegistrationStatus = RegistrationStatusEnum.COMPLETED;
            finalPaymentStatus = PayStatusEnum.PAID;
        } else {
            // 其他场景：标记为已取消
            finalRefundStatus = RefundStatusEnum.FULL_REFUNDED;
            finalRegistrationStatus = RegistrationStatusEnum.CANCELLED;
            finalPaymentStatus = PayStatusEnum.REFUND;
        }

        // 3. 原子性更新到最终状态
        boolean updateSuccess = registrationMapper.update(null,
                Wrappers.<RegistrationDO>lambdaUpdate()
                        .eq(RegistrationDO::getId, registration.getId())
                        .set(RegistrationDO::getRefundStatus, finalRefundStatus.getStatus())
                        .set(RegistrationDO::getStatus, finalRegistrationStatus.getStatus())
                        .set(RegistrationDO::getPaymentStatus, finalPaymentStatus.getStatus())
                        .set(RegistrationDO::getTotalRefundPrice, 0)
                        .set(RegistrationDO::getCancelReason, reason)
                        .set(RegistrationDO::getUpdateTime, LocalDateTime.now())
        ) > 0;

        if (!updateSuccess) {
            log.error("[processZeroRefundDirectly][更新到最终状态失败] 报名ID: {}", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_UPDATE_FAILED);
        }

        log.info("[processZeroRefundDirectly][0元退款直接处理完成] 报名ID: {}, 最终状态: 退款={}, 报名={}, 支付={}",
                registration.getId(), finalRefundStatus, finalRegistrationStatus, finalPaymentStatus);

        // 4. 发布相关事件（确保业务逻辑完整性）
        if (RefundStatusEnum.FULL_REFUNDED.equals(finalRefundStatus)) {
            // 重新查询最新状态用于事件发布
            RegistrationDO updatedRegistration = registrationMapper.selectById(registration.getId());
            publishRefundCompletedEventSynchronously(updatedRegistration);
        }

        publishRefundEvent(registration, scenario, operatorId);
    }


    /**
     * 判断是否需要在特定场景下退还优惠券
     *
     * @param scenario 退款场景
     * @return true-需要退还优惠券，false-不退还优惠券
     */
    private boolean shouldReturnCoupon(RefundScenario scenario) {
        switch (scenario) {
            case GROUPING_FAILED:
            case ADMIN_OPERATION:
            case WAIT_FAILED:
            case SYSTEM_AUTO:
                // 组局失败、管理员操作、系统自动退款：退还优惠券
                return true;
            case DIFFERENCE:
                // 差额退款：不退还优惠券（用户已享受折扣且参与了活动）
                return false;
            case USER_CANCEL:
                // 用户主动取消：根据业务规则决定，当前选择不退还
                return true;
            default:
                // 未知场景：为安全起见，不退还
                log.warn("[shouldReturnCoupon] 未知退款场景: {}", scenario);
                return false;
        }
    }
}