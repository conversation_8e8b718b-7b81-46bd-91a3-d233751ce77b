package cn.iocoder.yudao.module.operation.service.strategy;

import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.GameStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import cn.iocoder.yudao.module.operation.service.strategy.util.FeeCalculationUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象活动策略基类 - 提供通用功能实现
 * <p>
 * 实现了活动策略的通用逻辑，包括：
 * 1. 通用的活动校验
 * 2. 基础的费用计算框架
 * 3. 通用的支付成功处理
 * 4. 共同的组局检查逻辑
 * <p>
 * 子类只需实现特定的业务逻辑差异部分
 */
@Slf4j
public abstract class AbstractActivityStrategy implements ActivityStrategy {

    @Resource
    protected ActivityMapper activityMapper;

    @Resource
    protected RegistrationMapper registrationMapper;

    @Resource
    protected GameService gameService;

    @Resource
    protected ActivityRefundService activityRefundService;


    @Resource
    protected FeeCalculationUtils feeCalculationUtils;

    @Resource
    protected GamePlayerSyncService gamePlayerSyncService;

    @Override
    public int getType() {
        return getActivityType().getType();
    }


    /**
     * 校验报名请求参数（在创建RegistrationDO之前调用）
     *
     * @param activity    活动信息
     * @param createReqVO 报名请求参数
     * @param userId      用户ID
     */
    public void validateRegistrationRequest(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId) {
        log.info("[validateRegistrationRequest] 开始校验报名请求参数: activityId={}, userId={}, activityType={}",
                activity.getId(), userId, getActivityType().getName());

        // 基础参数校验
        validateBasicRequestParameters(activity, createReqVO, userId);

        // 子类特定的请求参数校验
        doValidateRegistrationRequest(activity, createReqVO, userId);

        log.info("[validateRegistrationRequest] 报名请求参数校验通过: activityId={}, userId={}", activity.getId(), userId);
    }

    /**
     * 基础请求参数校验（所有活动类型通用）
     */
    private void validateBasicRequestParameters(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId) {
        // 基础用户校验
        if (userId == null || userId <= 0) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }

        // 基础活动配置校验
        if (activity.getTotalFee() != null && activity.getTotalFee() < 0) {
            throw exception(ErrorCodeConstants.ACTIVITY_CONFIG_ERROR, "活动费用配置错误");
        }
        // 基础活动状态校验（不包含用户相关校验）
        if (!ActivityStatusEnum.REGISTRATION.getStatus().equals(activity.getStatus()) &&
                !ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus().equals(activity.getStatus())) {
            log.warn("[validateAndPopulateActivity] 活动状态不允许报名: activityId={}, status={}",
                    activity.getId(), activity.getStatus());
            throw exception(ErrorCodeConstants.ACTIVITY_NOT_IN_REGISTRATION);
        }

        // 基础时间校验 - 比赛是否已开始
        if (activity.getStartTime() != null && LocalDateTime.now().isAfter(activity.getStartTime())) {
            log.warn("[validateAndPopulateActivity] 比赛已开始，无法报名: activityId={}, startTime={}",
                    activity.getId(), activity.getStartTime());
            throw exception(ErrorCodeConstants.ACTIVITY_REGISTRATION_CLOSED);
        }


        log.debug("[validateBasicRequestParameters] 基础参数校验通过: activityId={}, userId={}", activity.getId(), userId);
    }

    /**
     * 子类实现：校验特定活动类型的请求参数
     *
     * @param activity    活动信息
     * @param createReqVO 报名请求参数
     * @param userId      用户ID
     */
    protected abstract void doValidateRegistrationRequest(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId);

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndProcessGrouping(@NotNull ActivityDO activity) {
        log.info("[checkAndProcessGrouping] 开始检查组局条件: activityId={}, activityType={}",
                activity.getId(), getActivityType().getName());

        // 第一步：检查是否满足组局条件
        GroupingCheckResult checkResult = checkGroupingConditions(activity);

        if (checkResult.isSuccess()) {
            log.info("[checkAndProcessGrouping] 组局成功: activityId={}, playerCount={}",
                    activity.getId(), checkResult.getPlayerCount());

            // 第二步：执行组局前的预处理逻辑（如最终分队算法）
            doPreGroupingLogic(activity, checkResult);

            // 第三步：更新活动状态为组局成功
            updateActivityStatus(activity.getId(), ActivityStatusEnum.GROUPING_SUCCESSFUL);

            // 🔧 第四步：先更新报名状态为成功（PAID → SUCCESSFUL）
            // 这样后续的 createGamesAfterGrouping 方法才能正确查询到记录
            updatePaidRegistrationsToSuccessful(activity.getId());

            // 第五步：创建实际的比赛/联赛结构
            createGamesAfterGrouping(activity);

            // 第六步：执行组局后的后处理逻辑
            doPostGroupingLogic(activity, checkResult);

            // 第七步：子类特定的组局成功处理
            doOnGroupingSuccess(activity, checkResult);

            log.info("[checkAndProcessGrouping] 组局成功处理完成: activityId={}", activity.getId());
        } else {
            log.warn("[checkAndProcessGrouping] 组局失败: activityId={}, reason={}",
                    activity.getId(), checkResult.getFailureReason());

            // 组局失败处理
            handleGroupingFailure(activity, checkResult);

            // 子类特定的组局失败处理
            doOnGroupingFailure(activity, checkResult);

            log.info("[checkAndProcessGrouping] 组局失败处理完成: activityId={}", activity.getId());
        }
    }

    /**
     * 检查组局条件
     */
    protected GroupingCheckResult checkGroupingConditions(ActivityDO activity) {
        // 获取已支付的报名记录
        int paidCount = getPaidRegistrationCount(activity.getId());
        int minRequired = getMinRequiredPlayersForGrouping(activity);

        if (paidCount >= minRequired) {
            return GroupingCheckResult.success(paidCount);
        } else {
            return GroupingCheckResult.failure(
                    String.format("报名人数不足: 当前%d人, 最低要求%d人", paidCount, minRequired)
            );
        }
    }

    /**
     * 获取已支付的报名记录数量
     */
    protected int getPaidRegistrationCount(Long activityId) {
        Long count = registrationMapper.selectCount(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus())
        );
        return count != null ? count.intValue() : 0;
    }

    /**
     * 组局失败处理
     */
    protected void handleGroupingFailure(ActivityDO activity, GroupingCheckResult checkResult) {
        // 更新活动状态为组局失败
        updateActivityStatus(activity.getId(), ActivityStatusEnum.GROUPING_FAILED);

        // 处理组局失败退款
        handleGroupingFailureRefund(activity);
    }

    /**
     * 处理组局失败退款
     */
    protected void handleGroupingFailureRefund(ActivityDO activity) {
        try {
            log.info("[handleGroupingFailureRefund] 开始处理组局失败退款: activityId={}", activity.getId());

            // 🔧 获取所有已支付的报名记录并逐个处理退款（组局失败时查询PAID状态）
            List<RegistrationDO> paidRegistrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<RegistrationDO>()
                            .eq(RegistrationDO::getActivityId, activity.getId())
                            .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus())
            );

            for (RegistrationDO registration : paidRegistrations) {
                try {
                    activityRefundService.processGroupingFailedRefund(registration.getId(), null);
                    log.info("[handleGroupingFailureRefund] 组局失败退款成功: registrationId={}", registration.getId());
                } catch (Exception e) {
                    log.error("[handleGroupingFailureRefund] 组局失败退款异常: registrationId={}", registration.getId(), e);
                }
            }

            log.info("[handleGroupingFailureRefund] 组局失败退款处理完成: activityId={}", activity.getId());
        } catch (Exception e) {
            log.error("[handleGroupingFailureRefund] 组局失败退款处理失败: activityId={}", activity.getId(), e);
            // 不抛出异常，避免影响组局状态更新
        }
    }

    /**
     * 更新活动状态
     */
    protected void updateActivityStatus(Long activityId, ActivityStatusEnum status) {
        try {
            activityMapper.updateById(
                    new ActivityDO()
                            .setId(activityId)
                            .setStatus(status.getStatus())
            );
            log.info("[updateActivityStatus] 活动状态更新成功: activityId={}, status={}",
                    activityId, status.getName());
        } catch (Exception e) {
            log.error("[updateActivityStatus] 活动状态更新失败: activityId={}, status={}",
                    activityId, status.getName(), e);
            throw e;
        }
    }

    // ============== 抽象方法 ==============

    /**
     * 获取活动类型
     */
    public abstract ActivityTypeEnum getActivityType();

    /**
     * 计算实际生效的费用
     */
    public abstract Integer calculateEffectiveFee(@NotNull ActivityDO activity, @NotNull FeeCalculationContext context);

    /**
     * 组局成功后创建实际的比赛/联赛结构
     */
    public abstract void createGamesAfterGrouping(@NotNull ActivityDO activity);

    /**
     * 获取该活动类型组局所需的最低人数/队伍数
     */
    protected abstract int getMinRequiredPlayersForGrouping(ActivityDO activity);

    // ============== 可选重写的钩子方法 ==============


    /**
     * 支付成功后的业务处理
     */
    public abstract void handlePaymentSuccessAfterStateUpdate(ActivityDO activity, RegistrationDO registration);

    /**
     * 处理组局成功后新报名的状态转换
     * 组局成功后的新报名，支付成功后应直接变为 SUCCESSFUL 状态
     */
    protected void handlePostGroupingRegistrationStatus(ActivityDO activity, RegistrationDO registration) {
        if (!ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus().equals(activity.getStatus())) {
            // 如果活动状态为 REGISTRATION，保持默认的 PAID 状态，等待组局时统一更新
            return;
        }

        // 如果活动已组局成功，新报名应直接变为 SUCCESSFUL 状态
        log.info("[handlePostGroupingRegistrationStatus] 活动 {} 已组局成功，报名 {} 直接更新为 SUCCESSFUL 状态",
                activity.getId(), registration.getId());
        updateRegistrationToSuccessful(registration.getId());

        // 🔥 关键：如果比赛已创建，同步球员到比赛球员表
        if (activity.getGameId() != null) {
            try {
                // 重新获取更新后的报名记录
                RegistrationDO updatedRegistration = registrationMapper.selectById(registration.getId());
                if (updatedRegistration != null) {
                    gamePlayerSyncService.addPlayerToGameIfExists(updatedRegistration);
                    log.info("[handlePostGroupingRegistrationStatus] 已同步球员 {} 到比赛 {}",
                            updatedRegistration.getPlayerId(), activity.getGameId());
                }
            } catch (Exception e) {
                log.error("[handlePostGroupingRegistrationStatus] 同步球员到比赛失败: registrationId={}, gameId={}",
                        registration.getId(), activity.getGameId(), e);
                // 不抛出异常，避免影响报名流程
            }
        }

    }

    /**
     * 更新单个报名记录状态为成功
     */
    protected void updateRegistrationToSuccessful(Long registrationId) {
        try {
            registrationMapper.update(
                    new RegistrationDO().setStatus(RegistrationStatusEnum.SUCCESSFUL.getStatus()),
                    new LambdaQueryWrapper<RegistrationDO>()
                            .eq(RegistrationDO::getId, registrationId)
            );
            log.info("[updateRegistrationToSuccessful] 报名状态更新成功: registrationId={}", registrationId);
        } catch (Exception e) {
            log.error("[updateRegistrationToSuccessful] 报名状态更新失败: registrationId={}", registrationId, e);
            throw e;
        }
    }

    /**
     * 组局前的预处理逻辑
     * 例如：排位赛执行最终分队算法，友谊赛确定参赛队伍
     */
    protected void doPreGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        // 默认空实现，子类可选择重写
    }

    /**
     * 组局后的后处理逻辑
     */
    protected void doPostGroupingLogic(ActivityDO activity, GroupingCheckResult checkResult) {
        // 默认空实现：报名状态更新已在前面完成，子类可重写处理其他逻辑
        log.info("[doPostGroupingLogic] 组局后处理逻辑，报名状态已更新完成: activityId={}", activity.getId());
    }

    /**
     * 组局成功的特定处理
     * 默认空实现，子类可选择重写
     */
    protected void doOnGroupingSuccess(ActivityDO activity, GroupingCheckResult checkResult) {
        // 默认空实现
    }

    /**
     * 组局失败的特定处理
     * 默认空实现，子类可选择重写
     */
    protected void doOnGroupingFailure(ActivityDO activity, GroupingCheckResult checkResult) {
        // 默认空实现
    }

    /**
     * 更新已支付报名记录状态为成功
     */
    protected void updatePaidRegistrationsToSuccessful(Long activityId) {
        try {
            // 这里应该通过RegistrationService来更新状态，而不是直接操作数据库
            // 但为了保持当前架构，暂时直接更新
            registrationMapper.update(
                    new RegistrationDO().setStatus(RegistrationStatusEnum.SUCCESSFUL.getStatus()),
                    new LambdaQueryWrapper<RegistrationDO>()
                            .eq(RegistrationDO::getActivityId, activityId)
                            .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PAID.getStatus())
            );
            log.info("[updatePaidRegistrationsToSuccessful] 报名状态更新成功: activityId={}", activityId);
        } catch (Exception e) {
            log.error("[updatePaidRegistrationsToSuccessful] 报名状态更新失败: activityId={}", activityId, e);
            throw e;
        }
    }

    // ============== 定时任务相关方法 ==============

    @Override
    public boolean checkRefundCompletion(@NotNull ActivityDO activity) {
        log.info("[checkRefundCompletion] 检查活动 id={} 的退款完成状态", activity.getId());

        // 查询该活动的所有报名记录
        List<RegistrationDO> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activity.getId())
        );

        if (registrations.isEmpty()) {
            log.warn("[checkRefundCompletion] 活动 id={} 没有报名记录", activity.getId());
            return true; // 没有报名记录的情况下，认为退款已完成
        }

        // 检查是否所有需要退款的报名都已完成退款
        for (RegistrationDO registration : registrations) {
            if (!isRefundCompleted(registration)) {
                log.info("[checkRefundCompletion] 活动 id={} 还有退款未完成，报名ID: {}",
                        activity.getId(), registration.getId());
                return false;
            }
        }

        log.info("[checkRefundCompletion] 活动 id={} 所有退款已完成", activity.getId());
        return true;
    }

    @Override
    public StatusTransitionResult processStatusToInProgress(@NotNull ActivityDO activity) {
        log.info("[processStatusToInProgress] 开始处理活动 id={}, 类型={}, 名称={}",
                activity.getId(), getActivityType().getName(), activity.getName());

        try {
            // 1. 先更新活动状态为进行中，防止重复处理
            updateActivityStatus(activity.getId(), ActivityStatusEnum.IN_PROGRESS);
            log.info("[processStatusToInProgress] 活动 id={} 状态已更新为进行中", activity.getId());

            // 2. 同步更新关联比赛的状态为进行中
            updateRelatedGameStatus(activity);

            // 3. 处理候补球员全额退款 - 失败不影响状态转换
            int refundCount = 0;
            try {
                int waitlistRefundCount = processWaitlistFullRefund(activity);
                log.info("[processStatusToInProgress] 活动 id={} 候补球员全额退款处理成功，处理 {} 笔",
                        activity.getId(), waitlistRefundCount);
                refundCount += waitlistRefundCount;
            } catch (Exception refundException) {
                log.error("[processStatusToInProgress] 活动 id={} 候补球员全额退款处理失败，但不影响状态转换",
                        activity.getId(), refundException);
                // 候补球员退款失败不抛出异常，不影响活动状态转换
            }

            // 4. 处理差额退款（根据活动类型判断）- 失败不影响状态转换
            try {
                refundCount = processDifferentialRefund(activity);
                log.info("[processStatusToInProgress] 活动 id={} 差额退款处理成功，处理 {} 笔", activity.getId(), refundCount);
            } catch (Exception refundException) {
                log.error("[processStatusToInProgress] 活动 id={} 差额退款处理失败，但不影响状态转换",
                        activity.getId(), refundException);
                // 差额退款失败不抛出异常，不影响活动状态转换
            }

            String detail = String.format("活动状态已更新为进行中，关联比赛状态已同步更新，处理差额退款 %d 笔", refundCount);
            log.info("[processStatusToInProgress] 活动 id={} 状态转换成功，{}", activity.getId(), detail);

            return StatusTransitionResult.success(refundCount, detail);

        } catch (Exception e) {
            String errorMessage = "处理活动状态转换异常: " + e.getMessage();
            log.error("[processStatusToInProgress] 活动 id={} {}", activity.getId(), errorMessage, e);
            return StatusTransitionResult.failure(errorMessage);
        }
    }

    /**
     * 检查报名记录的退款是否已完成
     */
    private boolean isRefundCompleted(RegistrationDO registration) {
        // 如果从未支付，则不需要退款
        if (!PayStatusEnum.PAID.getStatus().equals(registration.getPaymentStatus())) {
            return true;
        }
        
        // 如果是取消状态且有退款记录，检查退款状态
        if (RegistrationStatusEnum.CANCELLED.getStatus().equals(registration.getStatus())) {
            Integer refundStatus = registration.getRefundStatus();
            return (RefundStatusEnum.FULL_REFUNDED.getStatus().equals(refundStatus) ||
                    RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(refundStatus));
        }
        
        // 其他状态暂不需要退款（例如正常完成的报名）
        return true;
    }

    /**
     * 处理候补球员全额退款
     * 当活动进入进行中状态时，所有候补球员进行全额退款
     */
    protected int processWaitlistFullRefund(ActivityDO activity) {
        log.info("[processWaitlistFullRefund] 开始处理活动 id={} 的候补球员全额退款", activity.getId());

        // 获取所有候补状态的报名记录
        List<RegistrationDO> waitlistRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activity.getId())
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus(),
                                RegistrationStatusEnum.WAIT_LIST.getStatus())
        );

        if (waitlistRegistrations.isEmpty()) {
            log.info("[processWaitlistFullRefund] 活动 id={} 没有需要退款的候补球员", activity.getId());
            return 0;
        }

        int refundCount = 0;
        for (RegistrationDO registration : waitlistRegistrations) {
            try {
                // 使用系统自动退款场景进行全额退款
                activityRefundService.processRefund(
                        registration.getId(),
                        RefundScenario.WAIT_FAILED,
                        0L,
                        "比赛开始，候补球员全额退款"
                );
                refundCount++;
                log.info("[processWaitlistFullRefund] 候补球员退款成功: registrationId={}", registration.getId());
            } catch (Exception e) {
                log.error("[processWaitlistFullRefund] 候补球员退款失败: registrationId={}", registration.getId(), e);
                // 继续处理下一个，不中断整个流程
            }
        }

        log.info("[processWaitlistFullRefund] 活动 id={} 候补球员全额退款处理完成，成功处理 {} 笔",
                activity.getId(), refundCount);
        return refundCount;
    }

    /**
     * 处理差额退款 - 默认实现，子类可重写
     * 返回处理的退款数量
     */
    protected int processDifferentialRefund(ActivityDO activity) {
        // 默认实现：不进行差额退款
        log.info("[processDifferentialRefund] 活动类型 {} 使用默认差额退款逻辑（无需退款）", 
                getActivityType().getName());
        return 0;
    }

    /**
     * 更新关联比赛的状态
     * 当活动开始时，同步更新比赛状态从"待开始"到"进行中"
     */
    protected void updateRelatedGameStatus(ActivityDO activity) {
        if (activity.getGameId() == null) {
            log.info("[updateRelatedGameStatus] 活动 id={} 没有关联的比赛，跳过比赛状态更新", activity.getId());
            return;
        }

        try {
            // 调用GameService更新比赛状态为进行中
            gameService.updateGameStatusById(activity.getGameId(), GameStatusEnum.STARTED.getStatus());
            log.info("[updateRelatedGameStatus] 活动 id={} 关联比赛 id={} 状态已更新为进行中", 
                    activity.getId(), activity.getGameId());
        } catch (Exception e) {
            log.error("[updateRelatedGameStatus] 更新活动 id={} 关联比赛 id={} 状态失败", 
                    activity.getId(), activity.getGameId(), e);
            // 不抛出异常，避免影响活动状态更新
        }
    }

    // ============= 内部类 =============

    /**
     * 组局检查结果
     */
    @Data
    protected static class GroupingCheckResult {
        private final boolean success;
        private final int playerCount;
        private final String failureReason;

        private GroupingCheckResult(boolean success, int playerCount, String failureReason) {
            this.success = success;
            this.playerCount = playerCount;
            this.failureReason = failureReason;
        }

        public static GroupingCheckResult success(int playerCount) {
            return new GroupingCheckResult(true, playerCount, null);
        }

        public static GroupingCheckResult failure(String reason) {
            return new GroupingCheckResult(false, 0, reason);
        }

    }
} 