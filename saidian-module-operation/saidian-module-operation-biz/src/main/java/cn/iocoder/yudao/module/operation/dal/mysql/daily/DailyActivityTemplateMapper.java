package cn.iocoder.yudao.module.operation.dal.mysql.daily;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.DailyActivityTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.operation.controller.admin.daily.vo.*;

/**
 * 日常活动模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DailyActivityTemplateMapper extends BaseMapperX<DailyActivityTemplateDO> {

    default PageResult<DailyActivityTemplateDO> selectPage(DailyActivityTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DailyActivityTemplateDO>()
                .eqIfPresent(DailyActivityTemplateDO::getTitle, reqVO.getTitle())
                .eqIfPresent(DailyActivityTemplateDO::getMinPlayers, reqVO.getMinPlayers())
                .eqIfPresent(DailyActivityTemplateDO::getMaxPlayers, reqVO.getMaxPlayers())
                .eqIfPresent(DailyActivityTemplateDO::getPrice, reqVO.getPrice())
                .eqIfPresent(DailyActivityTemplateDO::getMode, reqVO.getMode())
                .eqIfPresent(DailyActivityTemplateDO::getType, reqVO.getType())
                .eqIfPresent(DailyActivityTemplateDO::getPicUrl, reqVO.getPicUrl())
                .eqIfPresent(DailyActivityTemplateDO::getCourtPrice, reqVO.getCourtPrice())
                .eqIfPresent(DailyActivityTemplateDO::getRefereeCount, reqVO.getRefereeCount())
                .eqIfPresent(DailyActivityTemplateDO::getRefereePrice, reqVO.getRefereePrice())
                .eqIfPresent(DailyActivityTemplateDO::getPhotographerCount, reqVO.getPhotographerCount())
                .eqIfPresent(DailyActivityTemplateDO::getPhotographerPrice, reqVO.getPhotographerPrice())
                .eqIfPresent(DailyActivityTemplateDO::getVideographerCount, reqVO.getVideographerCount())
                .eqIfPresent(DailyActivityTemplateDO::getVideographerPrice, reqVO.getVideographerPrice())
                .eqIfPresent(DailyActivityTemplateDO::getRecorderCount, reqVO.getRecorderCount())
                .eqIfPresent(DailyActivityTemplateDO::getRecorderPrice, reqVO.getRecorderPrice())
                .eqIfPresent(DailyActivityTemplateDO::getGiveIntegral, reqVO.getGiveIntegral())
                .eqIfPresent(DailyActivityTemplateDO::getDescription, reqVO.getDescription())
                .eqIfPresent(DailyActivityTemplateDO::getLocation, reqVO.getLocation())
                .betweenIfPresent(DailyActivityTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DailyActivityTemplateDO::getId));
    }

}