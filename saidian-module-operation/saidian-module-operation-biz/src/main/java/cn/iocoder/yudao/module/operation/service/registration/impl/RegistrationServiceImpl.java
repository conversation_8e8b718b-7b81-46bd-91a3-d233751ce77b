package cn.iocoder.yudao.module.operation.service.registration.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityRegistrationDetailRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityRespVO;
import cn.iocoder.yudao.module.operation.convert.activity.ActivityConvert;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.activity.FriendlyPaymentTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.pay.enums.order.PayOrderStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.TeamInfoRespVO;
import cn.iocoder.yudao.module.operation.service.strategy.util.FeeCalculationUtils;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationDetailRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationSettlementReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationSettlementRespVO;
import cn.iocoder.yudao.module.operation.convert.registration.RegistrationConvert;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.friendgroup.FriendGroupDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.friendgroup.FriendGroupMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.friendgroup.FriendGroupStatusEnum;
import cn.iocoder.yudao.module.operation.service.activity.ActivityService;
import cn.iocoder.yudao.module.operation.service.friendgroup.FriendGroupService;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationService;
import cn.iocoder.yudao.module.operation.service.registration.bo.RegistrationSettlementBO;
import cn.iocoder.yudao.module.operation.service.registration.bo.RegistrationCreateBO;
import cn.iocoder.yudao.module.operation.service.state.RegistrationState;
import cn.iocoder.yudao.module.operation.service.state.factory.RegistrationStateFactory;
import cn.iocoder.yudao.module.operation.service.strategy.ActivityStrategy;
import cn.iocoder.yudao.module.operation.service.strategy.factory.ActivityStrategyFactory;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import cn.iocoder.yudao.module.pay.api.order.PayOrderApi;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderRespDTO;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.module.operation.service.discount.DiscountService;
import cn.iocoder.yudao.module.operation.service.discount.dto.RegistrationPricingResult;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import cn.iocoder.yudao.module.operation.service.strategy.validator.ActivityValidator;
import cn.iocoder.yudao.module.operation.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.CacheEvict;

/**
 * 活动报名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RegistrationServiceImpl implements RegistrationService {

    @Value("${saidian.pay.activity.payAppKey:activity}")
    private String payAppKey;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private ActivityService activityService;

    @Resource
    private PayOrderApi payOrderApi;

    @Resource
    private ActivityStrategyFactory activityStrategyFactory;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private FriendGroupMapper friendGroupMapper;

    @Resource
    private RegistrationStateFactory stateFactory;

    @Resource
    private FriendGroupService friendGroupService;

    @Resource
    private DiscountService discountService;

    @Resource
    private PlayerService playerService;

    @Resource
    private WaitlistService waitlistService;

    @Resource
    private ActivityValidator activityValidator;

    @Resource
    private FeeCalculationUtils feeCalculationUtils;

    /**
     * 好友组最大成员数
     */
    private static final int MAX_FRIENDS_IN_GROUP = 3;

    // 新增私有方法：计算原始报名价格
    private Integer calculateOriginalRegistrationPrice(ActivityDO activity, ActivityStrategy strategy, Long teamId,
                                                       Integer paymentType, AppRegistrationCreateReqVO createReqVO) {
        ActivityStrategy.FeeCalculationContext feeContext = new ActivityStrategy.FeeCalculationContext(teamId,
                paymentType, createReqVO);
        return strategy.calculateEffectiveFee(activity, feeContext);
    }

    @Override
    public AppRegistrationSettlementRespVO settlementRegistration(Long userId,
                                                                  AppRegistrationSettlementReqVO settlementReqVO) {
        log.info("[settlementRegistration] 开始处理用户 {} 的报名结算请求: {}", userId, settlementReqVO);

        // 1. 创建并初始化 RegistrationSettlementBO
        RegistrationSettlementBO settlementBO = createInitialSettlementBO(userId, settlementReqVO);
        // 2. 填充活动信息
        populateActivityInfo(settlementBO, settlementReqVO.getActivityId());
        // 3. 填充球员信息
        populatePlayerInfo(settlementBO, userId);
        // 4. 计算原始费用并填充费用信息
        populateFeeCalculation(settlementBO, settlementReqVO);
        // 5. 调用优惠服务计算结算详情 (不执行实际核销)
        AppRegistrationSettlementRespVO respVO = discountService.calculateSettlementDetails(userId,
                createDiscountReqVO(settlementBO), settlementBO.getActivityInfo().getActivityDO());
        // 6. 补充球员档案信息到响应
        supplementPlayerProfileInfo(respVO, settlementBO, settlementReqVO);
        // 7. 补充好友组队信息到响应（如果有）
        supplementFriendGroupInfo(respVO, userId, settlementReqVO);

        log.info("[settlementRegistration] 完成用户 {} 的报名结算处理", userId);
        return respVO;
    }

    /**
     * 创建并初始化 RegistrationSettlementBO
     */
    private RegistrationSettlementBO createInitialSettlementBO(Long userId,
                                                               AppRegistrationSettlementReqVO settlementReqVO) {
        return RegistrationSettlementBO.builder()
                .activityInfo(RegistrationSettlementBO.ActivityInfoBO.builder()
                        .id(settlementReqVO.getActivityId())
                        .build())
                .playerProfile(RegistrationSettlementBO.PlayerProfileBO.builder().build())
                .feeCalculation(RegistrationSettlementBO.FeeCalculationBO.builder()
                        .selectedCouponId(settlementReqVO.getCouponId())
                        .pointsUsed(settlementReqVO.getPointsToUse())
                        .paymentType(settlementReqVO.getPaymentType())
                        .build())
                .userPoints(RegistrationSettlementBO.UserPointsBO.builder().build())
                .activityFeeStructure(RegistrationSettlementBO.ActivityFeeStructureBO.builder().build())
                .build();
    }

    /**
     * 填充活动信息到 SettlementBO
     */
    private void populateActivityInfo(RegistrationSettlementBO settlementBO, Long activityId) {
        ActivityDO activity = activityService.getActivity(activityId);
        if (activity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 更新活动信息BO
        RegistrationSettlementBO.ActivityInfoBO activityInfo = settlementBO.getActivityInfo();
        activityInfo.setId(activity.getId());
        activityInfo.setTitle(activity.getName());
        activityInfo.setStartTime(activity.getStartTime());
        activityInfo.setLocation(activity.getLocation());
        activityInfo.setActivityType(activity.getType());
        // 保存完整的 ActivityDO 以供后续使用
        activityInfo.setActivityDO(activity);
    }

    /**
     * 填充球员信息到 SettlementBO
     */
    private void populatePlayerInfo(RegistrationSettlementBO settlementBO, Long userId) {
        PlayerDO player = null;
        try {
            // 直接使用 PlayerService 获取球员信息，不使用 PlayerApi
            player = playerService.getPlayerByUserId(userId);
        } catch (Exception e) {
            log.warn("[populatePlayerInfo] 获取用户 {} 球员信息失败: {}", userId, e.getMessage());
        }

        RegistrationSettlementBO.PlayerProfileBO playerProfile = settlementBO.getPlayerProfile();
        if (player == null) {
            // 新用户，需要完善球员档案
            playerProfile.setIsNewUser(true);
            log.info("[populatePlayerInfo] 用户 {} 为新用户（球员信息不存在），需要完善球员档案", userId);
        } else {
            // 判断是否为新用户：根据球员姓名是否为"用户"+6位数字的格式
            boolean isNewUser = isDefaultPlayerName(player.getName());

            playerProfile.setIsNewUser(isNewUser);
            playerProfile.setPlayerId(player.getId());
            playerProfile.setPlayerName(player.getName());
            playerProfile.setPlayerAvatar(player.getAvatar());
            playerProfile.setJerseyNumber(player.getNumber() != null ? player.getNumber().toString() : null);
            playerProfile.setSex(player.getSex());
            playerProfile.setPosition(player.getPosition());
            playerProfile.setHeight(player.getHeight() != null ? player.getHeight().intValue() : null);
            playerProfile.setWeight(player.getWeight());
            playerProfile.setPlayerDO(player);

            if (isNewUser) {
                log.info("[populatePlayerInfo] 用户 {} 为新用户（球员姓名为默认格式: {}），需要完善球员档案", userId, player.getName());
            } else {
                log.info("[populatePlayerInfo] 用户 {} 已有完整球员档案，球员ID: {}, 姓名: {}", userId, player.getId(),
                        player.getName());
            }
        }
    }

    /**
     * 判断是否为默认的球员姓名（"用户" + 6位数字）
     */
    private boolean isDefaultPlayerName(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return true;
        }

        // 检查是否匹配"用户" + 6位数字的格式
        String pattern = "^用户\\d{6}$";
        boolean isDefault = playerName.matches(pattern);

        log.debug("[isDefaultPlayerName] 球员姓名: {}, 是否为默认格式: {}", playerName, isDefault);
        return isDefault;
    }

    /**
     * 计算原始费用并填充费用信息到 SettlementBO
     */
    private void populateFeeCalculation(RegistrationSettlementBO settlementBO,
                                        AppRegistrationSettlementReqVO settlementReqVO) {
        ActivityDO activity = settlementBO.getActivityInfo().getActivityDO();
        ActivityStrategy strategy = activityStrategyFactory.getStrategy(activity.getType());

        // 计算原始费用
        Integer originalPrice = calculateOriginalRegistrationPrice(activity, strategy,
                settlementReqVO.getTeamId(), settlementReqVO.getPaymentType(),
                AppRegistrationCreateReqVO.builder().activityId(settlementReqVO.getActivityId())
                        .teamId(settlementReqVO.getTeamId()).paymentType(settlementReqVO.getPaymentType()).build());

        // 更新费用计算BO
        RegistrationSettlementBO.FeeCalculationBO feeCalculation = settlementBO.getFeeCalculation();
        feeCalculation.setOriginalAmount(originalPrice);
        feeCalculation.setFinalPayAmount(originalPrice); // 初始值，会被优惠服务更新

        log.info("[populateFeeCalculation] 活动 {} 计算原始费用: {} 分", activity.getId(), originalPrice);
    }

    /**
     * 创建用于优惠服务的请求VO
     */
    private AppRegistrationSettlementReqVO createDiscountReqVO(RegistrationSettlementBO settlementBO) {
        AppRegistrationSettlementReqVO reqVO = new AppRegistrationSettlementReqVO();
        reqVO.setActivityId(settlementBO.getActivityInfo().getId());
        reqVO.setOriginalPrice(settlementBO.getFeeCalculation().getOriginalAmount());
        reqVO.setCouponId(settlementBO.getFeeCalculation().getSelectedCouponId());
        reqVO.setPointsToUse(settlementBO.getFeeCalculation().getPointsUsed());
        reqVO.setUsePoints(settlementBO.getFeeCalculation().getPointsUsed() != null
                && settlementBO.getFeeCalculation().getPointsUsed() > 0);
        return reqVO;
    }

    /**
     * 补充球员档案信息到响应VO
     */
    private void supplementPlayerProfileInfo(AppRegistrationSettlementRespVO respVO,
                                             RegistrationSettlementBO settlementBO,
                                             AppRegistrationSettlementReqVO settlementReqVO) {
        RegistrationSettlementBO.PlayerProfileBO playerProfile = settlementBO.getPlayerProfile();

        AppRegistrationSettlementRespVO.PlayerProfileVO playerProfileVO = new AppRegistrationSettlementRespVO.PlayerProfileVO();
        playerProfileVO.setIsNewUser(playerProfile.getIsNewUser());
        playerProfileVO.setPlayerId(playerProfile.getPlayerId());
        playerProfileVO.setPlayerName(playerProfile.getPlayerName());
        playerProfileVO.setPlayerAvatar(playerProfile.getPlayerAvatar());
        playerProfileVO.setJerseyNumber(playerProfile.getJerseyNumber());
        playerProfileVO.setSex(playerProfile.getSex());
        playerProfileVO.setPosition(playerProfile.getPosition());
        playerProfileVO.setHeight(playerProfile.getHeight());
        playerProfileVO.setWeight(playerProfile.getWeight());

        respVO.setPlayerProfile(playerProfileVO);

        // 补充活动原始费用信息，包括友谊赛固定费用和球队支付方式
        supplementActivityOriginalFeeInfo(respVO, settlementBO, settlementReqVO);
    }

    /**
     * 补充活动原始费用信息（重载方法，为了传递settlementReqVO）
     */
    private void supplementActivityOriginalFeeInfo(AppRegistrationSettlementRespVO respVO,
                                                   RegistrationSettlementBO settlementBO,
                                                   AppRegistrationSettlementReqVO settlementReqVO) {
        ActivityDO activity = settlementBO.getActivityInfo().getActivityDO();

        // 如果已经有activityOriginalFee，则补充友谊赛字段
        AppRegistrationSettlementRespVO.ActivityOriginalFeeVO originalFeeVO = respVO.getActivityOriginalFee();
        if (originalFeeVO == null) {
            originalFeeVO = new AppRegistrationSettlementRespVO.ActivityOriginalFeeVO();
            respVO.setActivityOriginalFee(originalFeeVO);
        }

        // 计算友谊赛固定费用
        if (ActivityTypeEnum.isFriendly(activity.getType())) {
            Integer totalFee = activity.getTotalFee() != null ? activity.getTotalFee() : 0;

            // 队伍费用 = 总费用的一半（向上取整）
            Integer teamFee = feeCalculationUtils.calculateHalfFee(totalFee, FeeCalculationUtils.RoundingMode.CEIL);
            originalFeeVO.setFriendlyTeamFee(teamFee);

            // AA制人均费用 = 队伍费用除以最小人数（向上取整）
            Integer minPlayers = activity.getMinPlayersPerTeamFriendly() != null ?
                    activity.getMinPlayersPerTeamFriendly() : 5;
            Integer aaFee = feeCalculationUtils.calculatePerPlayerFee(teamFee, minPlayers, FeeCalculationUtils.RoundingMode.CEIL);
            originalFeeVO.setFriendlyAAFee(aaFee);

            // 获取球队已确定的支付方式（友谊赛特有逻辑）
            if (settlementReqVO.getTeamId() != null) {
                try {
                    ActivityStrategy strategy = activityStrategyFactory.getStrategy(activity.getType());
                    Integer teamPaymentType = strategy.getTeamPaymentType(activity.getId(), settlementReqVO.getTeamId());
                    originalFeeVO.setTeamPaymentType(teamPaymentType);
                    log.info("[supplementActivityOriginalFeeInfo] 球队 {} 支付方式: {}",
                            settlementReqVO.getTeamId(), teamPaymentType);
                } catch (Exception e) {
                    log.warn("[supplementActivityOriginalFeeInfo] 获取球队支付方式失败: activityId={}, teamId={}",
                            activity.getId(), settlementReqVO.getTeamId(), e);
                    originalFeeVO.setTeamPaymentType(null);
                }
            }

            log.info("[supplementActivityOriginalFeeInfo] 友谊赛固定费用计算: totalFee={}, teamFee={}, aaFee={}, minPlayers={}",
                    totalFee, teamFee, aaFee, minPlayers);
        }
    }

    /**
     * 补充好友组队信息到响应VO
     */
    private void supplementFriendGroupInfo(AppRegistrationSettlementRespVO respVO, Long userId,
                                           AppRegistrationSettlementReqVO settlementReqVO) {
        // 只有好友组队报名模式或者提供了roomId时才处理
        if (settlementReqVO.getRoomId() == null) {
            return;
        }

        try {
            // 获取好友组队信息
            FriendGroupDO friendGroup = friendGroupService.getFriendGroup(settlementReqVO.getRoomId());
            if (friendGroup == null) {
                log.warn("[supplementFriendGroupInfo] 未找到好友组队房间: roomId={}", settlementReqVO.getRoomId());
                return;
            }

            // 获取房间成员的报名信息
            List<RegistrationDO> groupRegistrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<RegistrationDO>()
                            .eq(RegistrationDO::getActivityId, settlementReqVO.getActivityId())
                            .eq(RegistrationDO::getFriendGroupId, settlementReqVO.getRoomId())
            );

            // 构建好友组队信息VO
            AppRegistrationSettlementRespVO.FriendGroupInfoVO friendGroupInfoVO =
                    new AppRegistrationSettlementRespVO.FriendGroupInfoVO();
            friendGroupInfoVO.setRoomId(friendGroup.getId());
            friendGroupInfoVO.setOwnerUserId(friendGroup.getLeaderUserId());
            friendGroupInfoVO.setStatus(friendGroup.getStatus());
            friendGroupInfoVO.setMaxMembers(3); // 固定为3人（房主+2个好友）
            friendGroupInfoVO.setInviteCode(friendGroup.getInviteCode());

            // 设置过期时间
            if (friendGroup.getExpiresAt() != null) {
                friendGroupInfoVO.setExpireTime(friendGroup.getExpiresAt().toEpochSecond(java.time.ZoneOffset.of("+08:00")));
            }

            // 构建成员列表
            List<AppRegistrationSettlementRespVO.PlayerSignupInfoVO> members = groupRegistrations.stream()
                    .filter(reg -> reg.getPlayerId() != null)
                    .map(this::buildPlayerSignupInfo)
                    .collect(Collectors.toList());
            friendGroupInfoVO.setMembers(members);

            respVO.setFriendGroupInfo(friendGroupInfoVO);
            log.info("[supplementFriendGroupInfo] 成功补充好友组队信息: roomId={}, memberCount={}",
                    settlementReqVO.getRoomId(), members.size());

        } catch (Exception e) {
            log.warn("[supplementFriendGroupInfo] 获取好友组队信息失败: userId={}, roomId={}, error={}",
                    userId, settlementReqVO.getRoomId(), e.getMessage());
        }
    }

    /**
     * 构建球员报名信息VO
     */
    private AppRegistrationSettlementRespVO.PlayerSignupInfoVO buildPlayerSignupInfo(RegistrationDO registration) {
        AppRegistrationSettlementRespVO.PlayerSignupInfoVO playerInfo =
                new AppRegistrationSettlementRespVO.PlayerSignupInfoVO();
        playerInfo.setId(registration.getId());
        playerInfo.setUserId(registration.getUserId());

        // 获取球员详细信息
        try {
            PlayerDO player = playerService.getPlayer(registration.getPlayerId());
            if (player != null) {
                playerInfo.setNickname(player.getName());
                playerInfo.setAvatar(player.getAvatar());
                playerInfo.setPosition(player.getPosition());
                // PlayerDO 中暂时没有技能评分字段，设置为null
                playerInfo.setScore(null);
            }
        } catch (Exception e) {
            log.warn("[buildPlayerSignupInfo] 获取球员信息失败: playerId={}, error={}",
                    registration.getPlayerId(), e.getMessage());
            playerInfo.setNickname("球员" + registration.getPlayerId());
        }

        return playerInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public AppRegistrationRespVO createRegistration(Long userId, AppRegistrationCreateReqVO createReqVO) {
        log.info("[createRegistration] 开始处理用户 {} 的报名创建请求: {}", userId, createReqVO);

        // 1. 创建并初始化 RegistrationCreateBO
        RegistrationCreateBO createBO = createInitialCreateBO(userId, createReqVO);
        // 2. 基础校验和活动信息填充
        validateAndPopulateActivity(createBO, createReqVO.getActivityId());
        // 3. 处理球员信息（新用户档案创建/更新或现有用户验证）
        handlePlayerProfile(createBO, createReqVO);
        // 4. 计算原始费用
        calculateOriginalFee(createBO);

        // 5. 检查活动是否已满，如果满了，标记为候补
        boolean isWaitlist = checkAndMarkAsWaitlistIfNeeded(createBO);

        // 6. 创建初始报名记录
        createInitialRegistration(createBO);
        // 7. 应用优惠并最终确定价格
        applyDiscountsAndFinalizePricing(createBO);
        // 8. 处理支付逻辑
        AppRegistrationRespVO respVO = handlePaymentProcess(createBO);

        // 9. 如果是候补，更新响应信息
        if (isWaitlist) {
            respVO.setIsWaitlist(true);
            respVO.setWaitlistPosition(createBO.getRegistration().getWaitlistPosition());
        }

        log.info("[createRegistration] 完成用户 {} 的报名创建处理", userId);
        return respVO;
    }

    /**
     * 检查活动是否已满，如果满了，标记为候补
     *
     * @param createBO 创建BO
     * @return 是否为候补
     */
    private boolean checkAndMarkAsWaitlistIfNeeded(RegistrationCreateBO createBO) {
        ActivityDO activity = createBO.getActivity();

        // 检查活动是否已满
        boolean isFull = waitlistService.isActivityFull(activity);
        if (isFull) {
            log.info("[checkAndMarkAsWaitlistIfNeeded] 活动 {} 已满员，用户 {} 将进入候补队列",
                    activity.getId(), createBO.getUserId());
            createBO.setWaitlist(true);
            return true;
        }

        return false;
    }

    /**
     * 创建并初始化 RegistrationCreateBO
     */
    private RegistrationCreateBO createInitialCreateBO(Long userId, AppRegistrationCreateReqVO createReqVO) {
        return RegistrationCreateBO.builder()
                .userId(userId)
                .createReqVO(createReqVO)
                .waitlist(false)
                .build();
    }

    /**
     * 基础校验和活动信息填充
     */
    private void validateAndPopulateActivity(RegistrationCreateBO createBO, Long activityId) {
        // 1. 基础校验：仅进行活动存在性、基础状态和时间校验
        ActivityDO activity = activityValidator.validateActivityExists(activityId);

        // 2. 填充活动信息
        createBO.setActivity(activity);

        // 3. 获取活动策略并进行请求参数校验
        ActivityStrategy strategy = activityStrategyFactory.getStrategy(activity.getType());
        strategy.validateRegistrationRequest(activity, createBO.getCreateReqVO(), createBO.getUserId());
        createBO.setActivityStrategy(strategy);

        // 4. 重复报名校验
        validateDuplicateRegistration(createBO.getUserId(), activity.getId());
        log.info("[validateAndPopulateActivity] 活动 {} 校验通过，类型: {}", activity.getId(), activity.getType());
    }

    /**
     * 处理球员信息（新用户档案创建/更新或现有用户验证）
     */
    private void handlePlayerProfile(RegistrationCreateBO createBO, AppRegistrationCreateReqVO createReqVO) {
        // 卫语句：如果未传递球员档案信息，直接使用现有用户的球员档案
        if (createReqVO.getPlayerProfile() == null) {
            PlayerDO existingPlayer = playerService.getPlayerByUserId(createBO.getUserId());
            if (existingPlayer == null) {
                log.error("[handlePlayerProfile] 用户 {} 未传递球员档案信息且无现有球员档案", createBO.getUserId());
                throw exception(ErrorCodeConstants.PLAYER_NOT_FOUND_BY_USER);
            }
            // 直接使用现有球员档案，跳过档案处理环节
            useExistingPlayerProfile(createBO, existingPlayer);
            log.info("[handlePlayerProfile] 用户 {} 未传递球员档案信息，直接使用现有球员档案，球员ID: {}",
                    createBO.getUserId(), existingPlayer.getId());
            return;
        }

        // 处理传递了球员档案信息的情况：检查用户是否已有球员档案
        PlayerDO existingPlayer = playerService.getPlayerByUserId(createBO.getUserId());

        // 卫语句：如果是新用户（无球员档案），处理新用户创建
        if (existingPlayer == null) {
            handleNewUserPlayerProfile(createBO, createReqVO);
            return;
        }

        // 处理已有球员档案的用户
        handleExistingUserPlayerProfile(createBO, createReqVO, existingPlayer);
    }

    /**
     * 处理新用户的球员档案创建
     */
    private void handleNewUserPlayerProfile(RegistrationCreateBO createBO, AppRegistrationCreateReqVO createReqVO) {
        Long userId = createBO.getUserId();

        // 卫语句：检查是否提供了球员档案信息
        if (createReqVO.getPlayerProfile() == null) {
            log.error("[handleNewUserPlayerProfile] 用户 {} 为新用户但未提供球员档案信息", userId);
            throw exception(ErrorCodeConstants.PLAYER_NOT_FOUND_BY_USER);
        }

        log.info("[handleNewUserPlayerProfile] 用户 {} 为新用户，创建球员档案", userId);

        // 委托给PlayerService创建球员档案
        try {
            PlayerDO newPlayer = playerService.createPlayerFromProfile(userId, createReqVO.getPlayerProfile());
            createBO.setResolvedPlayerId(newPlayer.getId());
            createBO.setPlayer(newPlayer);
            log.info("[handleNewUserPlayerProfile] 用户 {} 球员档案创建成功，球员ID: {}", userId, newPlayer.getId());
        } catch (Exception e) {
            log.error("[handleNewUserPlayerProfile] 用户 {} 创建球员档案失败", userId, e);
            throw exception(ErrorCodeConstants.PLAYER_STATUS_ERROR);
        }

        // 如果档案不完整，提示用户
        if (createReqVO.getPlayerProfile().getName() == null
                || createReqVO.getPlayerProfile().getName().trim().isEmpty()) {
            log.error("[handleNewUserPlayerProfile] 用户 {} 为新用户但未提供完整球员档案信息，请先完善档案", userId);
            throw exception(ErrorCodeConstants.PLAYER_PROFILE_REQUIRED);
        }
    }

    /**
     * 处理已有球员档案的用户
     */
    private void handleExistingUserPlayerProfile(RegistrationCreateBO createBO, AppRegistrationCreateReqVO createReqVO,
                                                 PlayerDO existingPlayer) {
        Long userId = createBO.getUserId();

        // 卫语句：检查是否为默认姓名，如果不是则直接使用现有档案
        if (!isDefaultPlayerName(existingPlayer.getName())) {
            useExistingPlayerProfile(createBO, existingPlayer);
            return;
        }

        // 处理默认姓名用户的档案完善
        handleDefaultNamePlayerProfile(createBO, createReqVO, existingPlayer);
    }

    /**
     * 使用现有的球员档案
     */
    private void useExistingPlayerProfile(RegistrationCreateBO createBO, PlayerDO existingPlayer) {
        createBO.setResolvedPlayerId(existingPlayer.getId());
        createBO.setPlayer(existingPlayer);
        log.info("[useExistingPlayerProfile] 用户 {} 使用现有完整球员档案，球员ID: {}",
                createBO.getUserId(), existingPlayer.getId());
    }

    /**
     * 处理默认姓名用户的档案完善
     */
    private void handleDefaultNamePlayerProfile(RegistrationCreateBO createBO, AppRegistrationCreateReqVO createReqVO,
                                                PlayerDO existingPlayer) {
        Long userId = createBO.getUserId();

        // 卫语句：检查是否提供了完善信息
        if (createReqVO.getPlayerProfile() != null) {
            log.info("[handleDefaultNamePlayerProfile] 用户 {} 球员姓名为默认格式（{}），提供了完善信息，更新档案",
                    userId, existingPlayer.getName());

            // 委托给PlayerService更新球员档案
            try {
                PlayerDO updatedPlayer = playerService.updatePlayerProfile(existingPlayer.getId(),
                        createReqVO.getPlayerProfile());
                createBO.setResolvedPlayerId(updatedPlayer.getId());
                createBO.setPlayer(updatedPlayer);
                log.info("[handleDefaultNamePlayerProfile] 用户 {} 球员档案更新成功", userId);
            } catch (Exception e) {
                log.error("[handleDefaultNamePlayerProfile] 用户 {} 更新球员档案失败", userId, e);
                // 更新失败，仍然使用原有档案
                useExistingPlayerProfile(createBO, existingPlayer);
            }

            log.info("[handleDefaultNamePlayerProfile] 用户 {} 提供了完善信息，暂时使用现有档案", userId);
            useExistingPlayerProfile(createBO, existingPlayer);
        } else {
            // 没有提供完善信息，报错
            log.error("[handleDefaultNamePlayerProfile] 用户 {} 球员姓名为默认格式（{}），需要先完善球员档案信息",
                    userId, existingPlayer.getName());
            throw exception(ErrorCodeConstants.PLAYER_PROFILE_REQUIRED);
        }
    }

    /**
     * 计算原始费用
     */
    private void calculateOriginalFee(RegistrationCreateBO createBO) {
        ActivityDO activity = createBO.getActivity();
        ActivityStrategy strategy = createBO.getActivityStrategy();
        AppRegistrationCreateReqVO createReqVO = createBO.getCreateReqVO();

        // 确定支付类型
        Integer teamPaymentType = strategy.getTeamPaymentType(activity.getId(), createReqVO.getTeamId());
        Integer finalPaymentType = teamPaymentType != null ? teamPaymentType : createReqVO.getPaymentType();

        // 计算原始费用
        Integer originalPrice = calculateOriginalRegistrationPrice(activity, strategy,
                createReqVO.getTeamId(), finalPaymentType, createReqVO);

        createBO.setOriginalPrice(originalPrice);
        createBO.setPaymentType(finalPaymentType);

        log.info("[calculateOriginalFee] 活动 {} 计算原始费用: {} 分", activity.getId(), originalPrice);
    }

    /**
     * 创建初始报名记录
     */
    private void createInitialRegistration(RegistrationCreateBO createBO) {
        AppRegistrationCreateReqVO createReqVO = createBO.getCreateReqVO();

        // 初始化报名记录
        RegistrationDO registration = RegistrationConvert.INSTANCE.convert(createReqVO);
        registration.setUserId(createBO.getUserId());
        registration.setPlayerId(createBO.getResolvedPlayerId());
        registration.setActivityType(createBO.getActivity().getType());
        registration.setStatus(RegistrationStatusEnum.PENDING_PAYMENT.getStatus());
        registration.setPaymentStatus(PayStatusEnum.UNPAID.getStatus());
        registration.setRegistrationTime(LocalDateTime.now());
        registration.setPaymentType(createBO.getPaymentType());
        registration.setShouldPayPrice(createBO.getOriginalPrice());

        // 设置候补状态
        registration.setIsWaitlist(createBO.getWaitlist());

        // 插入数据库获取ID
        registrationMapper.insert(registration);
        createBO.setRegistration(registration);

        // 如果是候补状态，添加到候补队列
        if (createBO.getWaitlist()) {
            int position = waitlistService.addToWaitlist(registration, createBO.getActivity());
            log.info("[createInitialRegistration] 报名记录 {} 添加到候补队列，位置: {}", registration.getId(), position);
        } else {
            log.info("[createInitialRegistration] 报名记录初步创建，ID: {}", registration.getId());
        }
    }

    /**
     * 应用优惠并最终确定价格
     */
    private void applyDiscountsAndFinalizePricing(RegistrationCreateBO createBO) {
        AppRegistrationCreateReqVO createReqVO = createBO.getCreateReqVO();
        RegistrationDO registration = createBO.getRegistration();

        // 设置原始价格到请求VO中供优惠服务使用
        createReqVO.setOriginalPrice(createBO.getOriginalPrice());

        // 调用优惠服务应用优惠
        RegistrationPricingResult pricingResult = discountService.applyAndFinalizeDiscounts(
                createBO.getUserId(),
                registration.getId().toString(),
                createBO.getActivity(),
                createReqVO);

        // 更新报名记录的价格信息
        registration.setActualPayPrice(pricingResult.getFinalPrice());
        registration.setCouponId(pricingResult.getCouponIdUsed());
        registration.setCouponDiscountPrice(pricingResult.getCouponDiscountAmount());
        registration.setPointsUsed(pricingResult.getPointsUsed());
        registration.setPointsDiscountPrice(pricingResult.getPointsDiscountAmount());

        // 更新数据库
        registrationMapper.updateById(registration);
        createBO.setPricingResult(pricingResult);

        log.info("[applyDiscountsAndFinalizePricing] 应用优惠后价格: ActualPayPrice={}, CouponDiscount={}, PointsDiscount={}",
                registration.getActualPayPrice(), registration.getCouponDiscountPrice(),
                registration.getPointsDiscountPrice());
    }

    /**
     * 处理支付逻辑
     */
    private AppRegistrationRespVO handlePaymentProcess(RegistrationCreateBO createBO) {
        // 所有订单都走正常支付流程，包括0元订单
        // 创建支付订单，让用户跳转到收银台
        Long payOrderId = createPayOrder(createBO.getUserId(), createBO.getRegistration(), createBO.getActivity());

        log.info("[handlePaymentProcess] 支付订单创建成功: registrationId={}, payOrderId={}, finalPayPrice={}",
                createBO.getRegistration().getId(), payOrderId, createBO.getRegistration().getActualPayPrice());

        // 更新报名记录的支付订单ID
        createBO.getRegistration().setPayOrderId(payOrderId);
        registrationMapper.updateById(createBO.getRegistration());

        return AppRegistrationRespVO.builder()
                .id(createBO.getRegistration().getId())
                .payOrderId(payOrderId)
                .paymentStatus(createBO.getRegistration().getPaymentStatus())
                .build();
    }

    /**
     * 处理支付成功的回调
     *
     * @param notifyReqDTO 支付回调数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void handlePaySuccess(PayOrderNotifyReqDTO notifyReqDTO) {
        log.info("[handlePaySuccess][处理支付成功回调] {}", notifyReqDTO);

        // 1. 根据商户订单号，查询报名记录
        RegistrationDO registration = registrationMapper.selectByMerchantOrderNo(notifyReqDTO.getMerchantOrderId());
        if (registration == null) {
            log.error("[handlePaySuccess][报名记录不存在] 商户订单号 {}", notifyReqDTO.getMerchantOrderId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
        }

        // 2. 使用状态模式处理支付成功
        RegistrationState state = stateFactory.getState(registration.getStatus());
        state.handlePaySuccess(registration, notifyReqDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void cancelRegistration(Long id, Long userId, String cancelReason) {
        log.info("[cancelRegistration] 开始处理用户 {} 取消报名 {}, 原因: {}", userId, id, cancelReason);
        RegistrationDO registration = validateRegistrationExists(id);

        // 确保只能取消自己的报名
        if (!Objects.equals(registration.getUserId(), userId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_CANCEL, "只能取消自己的报名");
        }

        // 处理好友组关联清理（在状态变更之前）
        handleFriendGroupOnCancel(registration, userId);

        // 获取当前状态，交由状态处理器处理，并传递取消原因
        RegistrationState state = stateFactory.getState(registration.getStatus());
        state.cancel(registration, userId, cancelReason);

        // 注意：候补晋升和重新分队逻辑已移至 TeamRebalanceListener 中统一处理
        // 这里仅处理候补队列重排（仅针对候补报名取消）
        if (Boolean.TRUE.equals(registration.getIsWaitlist())) {
            // 如果取消的是候补报名，需要重排候补队列
            waitlistService.reorderWaitlist(registration.getActivityId(), registration.getWaitlistPosition());
        }

        log.info("[cancelRegistration] 用户 {} 取消报名 {} 处理完成", userId, id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public void adminCancelRegistration(Long registrationId, Long operatorId, String cancelReason) {
        log.info("[adminCancelRegistration] 管理员 {} 开始取消报名 {}, 原因: {}", operatorId, registrationId, cancelReason);
        RegistrationDO registration = validateRegistrationExists(registrationId);

        // 管理员取消不需要校验用户权限，直接处理

        // 处理好友组关联清理（在状态变更之前）
        handleFriendGroupOnCancel(registration, operatorId);

        // 获取当前状态，交由状态处理器处理，并传递取消原因
        RegistrationState state = stateFactory.getState(registration.getStatus());
        state.cancel(registration, operatorId, cancelReason);

        // 注意：候补晋升和重新分队逻辑已移至 TeamRebalanceListener 中统一处理
        // 这里仅处理候补队列重排（仅针对候补报名取消）
        if (Boolean.TRUE.equals(registration.getIsWaitlist())) {
            // 如果取消的是候补报名，需要重排候补队列
            waitlistService.reorderWaitlist(registration.getActivityId(), registration.getWaitlistPosition());
        }

        log.info("[adminCancelRegistration] 管理员 {} 取消报名 {} 处理完成", operatorId, registrationId);
    }

    @Override
    public AppRegistrationDetailRespVO getRegistrationDetail(Long id, Long userId) {
        // 查询报名记录
        RegistrationDO registration = validateRegistrationExists(id);

        // 权限校验：只能查看自己的报名
        if (!Objects.equals(registration.getUserId(), userId)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_PERMITTED);
        }

        // 获取活动信息
        ActivityDO activity = activityService.getActivity(registration.getActivityId());
        if (activity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 获取好友组信息（如果有）
        FriendGroupDO friendGroup = null;
        if (registration.getFriendGroupId() != null) {
            friendGroup = friendGroupService.getFriendGroup(registration.getFriendGroupId());
        }

        // 转换为详情VO
        return RegistrationConvert.INSTANCE.convertDetail(registration, activity, friendGroup);
    }

    // --- 私有方法 ---
    private void validateDuplicateRegistration(Long userId, Long activityId) {
        // 直接查询该用户在该活动下的非取消状态报名记录
        List<RegistrationDO> activeRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getUserId, userId)
                        .eq(RegistrationDO::getActivityId, activityId)
                        .ne(RegistrationDO::getStatus, RegistrationStatusEnum.CANCELLED.getStatus())
                        .orderByDesc(RegistrationDO::getCreateTime)
        );

        // 如果存在非取消状态的记录，不允许重复报名
        if (!activeRegistrations.isEmpty()) {
            RegistrationDO activeRegistration = activeRegistrations.get(0); // 最新的活跃记录
            String statusDesc = RegistrationStatusEnum.getNameByStatus(activeRegistration.getStatus());
            log.warn("[validateDuplicateRegistration][userId({}) 活动编号({})] 用户已有{}状态的报名记录，不允许重复报名, 记录ID: {}",
                    userId, activityId, statusDesc, activeRegistration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_ALREADY_EXISTS,
                    String.format("您已报名该活动（状态：%s），无法重复报名", statusDesc));
        }

        // 没有活跃的报名记录，允许报名（可能是第一次报名，或者之前的记录都已取消）
        log.info("[validateDuplicateRegistration][userId({}) 活动编号({})] 用户没有活跃的报名记录，允许报名",
                userId, activityId);
    }

    private Long createPayOrder(Long userId, RegistrationDO registration, ActivityDO activity) {
        // 获取用户 IP
        String userIp = ServletUtils.getClientIP();
        // 在非HTTP请求环境中，userIp可能为空，使用默认值
        if (userIp == null || userIp.trim().isEmpty()) {
            userIp = "127.0.0.1"; // 默认本地IP
        }

        // 组装支付订单创建请求 DTO
        PayOrderCreateReqDTO createReq = new PayOrderCreateReqDTO();
        createReq.setAppKey(payAppKey); // 使用从配置注入的 App ID，并转换为 String
        createReq.setUserIp(userIp);
        // 使用报名记录 ID 作为支付系统的商家订单号，方便关联
        createReq.setMerchantOrderId(String.valueOf(registration.getId()));
        createReq.setSubject(String.format("%s-报名", activity.getName())); // 简化标题
        createReq.setBody(String.format("活动报名: %s.用户：%s", activity.getName(), registration.getUserId())); // 增加 body 描述
        // 使用优惠后的实际支付价格，而不是原始价格
        Integer payPrice = registration.getActualPayPrice() != null ? registration.getActualPayPrice()
                : registration.getShouldPayPrice();
        createReq.setPrice(payPrice);
        createReq.setExpireTime(LocalDateTime.now().plusMinutes(30)); // 支付超时时间，例如30分钟

        // 调用支付接口创建订单
        try {
            Long payOrderId = payOrderApi.createOrder(createReq);
            if (payOrderId == null || payOrderId <= 0) {
                log.error("[createPayOrder][userId({}) registrationId({})] 调用支付接口创建订单失败, 返回 payOrderId 无效: {}", userId,
                        registration.getId(), payOrderId);
                throw exception(ErrorCodeConstants.ORDER_CREATE_FAIL);
            }
            log.info("[createPayOrder][userId({}) registrationId({})] 支付订单创建成功, payOrderId: {}, payPrice: {}",
                    userId, registration.getId(), payOrderId, payPrice);
            return payOrderId;
        } catch (Exception e) {
            log.error("[createPayOrder][创建支付订单失败] registrationId: {}, userId: {}", registration.getId(), userId, e);
            // 重新抛出异常，以便事务回滚
            if (e instanceof cn.iocoder.yudao.framework.common.exception.ServiceException) {
                throw e;
            }
            throw exception(ErrorCodeConstants.ORDER_CREATE_FAIL, e.getMessage());
        }
    }

    private RegistrationDO validateRegistrationExists(Long id) {
        RegistrationDO registration = registrationMapper.selectById(id);
        if (registration == null) {
            log.error("[validateRegistrationExists][报名记录不存在] 报名ID: {}", id);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
        }
        return registration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public AppRegistrationRespVO createGroupRegistration(Long userId,
                                                         AppRegistrationCreateReqVO createReqVO) {
        log.info("[createGroupRegistration][用户:{} 创建活动:{} 的好友组队报名]", userId, createReqVO.getActivityId());

        // 1. 创建普通报名
        AppRegistrationRespVO respVO = createRegistration(userId, createReqVO);
        if (respVO == null || respVO.getId() == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CREATE_FAILED);
        }

        // 2. 创建好友组队 (仅当报名创建成功后)
        try {
            FriendGroupDO friendGroup = friendGroupService.createFriendGroup(
                    createReqVO.getActivityId(), userId, respVO.getId(), activityService.getActivity(createReqVO.getActivityId()));

            // 3. 设置好友组ID到响应
            respVO.setFriendGroupId(friendGroup.getId());

            log.info("[createGroupRegistration][用户:{} 创建活动:{} 的好友组队:{} 成功]",
                    userId, createReqVO.getActivityId(), friendGroup.getId());
        } catch (Exception e) {
            log.error("[createGroupRegistration][用户:{} 报名后创建好友组失败]", userId, e);
            // 注意：即使创建好友组失败，报名和支付流程仍然有效，所以不抛出异常中断事务
        }

        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public AppRegistrationRespVO joinGroupRegistration(Long userId,
                                                       AppRegistrationCreateReqVO createReqVO, String invitationCode) {
        log.info("[joinGroupRegistration][用户:{}  通过邀请码:{} 加入活动:{} 的好友组队]",
                userId, invitationCode, createReqVO.getActivityId());

        // 1. 验证邀请码并获取好友组信息（直接从数据库查询）
        FriendGroupDO friendGroup = friendGroupMapper.selectOne(
                new LambdaQueryWrapper<FriendGroupDO>()
                        .eq(FriendGroupDO::getInviteCode, invitationCode)
                        .eq(FriendGroupDO::getDeleted, false)
        );

        if (friendGroup == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FRIEND_GROUP_INVALID_INVITATION);
        }

        // 2. 验证活动ID匹配
        if (!friendGroup.getActivityId().equals(createReqVO.getActivityId())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FRIEND_GROUP_ACTIVITY_MISMATCH);
        }

        // 3. 验证好友组状态
        if (!FriendGroupStatusEnum.IN_PROGRESS.getStatus().equals(friendGroup.getStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FRIEND_GROUP_NOT_AVAILABLE);
        }

        // 4. 检查是否已经在该好友组中
        if (getUserFriendGroupId(userId, createReqVO.getActivityId()) != null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FRIEND_GROUP_ALREADY_JOINED);
        }

        // 5. 检查好友组人数是否已满
        int currentMembers = getCurrentMemberCount(friendGroup.getId());
        if (currentMembers >= MAX_FRIENDS_IN_GROUP) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.FRIEND_GROUP_PLAYER_LIMIT_EXCEEDED);
        }

        // 6. 创建普通报名
        AppRegistrationRespVO respVO = createRegistration(userId, createReqVO);
        if (respVO == null || respVO.getId() == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CREATE_FAILED);
        }

        // 7. 更新报名记录，设置好友组ID
        updateRegistrationFriendGroup(respVO.getId(), friendGroup.getId());

        // 8. 设置好友组ID到响应
        respVO.setFriendGroupId(friendGroup.getId());

        log.info("[joinGroupRegistration][用户:{} 加入活动:{} 的好友组:{} 成功]",
                userId, createReqVO.getActivityId(), friendGroup.getId());

        return respVO;
    }

    @Override
    public Long getUserFriendGroupId(Long userId, Long activityId) {
        // 🔧 优化：使用更完整的状态检查
        RegistrationDO registration = registrationMapper.selectOne(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getUserId, userId)
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .select(RegistrationDO::getFriendGroupId));

        return registration != null ? registration.getFriendGroupId() : null;
    }

    /**
     * 更新报名记录的好友组ID
     */
    private void updateRegistrationFriendGroup(Long registrationId, Long friendGroupId) {
        RegistrationDO updateReg = new RegistrationDO();
        updateReg.setId(registrationId);
        updateReg.setFriendGroupId(friendGroupId);
        updateReg.setIsGroupLeader(false);
        registrationMapper.updateById(updateReg);
    }

    /**
     * 获取好友组当前已支付成员数量
     * 只计算支付状态为已支付的成员，确保用户支付成功后才算真正加入房间
     */
    private int getCurrentMemberCount(Long friendGroupId) {
        return registrationMapper.selectCount(
                        new LambdaQueryWrapper<RegistrationDO>()
                                .eq(RegistrationDO::getFriendGroupId, friendGroupId)
                                .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())) // 只计算已支付的成员
                .intValue();
    }

    /**
     * 处理用户退赛时的好友组关联清理
     */
    private void handleFriendGroupOnCancel(RegistrationDO registration, Long userId) {
        if (registration.getFriendGroupId() == null) {
            log.debug("[handleFriendGroupOnCancel] 用户 {} 报名 {} 未关联好友组，无需处理", userId, registration.getId());
            return;
        }

        Long friendGroupId = registration.getFriendGroupId();
        log.info("[handleFriendGroupOnCancel] 处理用户 {} 退赛时的好友组 {} 关联清理", userId, friendGroupId);

        try {
            // 检查用户是否为好友组队长
            if (Boolean.TRUE.equals(registration.getIsGroupLeader())) {
                log.info("[handleFriendGroupOnCancel] 用户 {} 为好友组 {} 队长，解散整个好友组", userId, friendGroupId);
                // 队长退赛，解散整个好友组
                friendGroupService.disbandFriendGroup(userId, friendGroupId);
            } else {
                log.info("[handleFriendGroupOnCancel] 用户 {} 为好友组 {} 普通成员，退出好友组", userId, friendGroupId);
                // 普通成员退赛，只退出好友组
                friendGroupService.quitFriendGroup(userId, friendGroupId);
            }
        } catch (Exception e) {
            log.error("[handleFriendGroupOnCancel] 处理用户 {} 退赛时的好友组 {} 关联清理失败", userId, friendGroupId, e);
            // 不阻塞退赛流程，记录日志继续执行
        }
    }

    @Override
    public ActivityRegistrationDetailRespVO getActivityRegistrationDetail(Long activityId) {
        log.info("[getActivityRegistrationDetail] 开始获取活动 {} 的报名详情", activityId);

        // 1. 获取活动信息
        ActivityDO activity = activityService.getActivity(activityId);
        if (activity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        // 2. 转换活动信息
        ActivityRespVO activityVO = ActivityConvert.INSTANCE.convert(activity);

        // 3. 获取所有报名记录
        List<RegistrationDO> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .orderByDesc(RegistrationDO::getRegistrationTime));

        if (CollUtil.isEmpty(registrations)) {
            // 没有报名记录时返回空数据
            ActivityRegistrationDetailRespVO respVO = new ActivityRegistrationDetailRespVO();
            respVO.setActivity(activityVO);
            respVO.setStatistics(createEmptyStatistics(activity));
            respVO.setRegistrations(Collections.emptyList());
            return respVO;
        }

        // 4. 获取相关的用户信息和球员信息
        Set<Long> userIds = registrations.stream().map(RegistrationDO::getUserId).collect(Collectors.toSet());
        Set<Long> playerIds = registrations.stream().map(RegistrationDO::getPlayerId).collect(Collectors.toSet());
        Set<Long> teamIds = registrations.stream()
                .map(RegistrationDO::getTeamId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> teamAssignedIds = registrations.stream()
                .map(RegistrationDO::getTeamAssigned)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        teamIds.addAll(teamAssignedIds);

        // 5. 批量查询相关数据
        Map<Long, MemberUserRespDTO> userMap = getUserInfoMap(userIds);
        Map<Long, PlayerDO> playerMap = getPlayerInfoMap(playerIds);
        Map<Long, String> teamNameMap = getTeamNameMap(teamIds);

        // 6. 转换报名记录
        List<ActivityRegistrationDetailRespVO.RegistrationItemVO> registrationVOs =
                convertRegistrationItems(registrations, userMap, playerMap, teamNameMap);

        // 7. 计算统计信息
        ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics =
                calculateStatistics(activity, registrations);

        // 8. 组装返回结果
        ActivityRegistrationDetailRespVO respVO = new ActivityRegistrationDetailRespVO();
        respVO.setActivity(activityVO);
        respVO.setStatistics(statistics);
        respVO.setRegistrations(registrationVOs);

        log.info("[getActivityRegistrationDetail] 完成活动 {} 的报名详情获取，共 {} 条报名记录",
                activityId, registrations.size());

        return respVO;
    }

    /**
     * 创建空的统计信息
     */
    private ActivityRegistrationDetailRespVO.RegistrationStatisticsVO createEmptyStatistics(ActivityDO activity) {
        ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics =
                new ActivityRegistrationDetailRespVO.RegistrationStatisticsVO();

        statistics.setTotalRegistrations(0);
        statistics.setPaidRegistrations(0);
        statistics.setPendingRegistrations(0);
        statistics.setCancelledRegistrations(0);
        statistics.setWaitlistRegistrations(0);

        // 设置活动相关的配置信息
        if (ActivityTypeEnum.isRanking(activity.getType())) {
            statistics.setMinRequiredPlayers(activity.getMinPlayersPerGame());
            statistics.setMaxAllowedPlayers(activity.getMaxPlayersPerGame());
        }

        statistics.setIsGroupingSuccessful(isGroupingSuccessful(activity.getStatus()));

        return statistics;
    }

    /**
     * 判断是否组局成功
     */
    private Boolean isGroupingSuccessful(Integer status) {
        if (status == null) {
            return false;
        }
        return ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus().equals(status) ||
                ActivityStatusEnum.IN_PROGRESS.getStatus().equals(status) ||
                ActivityStatusEnum.COMPLETED.getStatus().equals(status);
    }

    /**
     * 获取用户信息映射
     */
    private Map<Long, MemberUserRespDTO> getUserInfoMap(Set<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        try {
            List<MemberUserRespDTO> users = memberUserApi.getUserList(userIds);
            return users.stream().collect(Collectors.toMap(
                    MemberUserRespDTO::getId,
                    user -> user,
                    (existing, replacement) -> existing));
        } catch (Exception e) {
            log.warn("[getUserInfoMap] 获取用户信息失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取球员信息映射
     */
    private Map<Long, PlayerDO> getPlayerInfoMap(Set<Long> playerIds) {
        if (CollUtil.isEmpty(playerIds)) {
            return Collections.emptyMap();
        }

        try {
            List<PlayerDO> players = playerService.getPlayersByIds(new ArrayList<>(playerIds));
            return players.stream().collect(Collectors.toMap(
                    PlayerDO::getId,
                    player -> player,
                    (existing, replacement) -> existing));
        } catch (Exception e) {
            log.warn("[getPlayerInfoMap] 获取球员信息失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取球队名称映射
     */
    private Map<Long, String> getTeamNameMap(Set<Long> teamIds) {
        if (CollUtil.isEmpty(teamIds)) {
            return Collections.emptyMap();
        }

        try {
            Map<Long, String> teamNameMap = new HashMap<>();
            for (Long teamId : teamIds) {
                try {
                    TeamInfoRespVO teamInfo = activityService.getTeamInfo(teamId);
                    teamNameMap.put(teamId, teamInfo != null ? teamInfo.getName() : "未知球队");
                } catch (Exception e) {
                    log.warn("[getTeamNameMap] 获取球队 {} 信息失败", teamId, e);
                    teamNameMap.put(teamId, "未知球队");
                }
            }
            return teamNameMap;
        } catch (Exception e) {
            log.warn("[getTeamNameMap] 获取球队信息映射失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 转换报名记录
     */
    private List<ActivityRegistrationDetailRespVO.RegistrationItemVO> convertRegistrationItems(
            List<RegistrationDO> registrations,
            Map<Long, MemberUserRespDTO> userMap,
            Map<Long, PlayerDO> playerMap,
            Map<Long, String> teamNameMap) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return registrations.stream().map(registration -> {
            ActivityRegistrationDetailRespVO.RegistrationItemVO vo =
                    new ActivityRegistrationDetailRespVO.RegistrationItemVO();

            // 基本报名信息
            vo.setId(registration.getId());
            vo.setUserId(registration.getUserId());
            vo.setPlayerId(registration.getPlayerId());
            vo.setStatus(registration.getStatus());
            vo.setStatusText(RegistrationStatusEnum.getNameByStatus(registration.getStatus()));
            vo.setPaymentStatus(registration.getPaymentStatus());
            vo.setPaymentStatusText(PayStatusEnum.PAID.getStatus().equals(registration.getPaymentStatus())
                    ? "已支付" : "未支付");
            vo.setRegistrationTime(registration.getRegistrationTime() != null
                    ? registration.getRegistrationTime().format(formatter) : null);
            vo.setPayTime(registration.getPayTime() != null
                    ? registration.getPayTime().format(formatter) : null);
            vo.setShouldPayPrice(registration.getShouldPayPrice());
            vo.setActualPayPrice(registration.getActualPayPrice());
            vo.setRefundPrice(registration.getTotalRefundPrice());
            vo.setCancelReason(registration.getCancelReason());

            // 用户信息
            MemberUserRespDTO user = userMap.get(registration.getUserId());
            if (user != null) {
                vo.setUserNickname(user.getNickname());
                vo.setUserAvatar(user.getAvatar());
            } else {
                vo.setUserNickname("用户" + registration.getUserId());
            }

            // 球员信息
            PlayerDO player = playerMap.get(registration.getPlayerId());
            if (player != null) {
                vo.setPlayerName(player.getName());
                vo.setPlayerNumber(player.getNumber() != null ? player.getNumber().toString() : null);
                vo.setPlayerPosition(player.getPosition() != null ? player.getPosition().toString() : null);
                vo.setPlayerHeight(player.getHeight() != null ? player.getHeight().intValue() : null);
                vo.setPlayerRating(player.getRatings());
            } else {
                vo.setPlayerName("球员" + registration.getPlayerId());
            }

            // 排位赛特有字段
            if (registration.getTeamAssigned() != null) {
                vo.setTeamAssigned(registration.getTeamAssigned());
                vo.setTeamAssignedName(teamNameMap.getOrDefault(registration.getTeamAssigned(), "未知球队"));
            }
            vo.setJerseyColor(registration.getJerseyColor());
            vo.setFriendGroupId(registration.getFriendGroupId());
            vo.setIsGroupLeader(registration.getIsGroupLeader());
            vo.setIsWaitlist(registration.getIsWaitlist());
            vo.setWaitlistPosition(registration.getWaitlistPosition());

            // 友谊赛和联赛特有字段
            if (registration.getTeamId() != null) {
                vo.setTeamId(registration.getTeamId());
                vo.setTeamName(teamNameMap.getOrDefault(registration.getTeamId(), "未知球队"));
            }
            if (registration.getPaymentType() != null) {
                vo.setPaymentType(registration.getPaymentType());
                vo.setPaymentTypeText(getPaymentTypeText(registration.getPaymentType()));
            }

            // 优惠信息
            vo.setCouponId(registration.getCouponId());
            vo.setCouponDiscountPrice(registration.getCouponDiscountPrice());
            vo.setPointsUsed(registration.getPointsUsed());
            vo.setPointsDiscountPrice(registration.getPointsDiscountPrice());

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取支付方式文本
     */
    private String getPaymentTypeText(Integer paymentType) {
        if (paymentType == null) {
            return null;
        }

        if (FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(paymentType)) {
            return "球队整体支付";
        } else if (FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(paymentType)) {
            return "球员个人支付AA";
        }

        return "未知支付方式";
    }

    /**
     * 计算统计信息
     */
    private ActivityRegistrationDetailRespVO.RegistrationStatisticsVO calculateStatistics(
            ActivityDO activity, List<RegistrationDO> registrations) {

        ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics =
                new ActivityRegistrationDetailRespVO.RegistrationStatisticsVO();

        // 基本统计
        statistics.setTotalRegistrations(registrations.size());
        statistics.setPaidRegistrations((int) registrations.stream()
                .filter(r -> PayStatusEnum.PAID.getStatus().equals(r.getPaymentStatus()))
                .count());
        statistics.setPendingRegistrations((int) registrations.stream()
                .filter(r -> RegistrationStatusEnum.PENDING_PAYMENT.getStatus().equals(r.getStatus()))
                .count());
        statistics.setCancelledRegistrations((int) registrations.stream()
                .filter(r -> RegistrationStatusEnum.CANCELLED.getStatus().equals(r.getStatus()))
                .count());
        statistics.setWaitlistRegistrations((int) registrations.stream()
                .filter(r -> Boolean.TRUE.equals(r.getIsWaitlist()))
                .count());

        // 根据活动类型计算特定统计
        if (ActivityTypeEnum.isRanking(activity.getType())) {
            calculateRankingStatistics(statistics, activity, registrations);
        } else if (ActivityTypeEnum.isFriendly(activity.getType())) {
            calculateFriendlyStatistics(statistics, activity, registrations);
        } else if (ActivityTypeEnum.isLeague(activity.getType())) {
            calculateLeagueStatistics(statistics, activity, registrations);
        }

        statistics.setIsGroupingSuccessful(isGroupingSuccessful(activity.getStatus()));

        return statistics;
    }

    /**
     * 计算排位赛统计信息
     */
    private void calculateRankingStatistics(
            ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics,
            ActivityDO activity,
            List<RegistrationDO> registrations) {

        statistics.setMinRequiredPlayers(activity.getMinPlayersPerGame());
        statistics.setMaxAllowedPlayers(activity.getMaxPlayersPerGame());

        // 计算主客队人数（只统计已支付的）
        List<RegistrationDO> paidRegistrations = registrations.stream()
                .filter(r -> PayStatusEnum.PAID.getStatus().equals(r.getPaymentStatus()))
                .collect(Collectors.toList());

        if (activity.getHomeTeamId() != null && activity.getGuestTeamId() != null) {
            statistics.setHomeTeamCount((int) paidRegistrations.stream()
                    .filter(r -> activity.getHomeTeamId().equals(r.getTeamAssigned()))
                    .count());
            statistics.setGuestTeamCount((int) paidRegistrations.stream()
                    .filter(r -> activity.getGuestTeamId().equals(r.getTeamAssigned()))
                    .count());
        }

        // 计算好友组数量
        statistics.setFriendGroupCount((int) paidRegistrations.stream()
                .map(RegistrationDO::getFriendGroupId)
                .filter(Objects::nonNull)
                .distinct()
                .count());
    }

    /**
     * 计算友谊赛统计信息
     */
    private void calculateFriendlyStatistics(
            ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics,
            ActivityDO activity,
            List<RegistrationDO> registrations) {

        // 计算参赛球队数
        Set<Long> teamIds = registrations.stream()
                .filter(r -> PayStatusEnum.PAID.getStatus().equals(r.getPaymentStatus()))
                .map(RegistrationDO::getTeamId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        statistics.setTeamCount(teamIds.size());

        // 计算支付方式统计
        Map<Long, List<RegistrationDO>> teamRegistrations = registrations.stream()
                .filter(r -> r.getTeamId() != null)
                .collect(Collectors.groupingBy(RegistrationDO::getTeamId));

        int aaPaymentTeams = 0;
        int totalPaymentTeams = 0;

        for (List<RegistrationDO> teamRegs : teamRegistrations.values()) {
            if (teamRegs.isEmpty()) continue;

            Integer paymentType = teamRegs.get(0).getPaymentType();
            if (FriendlyPaymentTypeEnum.TEAM_AA.getType().equals(paymentType)) {
                aaPaymentTeams++;
            } else if (FriendlyPaymentTypeEnum.TEAM_TOTAL.getType().equals(paymentType)) {
                totalPaymentTeams++;
            }
        }

        statistics.setAaPaymentTeamCount(aaPaymentTeams);
        statistics.setTotalPaymentTeamCount(totalPaymentTeams);
    }

    /**
     * 计算联赛统计信息
     */
    private void calculateLeagueStatistics(
            ActivityRegistrationDetailRespVO.RegistrationStatisticsVO statistics,
            ActivityDO activity,
            List<RegistrationDO> registrations) {

        // 计算参赛联盟数（球队数）
        Set<Long> leagueTeamIds = registrations.stream()
                .filter(r -> PayStatusEnum.PAID.getStatus().equals(r.getPaymentStatus()))
                .map(RegistrationDO::getTeamId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        statistics.setLeagueTeamCount(leagueTeamIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.HOME_RECOMMENDED_ACTIVITIES, allEntries = true)
    public int cancelTimeoutRegistrations() {
        log.info("[cancelTimeoutRegistrations] 开始处理超时未支付的报名记录");

        // 1. 查询所有待支付状态的报名记录
        List<RegistrationDO> pendingRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PENDING_PAYMENT.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.UNPAID.getStatus())
                        .isNotNull(RegistrationDO::getPayOrderId)
        );

        if (CollUtil.isEmpty(pendingRegistrations)) {
            log.info("[cancelTimeoutRegistrations] 没有找到待支付的报名记录");
            return 0;
        }

        log.info("[cancelTimeoutRegistrations] 找到 {} 条待支付的报名记录", pendingRegistrations.size());

        int cancelledCount = 0;

        // 2. 遍历检查每个报名记录的支付订单状态
        for (RegistrationDO registration : pendingRegistrations) {
            try {
                // 通过支付API查询支付订单状态
                if (registration.getPayOrderId() != null) {
                    // 检查支付订单状态
                    PaymentOrderCheckResult checkResult = checkPaymentOrderStatus(registration.getPayOrderId());
                    if (checkResult.shouldCancel()) {
                        updateRegistrationToTimeout(registration, checkResult.getCancelReason());
                        cancelledCount++;
                        log.info("[cancelTimeoutRegistrations] 成功取消报名记录: registrationId={}, payOrderId={}, reason={}",
                                registration.getId(), registration.getPayOrderId(), checkResult.getCancelReason());
                    }
                } else {
                    // 支付订单ID为空，说明支付流程有问题，也应该取消
                    updateRegistrationToTimeout(registration, "支付订单不存在");
                    cancelledCount++;
                    log.info("[cancelTimeoutRegistrations] 取消无支付订单的报名记录: registrationId={}", registration.getId());
                }
            } catch (Exception e) {
                log.error("[cancelTimeoutRegistrations] 处理报名记录失败: registrationId={}, error={}",
                        registration.getId(), e.getMessage(), e);
            }
        }

        log.info("[cancelTimeoutRegistrations] 完成超时报名记录处理，成功处理数量: {}", cancelledCount);
        return cancelledCount;
    }

    /**
     * 支付订单检查结果
     */
    private static class PaymentOrderCheckResult {
        private final boolean shouldCancel;
        @Getter
        private final String cancelReason;

        public PaymentOrderCheckResult(boolean shouldCancel, String cancelReason) {
            this.shouldCancel = shouldCancel;
            this.cancelReason = cancelReason;
        }

        public boolean shouldCancel() {
            return shouldCancel;
        }

    }

    /**
     * 检查支付订单状态
     */
    private PaymentOrderCheckResult checkPaymentOrderStatus(Long payOrderId) {
        try {
            // 调用支付API获取订单状态
            PayOrderRespDTO payOrder = payOrderApi.getOrder(payOrderId);
            if (payOrder == null) {
                log.warn("[checkPaymentOrderStatus] 支付订单不存在: payOrderId={}", payOrderId);
                return new PaymentOrderCheckResult(true, "支付订单不存在");
            }

            // 检查订单状态是否为已关闭
            boolean isClosed = PayOrderStatusEnum.isClosed(payOrder.getStatus());
            log.debug("[checkPaymentOrderStatus] 支付订单状态检查: payOrderId={}, status={}, isClosed={}",
                    payOrderId, payOrder.getStatus(), isClosed);

            if (isClosed) {
                return new PaymentOrderCheckResult(true, "支付超时自动取消");
            } else {
                return new PaymentOrderCheckResult(false, null);
            }
        } catch (Exception e) {
            log.error("[checkPaymentOrderStatus] 查询支付订单状态失败: payOrderId={}, error={}", payOrderId, e.getMessage(), e);
            // 查询失败的情况下，不取消报名，避免误操作
            return new PaymentOrderCheckResult(false, null);
        }
    }

    /**
     * 🔧 增强版：将报名记录更新为超时取消状态，并处理候补提升
     */
    private void updateRegistrationToTimeout(RegistrationDO registration, String cancelReason) {
        // 1. 更新报名记录状态
        RegistrationDO updateObj = new RegistrationDO();
        updateObj.setId(registration.getId());
        updateObj.setStatus(RegistrationStatusEnum.CANCELLED.getStatus());
        updateObj.setPaymentStatus(PayStatusEnum.CLOSED.getStatus()); // 🔧 修复：使用CLOSED状态保持与支付系统一致
        updateObj.setCancelReason(cancelReason);
        updateObj.setUpdateTime(LocalDateTime.now());

        int updateCount = registrationMapper.updateById(updateObj);
        if (updateCount == 0) {
            log.error("[updateRegistrationToTimeout] 更新报名记录失败: registrationId={}", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_STATUS_ERROR, "更新报名状态失败");
        }

        // 2. 如果是候补球员，需要从候补队列中移除并重排序
        if (Boolean.TRUE.equals(registration.getIsWaitlist())) {
            log.info("[updateRegistrationToTimeout] 候补球员支付超时，从候补队列中移除: registrationId={}, waitlistPosition={}",
                    registration.getId(), registration.getWaitlistPosition());

            // 重排候补队列（从该球员的位置开始）
            if (registration.getWaitlistPosition() != null) {
                waitlistService.reorderWaitlist(registration.getActivityId(), registration.getWaitlistPosition());
            }
        } else {
            // 3. 如果是正式报名球员，检查是否有候补球员可以提升
            try {
                ActivityDO activity = activityMapper.selectById(registration.getActivityId());
                if (activity != null) {
                    List<RegistrationDO> promotedRegistrations = waitlistService.promoteFromWaitlist(activity);
                    if (!promotedRegistrations.isEmpty()) {
                        log.info("[updateRegistrationToTimeout] 正式报名球员支付超时，提升了 {} 个候补球员", promotedRegistrations.size());
                    }
                }
            } catch (Exception e) {
                log.error("[updateRegistrationToTimeout] 处理候补提升失败: registrationId={}", registration.getId(), e);
                // 不抛出异常，避免影响主流程
            }
        }

        log.info("[updateRegistrationToTimeout] 成功处理超时报名: registrationId={}, isWaitlist={}, reason={}",
                registration.getId(), registration.getIsWaitlist(), cancelReason);

        // 记录操作日志
        logRegistrationOperation(registration.getId(),
                RegistrationStatusEnum.PENDING_PAYMENT.getStatus(),
                RegistrationStatusEnum.CANCELLED.getStatus(),
                cancelReason);
    }

    /**
     * 记录报名操作日志
     */
    private void logRegistrationOperation(Long registrationId, Integer beforeStatus, Integer afterStatus, String content) {
        try {
            // 这里可以添加具体的日志记录逻辑
            // 例如插入到 sd_registration_log 表
            log.info("[logRegistrationOperation] 报名操作日志: registrationId={}, beforeStatus={}, afterStatus={}, content={}",
                    registrationId, beforeStatus, afterStatus, content);
        } catch (Exception e) {
            log.error("[logRegistrationOperation] 记录操作日志失败: registrationId={}, error={}", registrationId, e.getMessage(), e);
        }
    }

}