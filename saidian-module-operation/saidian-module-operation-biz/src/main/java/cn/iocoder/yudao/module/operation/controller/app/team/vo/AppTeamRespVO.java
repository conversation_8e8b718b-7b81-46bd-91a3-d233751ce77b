package cn.iocoder.yudao.module.operation.controller.app.team.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.module.operation.controller.app.club.vo.AppClubRespVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerInfoRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Schema(description = "APP - 球队信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppTeamRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27014")
    private Long id;

    @Schema(description = "所属俱乐部", example = "27014")
    private AppClubRespVO club;

    @Schema(description = "球队名称", example = "赵六")
    private String name;

    @Schema(description = "球队简介", example = "你猜")
    private String description;

    @Schema(description = "球队简称", example = "芋艿")
    @ExcelProperty("球队简称")
    private String shortName;

    @Schema(description = "主队颜色", example = "red")
    private String homeColor;

    @Schema(description = "客队颜色", example = "green")
    private String guestColor;

    @Schema(description = "成立时间")
    private LocalDate founded;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "球队logo")
    private String logo;

    @Schema(description = "队长")
    private AppPlayerInfoRespVO captain;

    @Schema(description = "主教练")
    private AppPlayerInfoRespVO coach;

    @Schema(description = "球员列表")
    private List<AppPlayerInfoRespVO> players;

    @Schema(description = "球队类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @DictFormat("sd_team_type")
    private Integer type;

    @Schema(description = "球队高阶指标", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private List<AppTeamMetricsRespVO> metrics;

}