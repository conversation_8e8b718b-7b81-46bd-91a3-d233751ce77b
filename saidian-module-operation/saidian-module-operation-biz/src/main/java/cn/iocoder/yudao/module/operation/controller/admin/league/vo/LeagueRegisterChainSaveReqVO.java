package cn.iocoder.yudao.module.operation.controller.admin.league.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 联赛接龙信息新增/修改 Request VO")
@Data
public class LeagueRegisterChainSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16980")
    private Long id;

    @Schema(description = "联赛ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24146")
    @NotNull(message = "联赛ID不能为空")
    private Long leagueId;

    @Schema(description = "发起用户ID", example = "23832")
    private Long userId;

    @Schema(description = "发起球员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5242")
    @NotNull(message = "发起球员id不能为空")
    private Long playerId;

    @Schema(description = "接龙球员数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "接龙球员数不能为空")
    private Integer chainCounts;

    @Schema(description = "报名球队ID", example = "2373")
    private Long teamId;

    @Schema(description = "活动价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "28438")
    @NotNull(message = "活动价格不能为空")
    private Integer price;

    @Schema(description = "支付方式")
    private Integer payMode;

    @Schema(description = "AA人数", example = "3004")
    private Integer aaPlayerCount;

    @Schema(description = "报名状态", example = "2")
    private Integer status;

}