package cn.iocoder.yudao.module.operation.job.league;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.operation.service.league.LeagueService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>
 * 1.报名中->待开始:当联赛到了报名截止时间,且满足报名条件的球队大于最小球队数，则将状态改为待开始，
 * 操作1:将成功报名的球队插入联赛报名成功球队表中
 * 操作2:没报名成功的球队的报名,退款状态全部改成全部退款
 * 操作3:报名成功的球队的报名,判断球队报名支付的总金额是否大于费用，大于费用，则修改报名为部分退款状态
 * 操作4:如果联赛类型为友谊赛,则给报名成功的创建一场比赛
 * <p>
 * 2.报名中->待退款:当联赛到了报名截止时间,且满足报名条件的球队小于最小球队数，则将状态改为待退款，
 * 操作1:将所有报名的退款状态改成全部退款
 * <p>
 * 3.待开始->进行中:当联赛到了开始时间，则将状态改为进行中
 * <p>
 * 4.进行中->已结束:由后台手动修改
 * 5.退款中->已取消:当所有订单都退款成功的时候，将状态改为已取消
 *
 * <AUTHOR>
 */
@Component
public class LeagueStatusUpdateJob implements JobHandler {


    @Resource
    private LeagueService leagueService;

    @Override
    public String execute(String param) throws Exception {

        int count = leagueService.updateLeagueStatus();

        return StrUtil.format("更新联赛状态，共更新 {} 个", count);
    }
}
