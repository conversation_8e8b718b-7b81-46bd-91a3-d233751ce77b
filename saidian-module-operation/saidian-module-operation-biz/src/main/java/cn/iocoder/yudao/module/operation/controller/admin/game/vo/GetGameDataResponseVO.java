package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import cn.iocoder.yudao.module.operation.controller.admin.team.vo.TeamRespVO;
import lombok.Data;

import java.util.List;

@Data
public class GetGameDataResponseVO {
    /**
     * gameInfo: Game;
     *   homeTeam: Team; // 包含该场比赛的球员列表
     *   guestTeam: Team; // 包含该场比赛的球员列表
     *   initialState: { // 用于恢复前端状态
     *     homeActivePlayers: Player[]; // 上次记录的活跃球员
     *     guestActivePlayers: Player[];
     *     currentSection: number;
     *     isGamePaused: boolean;
     *     gameClockSeconds?: number; // 如果是进行中的比赛
     *     shotClockSeconds?: number;
     *     homeScore: number;
     *     guestScore: number;
     *     entryMode: 'realtime' | 'post';
     *   };
     *   playerStats?: PlayerStats[]; // 全场或指定节次的球员统计
     *   teamStats?: TeamStats[];   // 全场或指定节次的球队统计
     *   records?: DataRecord[];   // 详细的操作记录列表
     */
    private GameRespVO gameInfo;
    private TeamRespVO homeTeam;
    private List<DataRecordPlayerRespVO> homeTeamPlayers;

    private TeamRespVO guestTeam;
    private List<DataRecordPlayerRespVO> guestTeamPlayers;
    private GameInitialStateVO initialState;

    private List<DataRecordRespVO> dataRecords;

}
