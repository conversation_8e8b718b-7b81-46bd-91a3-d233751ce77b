package cn.iocoder.yudao.module.operation.service.state.impl;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import cn.iocoder.yudao.module.operation.service.registration.event.RegistrationCancelledEvent;
import cn.iocoder.yudao.module.operation.service.state.RegistrationState;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 已支付状态处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PaidState implements RegistrationState {

    @Resource
    private RegistrationMapper registrationMapper;
    
    @Resource
    private ActivityRefundService activityRefundService;
    
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public void handlePaySuccess(RegistrationDO registration, PayOrderNotifyReqDTO notifyReqDTO) {
        log.warn("[handlePaySuccess][PaidState] registrationId({}) 状态已是已支付，重复回调? 忽略。", registration.getId());
        // 已经是已支付状态，通常忽略重复的回调
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(RegistrationDO registration, Long operatorId, String cancelReason) {
        log.info("[cancel][PaidState] registrationId({}) 已支付状态下取消，转入退款流程，原因: {}", registration.getId(), cancelReason);
        
        String finalCancelReason = StringUtils.hasText(cancelReason) ? cancelReason : "用户主动取消";
        
        // 1. 检查是否允许取消 (如未过截止时间)
        // 暂时简单实现，后续可根据业务需求添加更多限制条件
        
        // 2. 调用退款服务发起全额退款
        // 注意：这里只发起退款，状态更新在退款成功回调中处理
        try {
            activityRefundService.processRefund(registration.getId(), RefundScenario.USER_CANCEL, operatorId, finalCancelReason);
            log.info("[cancel][PaidState] registrationId({}) 发起退款成功，等待退款完成后更新状态", registration.getId());
            
            // 🔧 修复：事件发布移到退款成功处理之后，避免事务传播问题
            // 由于processRefund使用REQUIRES_NEW独立事务，即使当前事务回滚也不影响退款
            // 因此将事件发布移到退款服务内部处理，确保事务一致性
            // log.info("[cancel][PaidState] registrationId({}) 退款处理成功，事件将在退款服务内部发布", registration.getId());
            
        } catch (Exception e) {
            log.error("[cancel][PaidState] registrationId({}) 发起退款失败", registration.getId(), e);
            throw e; // 重新抛出异常回滚事务
        }

        // 🔧 修复：移除此处的事件发布，避免事务传播导致的数据不一致
        // 事件发布现在在退款服务内部处理，确保与退款状态的一致性
        // eventPublisher.publishEvent(new RegistrationCancelledEvent(this, registration.getId(), operatorId, finalCancelReason));
    }

    @Override
    public void applyRefund(RegistrationDO registration, String reason) {
        log.info("[applyRefund][PaidState] registrationId({}) 已支付状态下申请退款, 原因: {}", registration.getId(), reason);
        
        // 已支付状态下申请退款逻辑与取消类似，可以调用 cancel 方法
        // 或者实现特定的退款逻辑
        cancel(registration, registration.getUserId(), reason);
    }

    @Override
    public void complete(RegistrationDO registration) {
        log.info("[complete][PaidState] registrationId({}) 已支付状态下完成", registration.getId());
        
        // 更新状态为 COMPLETED
        RegistrationDO updateObj = new RegistrationDO();
        updateObj.setId(registration.getId());
        updateObj.setStatus(RegistrationStatusEnum.COMPLETED.getStatus());
        
        int updateCount = registrationMapper.updateById(updateObj);
        if (updateCount == 0) {
            log.error("[complete][PaidState] registrationId({}) 更新报名记录状态失败", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_STATUS_UPDATE_FAILED);
        }
        
        log.info("[complete][PaidState] registrationId({}) 报名状态更新为已完成成功", registration.getId());
    }
} 