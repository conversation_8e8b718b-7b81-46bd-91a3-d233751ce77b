package cn.iocoder.yudao.module.operation.constant;

/**
 * 活动费用相关常量
 * 
 * <AUTHOR> Assistant
 */
public class ActivityFeeConstants {

    /**
     * 默认场地费(分) - 800元
     */
    public static final Integer DEFAULT_VENUE_FEE = 80000;
    
    /**
     * 默认配套费(分) - 360元 
     */
    public static final Integer DEFAULT_SUPPORT_FEE = 36000;
    
    /**
     * 场地费说明
     */
    public static final String VENUE_FEE_DESCRIPTION = "篮球场地租赁费用，包含场地使用、基础设施维护等";
    
    /**
     * 配套费说明  
     */
    public static final String SUPPORT_FEE_DESCRIPTION = "赛事服务费，包含裁判费、器材维护、赛事组织等";
    
    /**
     * 默认主队颜色
     */
    public static final String DEFAULT_HOME_TEAM_COLOR = "#FF0000";
    
    /**
     * 默认客队颜色
     */
    public static final String DEFAULT_AWAY_TEAM_COLOR = "#0000FF";
    
} 