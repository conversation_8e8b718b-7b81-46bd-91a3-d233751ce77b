package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class GamePlayerReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "633")
    private Long id;

    @Schema(description = "playerId", requiredMode = Schema.RequiredMode.REQUIRED, example = "633")
    private Long playerId;

    /**
     * 球员名字
     */
    @Schema(description = "球员名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String playerName;

    @Schema(description = "球队", requiredMode = Schema.RequiredMode.REQUIRED, example = "home or guest")
    private String team;

}
