package cn.iocoder.yudao.module.operation.controller.app.league.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 App - 联赛、友谊赛分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppLeaguePageReqVO extends PageParam {

    @Schema(description = "联赛模式", example = "1")
    private Integer mode;
}
