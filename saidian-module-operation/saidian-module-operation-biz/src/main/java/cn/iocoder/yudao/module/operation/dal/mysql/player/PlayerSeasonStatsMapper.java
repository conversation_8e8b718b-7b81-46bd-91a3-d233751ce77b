package cn.iocoder.yudao.module.operation.dal.mysql.player;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 球员赛季统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlayerSeasonStatsMapper extends BaseMapperX<PlayerSeasonStatsDO> {
}