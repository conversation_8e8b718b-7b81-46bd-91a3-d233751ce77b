package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 统计数据上传 Request VO")
@Data
public class StatsUploadReqVO {

    @Schema(description = "比赛编号", required = true, example = "1024")
    @NotNull
    private Long gameId;

    @Schema(description = "球员统计数据列表")
    private List<PlayerStatItem> playerStats;

    @Schema(description = "球队统计数据列表")
    private List<TeamStatItem> teamStats;

    /**
     * 球员统计项
     */
    @Data
    public static class PlayerStatItem {
        
        @Schema(description = "球员编号", required = true, example = "1024")
        @NotEmpty(message = "球员编号不能为空")
        private Long playerId;

        @Schema(description = "球员名称")
        private String playerName;

        @Schema(description = "球队编号")
        private Long teamId;

        @Schema(description = "球队名称")
        private String teamName;

        @Schema(description = "节次", required = true, example = "1")
        private Integer period;

        @Schema(description = "上场时间（秒）", example = "720")
        private Integer playingTimeSeconds;

        @Schema(description = "上场时间（格式化）")
        private String playingTimeFormatted;

        @Schema(description = "得分", example = "10")
        private Integer score;

        @Schema(description = "助攻数", example = "5")
        private Integer assists;

        @Schema(description = "抢断数", example = "2")
        private Integer steals;

        @Schema(description = "盖帽数", example = "1")
        private Integer blocks;

        @Schema(description = "失误数", example = "2")
        private Integer turnovers;

        @Schema(description = "犯规数", example = "3")
        private Integer fouls;

        @Schema(description = "两分出手", example = "5")
        private Integer twoPointAttempts;

        @Schema(description = "两分命中", example = "3")
        private Integer twoPointHits;

        @Schema(description = "两分命中率")
        private String twoPointRate;

        @Schema(description = "三分出手", example = "4")
        private Integer threePointAttempts;

        @Schema(description = "三分命中", example = "2")
        private Integer threePointHits;

        @Schema(description = "三分命中率")
        private String threePointRate;

        @Schema(description = "罚球出手", example = "2")
        private Integer freeThrowAttempts;

        @Schema(description = "罚球命中", example = "2")
        private Integer freeThrowHits;

        @Schema(description = "罚球命中率")
        private String freeThrowRate;

        @Schema(description = "前场篮板球", example = "1")
        private Integer offensiveRebounds;

        @Schema(description = "后场篮板球", example = "3")
        private Integer defensiveRebounds;

        @Schema(description = "总篮板")
        private Integer totalRebounds;

        @Schema(description = "快攻出手")
        private Integer fastBreakAttempts;

        @Schema(description = "快攻命中")
        private Integer fastBreakHits;

        @Schema(description = "快攻命中率")
        private String fastBreakRate;

        @Schema(description = "投篮出手")
        private Integer fieldGoalAttempts;

        @Schema(description = "投篮命中")
        private Integer fieldGoalHits;

        @Schema(description = "投篮命中率")
        private String fieldGoalRate;

        @Schema(description = "是否活跃")
        private Boolean isActive;

        @Schema(description = "效率值")
        private Integer efficiency;

        @Schema(description = "正负值")
        private Integer plusMinus;

        @Schema(description = "真实命中率")
        private String tsRate;
    }

    /**
     * 球队统计项
     */
    @Data
    public static class TeamStatItem {
        
        @Schema(description = "球队编号", required = true, example = "1024")
        @NotEmpty(message = "球队编号不能为空")
        private Long teamId;

        @Schema(description = "球队名称")
        private String teamName;

        @Schema(description = "节次", required = true, example = "1")
        private Integer period;

        @Schema(description = "得分", example = "24")
        private Integer score;

        @Schema(description = "助攻数", example = "10")
        private Integer assists;

        @Schema(description = "抢断数", example = "5")
        private Integer steals;

        @Schema(description = "盖帽数", example = "3")
        private Integer blocks;

        @Schema(description = "失误数", example = "7")
        private Integer turnovers;

        @Schema(description = "犯规数", example = "8")
        private Integer fouls;

        @Schema(description = "两分出手", example = "20")
        private Integer twoPointAttempts;

        @Schema(description = "两分命中", example = "10")
        private Integer twoPointHits;

        @Schema(description = "两分命中率")
        private String twoPointRate;

        @Schema(description = "三分出手", example = "15")
        private Integer threePointAttempts;

        @Schema(description = "三分命中", example = "5")
        private Integer threePointHits;

        @Schema(description = "三分命中率")
        private String threePointRate;

        @Schema(description = "罚球出手", example = "8")
        private Integer freeThrowAttempts;

        @Schema(description = "罚球命中", example = "6")
        private Integer freeThrowHits;

        @Schema(description = "罚球命中率")
        private String freeThrowRate;

        @Schema(description = "前场篮板球", example = "5")
        private Integer offensiveRebounds;

        @Schema(description = "后场篮板球", example = "15")
        private Integer defensiveRebounds;

        @Schema(description = "总篮板")
        private Integer totalRebounds;

        @Schema(description = "快攻出手")
        private Integer fastBreakAttempts;

        @Schema(description = "快攻命中")
        private Integer fastBreakHits;

        @Schema(description = "快攻命中率")
        private String fastBreakRate;

        @Schema(description = "投篮出手")
        private Integer fieldGoalAttempts;

        @Schema(description = "投篮命中")
        private Integer fieldGoalHits;

        @Schema(description = "投篮命中率")
        private String fieldGoalRate;

        @Schema(description = "暂停次数")
        private Integer timeouts;

        @Schema(description = "效率值")
        private Integer efficiency;

        @Schema(description = "正负值")
        private Integer plusMinus;

        @Schema(description = "真实命中率")
        private String tsRate;
    }
} 