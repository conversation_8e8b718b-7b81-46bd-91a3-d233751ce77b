package cn.iocoder.yudao.module.operation.job;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.registration.PayStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationService;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 🚀 支付超时清理定时任务
 * 
 * 专门处理支付超时的报名记录，提供更完善的清理和恢复机制：
 * 
 * 1. **支付超时检测**：
 *    - 检测支付订单已关闭但报名状态仍为待支付的记录
 *    - 检测创建时间超过30分钟但仍未支付的记录
 * 
 * 2. **状态清理**：
 *    - 更新报名状态为已取消
 *    - 更新支付状态为已关闭
 *    - 记录取消原因
 * 
 * 3. **候补处理**：
 *    - 候补球员：从候补队列中移除并重排序
 *    - 正式球员：自动提升候补球员填补空缺
 * 
 * 4. **活动统计更新**：
 *    - 更新活动的报名人数统计
 *    - 触发相关的业务事件
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class PaymentTimeoutCleanupJob implements JobHandler {

    @Resource
    private RegistrationService registrationService;
    
    @Resource
    private RegistrationMapper registrationMapper;
    
    @Resource
    private ActivityMapper activityMapper;
    
    @Resource
    private WaitlistService waitlistService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[PaymentTimeoutCleanupJob] 开始执行支付超时清理任务");
        
        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        
        try {
            // 1. 处理支付订单状态不一致的记录
            int inconsistentCount = processInconsistentPaymentStatus();
            totalProcessed += inconsistentCount;
            
            // 2. 处理创建时间超时的记录
            int timeoutCount = processTimeoutRegistrations();
            totalProcessed += timeoutCount;
            
            // 3. 批量处理候补提升
            int promotedCount = batchPromoteWaitlist();
            
            long endTime = System.currentTimeMillis();
            String result = String.format(
                "支付超时清理完成 - 处理超时记录: %d, 状态不一致: %d, 创建超时: %d, 候补提升: %d, 耗时: %dms",
                totalProcessed, inconsistentCount, timeoutCount, promotedCount, (endTime - startTime)
            );
            
            log.info("[PaymentTimeoutCleanupJob] {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("[PaymentTimeoutCleanupJob] 执行失败", e);
            throw e;
        }
    }

    /**
     * 处理支付订单状态不一致的记录
     */
    @Transactional(rollbackFor = Exception.class)
    public int processInconsistentPaymentStatus() {
        log.info("[processInconsistentPaymentStatus] 开始处理支付状态不一致的记录");
        
        // 调用现有的超时处理逻辑
        return registrationService.cancelTimeoutRegistrations();
    }

    /**
     * 处理创建时间超时的记录（30分钟）
     */
    @Transactional(rollbackFor = Exception.class)
    public int processTimeoutRegistrations() {
        log.info("[processTimeoutRegistrations] 开始处理创建时间超时的记录");
        
        // 查询30分钟前创建但仍未支付的记录
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);
        
        List<RegistrationDO> timeoutRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PENDING_PAYMENT.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.UNPAID.getStatus())
                        .lt(RegistrationDO::getCreateTime, timeoutThreshold)
        );
        
        if (CollUtil.isEmpty(timeoutRegistrations)) {
            log.info("[processTimeoutRegistrations] 没有找到创建时间超时的记录");
            return 0;
        }
        
        log.info("[processTimeoutRegistrations] 找到 {} 条创建时间超时的记录", timeoutRegistrations.size());
        
        int processedCount = 0;
        for (RegistrationDO registration : timeoutRegistrations) {
            try {
                updateRegistrationToTimeout(registration, "创建时间超过30分钟未支付");
                processedCount++;
            } catch (Exception e) {
                log.error("[processTimeoutRegistrations] 处理超时记录失败: registrationId={}", 
                         registration.getId(), e);
            }
        }
        
        return processedCount;
    }

    /**
     * 批量处理候补提升
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchPromoteWaitlist() {
        log.info("[batchPromoteWaitlist] 开始批量处理候补提升");
        
        // 查询所有有候补球员的活动
        List<RegistrationDO> waitlistRegistrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .in(RegistrationDO::getStatus,
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus())
                        .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.PAID.getStatus())
                        .select(RegistrationDO::getActivityId)
        );
        
        if (CollUtil.isEmpty(waitlistRegistrations)) {
            log.info("[batchPromoteWaitlist] 没有找到有候补球员的活动");
            return 0;
        }
        
        // 按活动分组
        Map<Long, List<RegistrationDO>> activityGroups = waitlistRegistrations.stream()
                .collect(Collectors.groupingBy(RegistrationDO::getActivityId));
        
        int totalPromoted = 0;
        for (Long activityId : activityGroups.keySet()) {
            try {
                ActivityDO activity = activityMapper.selectById(activityId);
                if (activity != null) {
                    List<RegistrationDO> promoted = waitlistService.promoteFromWaitlist(activity);
                    totalPromoted += promoted.size();
                    
                    if (!promoted.isEmpty()) {
                        log.info("[batchPromoteWaitlist] 活动 {} 提升了 {} 个候补球员", 
                                activityId, promoted.size());
                    }
                }
            } catch (Exception e) {
                log.error("[batchPromoteWaitlist] 处理活动候补提升失败: activityId={}", activityId, e);
            }
        }
        
        return totalPromoted;
    }

    /**
     * 更新报名记录为超时状态
     */
    private void updateRegistrationToTimeout(RegistrationDO registration, String reason) {
        RegistrationDO updateObj = new RegistrationDO();
        updateObj.setId(registration.getId());
        updateObj.setStatus(RegistrationStatusEnum.CANCELLED.getStatus());
        updateObj.setPaymentStatus(PayStatusEnum.CLOSED.getStatus());
        updateObj.setCancelReason(reason);
        updateObj.setUpdateTime(LocalDateTime.now());

        int updateCount = registrationMapper.updateById(updateObj);
        if (updateCount == 0) {
            log.error("[updateRegistrationToTimeout] 更新报名记录失败: registrationId={}", registration.getId());
            return;
        }

        // 处理候补相关逻辑
        if (Boolean.TRUE.equals(registration.getIsWaitlist()) && registration.getWaitlistPosition() != null) {
            try {
                waitlistService.reorderWaitlist(registration.getActivityId(), registration.getWaitlistPosition());
                log.info("[updateRegistrationToTimeout] 候补球员超时，重排候补队列: registrationId={}, position={}", 
                        registration.getId(), registration.getWaitlistPosition());
            } catch (Exception e) {
                log.error("[updateRegistrationToTimeout] 重排候补队列失败: registrationId={}", 
                         registration.getId(), e);
            }
        }

        log.info("[updateRegistrationToTimeout] 成功处理超时报名: registrationId={}, reason={}", 
                registration.getId(), reason);
    }
}
