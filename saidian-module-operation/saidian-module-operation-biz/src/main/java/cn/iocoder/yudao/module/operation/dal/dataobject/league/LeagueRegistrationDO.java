package cn.iocoder.yudao.module.operation.dal.dataobject.league;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 联赛报名信息 DO
 *
 * <AUTHOR>
 */
@TableName("sd_league_registration")
@KeySequence("sd_league_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeagueRegistrationDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 联赛ID
     */
    private Long leagueId;
    /**
     * 球员id
     */
    private Long playerId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 账单id
     */
    private Long orderId;
    /**
     * 报名球队ID
     */
    private Long teamId;

    /**
     * 支付方式
     */
    private Integer payMode;
    private Integer payPrice;
    private Integer refundPrice;

    private Integer refundStatus;
    private Boolean payStatus;

    /**
     * 是否垫付人
     */
    private Boolean coverPayer;

}