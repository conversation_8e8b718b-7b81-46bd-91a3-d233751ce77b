package cn.iocoder.yudao.module.operation.dal.dataobject.club;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 俱乐部信息 DO
 *
 * <AUTHOR>
 */
@TableName("sd_club")
@KeySequence("sd_club_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClubDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 俱乐部名称
     */
    private String name;
    /**
     * 俱乐部logo
     */
    private String logo;
    /**
     * 俱乐部简介
     */
    private String description;

}