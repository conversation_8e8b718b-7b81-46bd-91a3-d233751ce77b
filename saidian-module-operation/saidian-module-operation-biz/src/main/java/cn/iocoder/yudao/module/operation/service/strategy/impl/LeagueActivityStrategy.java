package cn.iocoder.yudao.module.operation.service.strategy.impl;

import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.league.LeagueDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.league.LeagueScheduleService;
import cn.iocoder.yudao.module.operation.service.league.LeagueService;
import cn.iocoder.yudao.module.operation.service.strategy.AbstractActivityStrategy;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 联赛活动策略 - 继承通用抽象策略，专注于联赛特有逻辑
 */
@Service
@Slf4j
public class LeagueActivityStrategy extends AbstractActivityStrategy {

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private TeamService teamService;

    @Resource
    private LeagueService leagueService;

    @Resource
    private LeagueScheduleService leagueScheduleService;

    @Override
    public ActivityTypeEnum getActivityType() {
        return ActivityTypeEnum.LEAGUE;
    }

    @Override
    public Integer calculateEffectiveFee(ActivityDO activity, FeeCalculationContext context) {
        // 联赛按固定人均费用收费，不受其他因素影响
        if (activity.getLeagueFeePerPlayer() == null || activity.getLeagueFeePerPlayer() <= 0) {
            log.error("[calculateEffectiveFee][联赛活动 {} 费用配置错误] LeagueFeePerPlayer: {}",
                    activity.getId(), activity.getLeagueFeePerPlayer());
            throw exception(ErrorCodeConstants.ACTIVITY_CONFIG_ERROR, "联赛活动费用配置错误");
        }
        return activity.getLeagueFeePerPlayer();
    }

    @Override
    public void createGamesAfterGrouping(ActivityDO activity) {
        log.info("[createGamesAfterGrouping][联赛][活动 {}] 组局成功，创建联赛赛程", activity.getId());

        try {
            // 🔧 优化：使用统一的状态查询服务获取所有有效的报名记录
            List<RegistrationDO> paidRegistrations = registrationStatusQueryService.getFormalRegistrations(activity.getId());

            if (paidRegistrations.isEmpty()) {
                log.warn("[createGamesAfterGrouping][联赛][活动 {}] 没有已支付的报名记录", activity.getId());
                return;
            }

            // 获取队伍ID列表
            List<Long> teamIds = paidRegistrations.stream()
                    .map(RegistrationDO::getTeamId)
                    .filter(java.util.Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (teamIds.isEmpty()) {
                log.warn("[createGamesAfterGrouping][联赛][活动 {}] 没有有效的队伍", activity.getId());
                return;
            }

            // 创建联赛，使用leagueService的正确方法
            LeagueDO league = leagueService.createLeagueFromActivity(activity, teamIds);

            if (league != null) {
                // 生成联赛赛程
                leagueScheduleService.generateSchedule(league.getId(), teamIds);

                log.info("[createGamesAfterGrouping][联赛][活动 {}] 联赛创建成功，ID: {}", activity.getId(), league.getId());
            } else {
                log.error("[createGamesAfterGrouping][联赛][活动 {}] 创建联赛失败", activity.getId());
            }

        } catch (Exception e) {
            log.error("[createGamesAfterGrouping][联赛][活动 {}] 创建联赛失败", activity.getId(), e);
            throw exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "创建联赛失败: " + e.getMessage());
        }
    }

    @Override
    protected int getMinRequiredPlayersForGrouping(ActivityDO activity) {
        // 联赛需要最小队伍数 * 每队最小人数
        Integer minTeams = activity.getMinTeamsLeague();
        Integer minPlayersPerTeam = activity.getMinPlayersPerTeamLeague();

        if (minTeams == null || minPlayersPerTeam == null) {
            log.warn("[getMinRequiredPlayersForGrouping][联赛][活动 {}] 缺少必要配置", activity.getId());
            return 4; // 默认最少4人
        }

        return minTeams * minPlayersPerTeam;
    }

    @Override
    public void handlePaymentSuccessAfterStateUpdate(ActivityDO activity, RegistrationDO registration) {
        // 联赛支付成功后的特殊处理
        log.info("[handlePaymentSuccessAfterStateUpdate][联赛][活动 {}] 支付成功处理，报名ID: {}",
                activity.getId(), registration.getId());

        // 1. 优先处理组局成功后的状态转换
        handlePostGroupingRegistrationStatus(activity, registration);

        // 2. 联赛通常不需要特殊的支付后处理逻辑，组局检查由定时任务处理
    }

    @Override
    protected void doValidateRegistrationRequest(ActivityDO activity, AppRegistrationCreateReqVO createReqVO, Long userId) {
        // 联赛必须提供队伍ID
        if (createReqVO.getTeamId() == null) {
            throw exception(ErrorCodeConstants.TEAM_NOT_FOUND, "联赛活动必须指定队伍ID");
        }

        // 校验联赛费用配置
        if (activity.getLeagueFeePerPlayer() == null || activity.getLeagueFeePerPlayer() <= 0) {
            throw exception(ErrorCodeConstants.ACTIVITY_CONFIG_ERROR, "联赛活动费用配置错误");
        }

        // 验证队伍是否存在
        try {
            if (teamService.getTeam(createReqVO.getTeamId()) == null) {
                throw exception(ErrorCodeConstants.TEAM_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("[doValidateRegistrationRequest][联赛][活动 {}] 队伍ID {} 校验失败: {}",
                    activity.getId(), createReqVO.getTeamId(), e.getMessage());
            throw exception(ErrorCodeConstants.TEAM_NOT_EXISTS);
        }

        log.info("[doValidateRegistrationRequest][联赛] 请求参数校验通过: activityId={}, teamId={}, feePerPlayer={}",
                activity.getId(), createReqVO.getTeamId(), activity.getLeagueFeePerPlayer());
    }





    @Override
    protected int processDifferentialRefund(ActivityDO activity) {
        // 联赛固定费用，无需差额退款
        log.info("[processDifferentialRefund][联赛] 联赛活动 id={} 采用固定费用，无需差额退款", activity.getId());
        return 0;
    }
}