package cn.iocoder.yudao.module.operation.service.teamassignment.impl;

import java.util.Arrays;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.friendgroup.FriendGroupDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.friendgroup.FriendGroupMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationStatusQueryService;
import cn.iocoder.yudao.module.operation.service.teamassignment.algorithm.TeamAssignmentAlgorithmManager;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResultBO;
import cn.iocoder.yudao.module.operation.service.teamassignment.event.PlayerTeamAssignedEvent;
import cn.iocoder.yudao.module.operation.service.teamassignment.event.BatchTeamAssignmentEvent;
import cn.iocoder.yudao.module.operation.service.teamassignment.event.WaitlistPromotionEvent;
import cn.iocoder.yudao.module.operation.service.teamassignment.monitor.TeamAssignmentMonitorService;
import cn.iocoder.yudao.module.operation.util.TeamBalanceScoreCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 团队分配服务实现类
 * <p>
 * 核心职责：
 * 1. 协调算法管理器执行分队算法
 * 2. 管理分队操作的事务处理
 * 3. 发布分队相关事件
 * 4. 提供分队历史记录和分析
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TeamAssignmentServiceImpl implements TeamAssignmentService {

    @Resource
    private TeamAssignmentAlgorithmManager algorithmManager;

    @Resource
    private TeamAssignmentMonitorService monitorService;

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private PlayerService playerService;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private FriendGroupMapper friendGroupMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private RegistrationStatusQueryService registrationStatusQueryService;

    /**
     * 分队历史记录缓存
     */
    private final Map<Long, List<TeamAssignmentResult>> assignmentHistory = new HashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeamAssignmentResult assignPlayerToTeam(@NotNull Long activityId, @NotNull Long registrationId) {
        log.info("[assignPlayerToTeam] 开始实时分队 - 活动ID: {}, 报名ID: {}", activityId, registrationId);

        try {
            // 1. 参数验证和数据准备
            ActivityDO activity = validateActivityAndPlayer(activityId, registrationId);
            PlayerRegistrationInfo newPlayer = buildPlayerRegistrationInfo(registrationId);

            if (newPlayer == null) {
                log.error("[assignPlayerToTeam] 构建球员信息失败 - 报名ID: {}", registrationId);
                return TeamAssignmentResult.failure("球员信息获取失败");
            }

            // 2. 获取当前队伍状态
            List<PlayerRegistrationInfo> currentHomeTeam = getCurrentTeamPlayers(activityId, activity.getHomeTeamId());
            List<PlayerRegistrationInfo> currentGuestTeam = getCurrentTeamPlayers(activityId, activity.getGuestTeamId());

            // 3. 记录操作前状态
            TeamAssignmentResultBO.TeamSnapshotBO beforeSnapshot = createTeamSnapshot(
                    currentHomeTeam, currentGuestTeam, Collections.singletonList(newPlayer));
            log.info("[assignPlayerToTeam] 分队前的情况：{}", JsonUtils.toJsonString(beforeSnapshot));

            // 4. 执行分队算法
            long startTime = System.currentTimeMillis();
            TeamAssignmentResult result = algorithmManager.assignPlayerToTeam(activity, newPlayer, currentHomeTeam, currentGuestTeam);
            long executionTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isSuccess()) {
                // 5. 更新数据库
                updateRegistrationTeamAssignment(result.getUpdatedRegistrations());

                // 6. 记录监控数据
                monitorService.recordAssignmentExecution(activity, "REALTIME_ASSIGN", result,
                        executionTime, currentHomeTeam.size() + currentGuestTeam.size() + 1);

                // 7. 检查是否需要重平衡
                boolean needsRebalance = needsReassignment(activityId);
                log.info("[assignPlayerToTeam] 分队后平衡度检查 - 活动ID: {}, 需要重平衡: {}, 当前平衡分数: {}", 
                        activityId, needsRebalance, result.getBalanceScore());
                
                if (needsRebalance) {
                    log.info("[assignPlayerToTeam] 触发重平衡 - 活动ID: {}, 当前平衡分数过低: {}", 
                            activityId, result.getBalanceScore());
                    
                    // 使用安全的重平衡方法，最多重试3次
                    TeamAssignmentResult rebalanceResult = performSafeRebalance(activityId, 3);
                    if (rebalanceResult != null && rebalanceResult.isSuccess()) {
                        log.info("[assignPlayerToTeam] 重平衡完成 - 活动ID: {}, 重平衡后分数: {}", 
                                activityId, rebalanceResult.getBalanceScore());
                        result = rebalanceResult; // 使用重平衡后的结果
                    } else {
                        log.warn("[assignPlayerToTeam] 重平衡失败 - 活动ID: {}, 继续使用原分队结果", activityId);
                    }
                }

                // 8. 发布事件（事件消费者会处理游戏同步）
                publishPlayerAssignedEvent(activityId, registrationId, result);

                // 9. 保存历史记录
                saveAssignmentHistory(activityId, result);

                log.info("[assignPlayerToTeam] 实时分队完成 - 活动ID: {}, 报名ID: {}, 分配到队伍: {}",
                        activityId, registrationId, result.getPromotedPlayerTeam());
            } else if (result != null) {
                log.warn("[assignPlayerToTeam] 实时分队失败 - 活动ID: {}, 报名ID: {}, 原因: {}",
                        activityId, registrationId, result.getFailureReason());
            } else {
                log.error("[assignPlayerToTeam] 算法返回空结果 - 活动ID: {}, 报名ID: {}", activityId, registrationId);
                return TeamAssignmentResult.failure("分队算法执行异常");
            }

            return result;

        } catch (Exception e) {
            log.error("[assignPlayerToTeam] 实时分队异常 - 活动ID: {}, 报名ID: {}", activityId, registrationId, e);
            return TeamAssignmentResult.failure("实时分队处理异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeamAssignmentResult assignAllPlayers(@NotNull Long activityId) {
        log.info("[assignAllPlayers] 开始批量分队 - 活动ID: {}", activityId);

        try {
            // 1. 验证活动状态
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            // 2. 获取所有已支付、组局成功的球员
            List<PlayerRegistrationInfo> allPlayers = getAllValidPlayers(activityId);
            if (allPlayers.isEmpty()) {
                log.warn("[assignAllPlayers] 活动 {} 没有已支付的球员", activityId);
                return TeamAssignmentResult.failure("没有需要分配的球员");
            }

            // 3. 执行批量分队算法
            long startTime = System.currentTimeMillis();
            TeamAssignmentResult result = algorithmManager.assignAllPlayers(activity, allPlayers);
            long executionTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isSuccess()) {
                // 4. 批量更新数据库
                updateRegistrationTeamAssignment(result.getUpdatedRegistrations());

                // 5. 记录监控数据
                monitorService.recordAssignmentExecution(activity, "BATCH_ASSIGN", result,
                        executionTime, allPlayers.size());

                // 6. 发布批量分队完成事件（事件消费者会处理游戏同步）
                publishBatchAssignmentEvent(activityId, result);

                // 7. 保存历史记录
                saveAssignmentHistory(activityId, result);

                log.info("[assignAllPlayers] 批量分队完成 - 活动ID: {}, 主队人数: {}, 客队人数: {}",
                        activityId, result.getHomeTeamPlayers().size(), result.getGuestTeamPlayers().size());

            } else if (result != null) {
                log.warn("[assignAllPlayers] 批量分队失败 - 活动ID: {}, 原因: {}", activityId, result.getFailureReason());
            } else {
                log.error("[assignAllPlayers] 算法返回空结果 - 活动ID: {}", activityId);
                return TeamAssignmentResult.failure("分队算法执行异常");
            }

            return result;

        } catch (Exception e) {
            log.error("[assignAllPlayers] 批量分队异常 - 活动ID: {}", activityId, e);
            return TeamAssignmentResult.failure("批量分队处理异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeamAssignmentResult rebalanceAfterWithdrawal(@NotNull Long activityId, @NotNull List<Long> withdrawnRegistrationIds) {
        log.info("[rebalanceAfterWithdrawal] 开始重新平衡 - 活动ID: {}, 退出报名数: {}",
                activityId, withdrawnRegistrationIds.size());

        try {
            // 1. 验证参数
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            // 2. 获取剩余球员列表
            List<PlayerRegistrationInfo> remainingPlayers = getRemainingPlayersAfterWithdrawal(activityId, withdrawnRegistrationIds);
            List<PlayerRegistrationInfo> currentHomeTeam = getCurrentTeamPlayers(activityId, activity.getHomeTeamId());
            List<PlayerRegistrationInfo> currentGuestTeam = getCurrentTeamPlayers(activityId, activity.getGuestTeamId());
            
            // 关键验证：检查总人数是否超出限制
            int totalPlayers = remainingPlayers.size();
            Integer maxCapacity = activity.getMaxPlayersPerGame();
            if (maxCapacity != null && totalPlayers > maxCapacity) {
                log.warn("[rebalanceAfterWithdrawal] 总人数({})超出限制({}), 活动ID: {}", 
                        totalPlayers, maxCapacity, activityId);
                return TeamAssignmentResult.failure("重新分队失败：总人数超出活动限制");
            }

            // 移除退出的球员
            currentHomeTeam.removeIf(p -> withdrawnRegistrationIds.contains(p.getRegistration().getId()));
            currentGuestTeam.removeIf(p -> withdrawnRegistrationIds.contains(p.getRegistration().getId()));

            // 3. 执行重新平衡算法
            long startTime = System.currentTimeMillis();
            TeamAssignmentResult result = algorithmManager.rebalanceTeams(
                    activity, remainingPlayers, currentHomeTeam, currentGuestTeam);
            long executionTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isSuccess()) {
                // 4. 更新数据库
                updateRegistrationTeamAssignment(result.getUpdatedRegistrations());

                // 5. 记录监控数据
                monitorService.recordAssignmentExecution(activity, "REBALANCE", result,
                        executionTime, remainingPlayers.size());

                // 6. 发布重平衡事件（事件消费者会处理游戏同步和数据修复）
                publishBatchAssignmentEvent(activityId, result);

                // 7. 保存历史记录
                saveAssignmentHistory(activityId, result);

                log.info("[rebalanceAfterWithdrawal] 重新平衡完成 - 活动ID: {}", activityId);
            }

            return result;

        } catch (Exception e) {
            log.error("[rebalanceAfterWithdrawal] 重新平衡异常 - 活动ID: {}", activityId, e);
            return TeamAssignmentResult.failure("重新平衡处理异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeamAssignmentResult promoteFromWaitlist(@NotNull Long activityId, int vacantSlots) {
        log.info("[promoteFromWaitlist] 开始候补晋升 - 活动ID: {}, 空缺位置: {}", activityId, vacantSlots);

        try {
            // 1. 验证参数
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            // 2. 获取候补球员和当前队伍
            List<PlayerRegistrationInfo> waitlistPlayers = getWaitlistPlayers(activityId);
            List<PlayerRegistrationInfo> currentHomeTeam = getCurrentTeamPlayers(activityId, activity.getHomeTeamId());
            List<PlayerRegistrationInfo> currentGuestTeam = getCurrentTeamPlayers(activityId, activity.getGuestTeamId());

            if (waitlistPlayers.isEmpty()) {
                log.info("[promoteFromWaitlist] 活动 {} 没有候补球员", activityId);
                return TeamAssignmentResult.failure("没有候补球员可以晋升");
            }

            // 3. 执行候补晋升算法
            long startTime = System.currentTimeMillis();
            TeamAssignmentResult result = algorithmManager.promoteFromWaitlist(
                    activity, waitlistPlayers, currentHomeTeam, currentGuestTeam, vacantSlots);
            long executionTime = System.currentTimeMillis() - startTime;

            if (result != null && result.isSuccess() && result.getPromotedPlayerId() != null) {
                // 4. 更新数据库
                updateRegistrationTeamAssignment(result.getUpdatedRegistrations());

                // 5. 记录监控数据
                monitorService.recordAssignmentExecution(activity, "WAITLIST_PROMOTE", result,
                        executionTime, waitlistPlayers.size());

                // 6. 发布候补晋升事件
                publishWaitlistPromotionEvent(activityId, result.getPromotedPlayerId(), result);

                // 7. 保存历史记录
                saveAssignmentHistory(activityId, result);

                log.info("[promoteFromWaitlist] 候补晋升完成 - 活动ID: {}, 晋升球员: {}",
                        activityId, result.getPromotedPlayerId());
            }

            return result;

        } catch (Exception e) {
            log.error("[promoteFromWaitlist] 候补晋升异常 - 活动ID: {}", activityId, e);
            return TeamAssignmentResult.failure("候补晋升处理异常: " + e.getMessage());
        }
    }

    @Override
    public TeamAssignmentResult.BalanceEvaluation evaluateTeamBalance(@NotNull Long activityId) {
        log.debug("[evaluateTeamBalance] 评估队伍平衡度 - 活动ID: {}", activityId);

        try {
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            List<PlayerRegistrationInfo> homeTeam = getCurrentTeamPlayers(activityId, activity.getHomeTeamId());
            List<PlayerRegistrationInfo> guestTeam = getCurrentTeamPlayers(activityId, activity.getGuestTeamId());

            return algorithmManager.evaluateTeamBalance(activity, homeTeam, guestTeam);

        } catch (Exception e) {
            log.error("[evaluateTeamBalance] 评估队伍平衡度异常 - 活动ID: {}", activityId, e);
            return null;
        }
    }

    @Override
    public TeamAssignmentResult getCurrentTeamAssignment(@NotNull Long activityId) {
        log.info("[getCurrentTeamAssignment] 获取当前队伍分配状态: activityId={}", activityId);

        // 获取活动信息
        ActivityDO activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        try {
            List<PlayerRegistrationInfo> homeTeam = getCurrentTeamPlayers(activityId, activity.getHomeTeamId());
            List<PlayerRegistrationInfo> guestTeam = getCurrentTeamPlayers(activityId, activity.getGuestTeamId());
            List<PlayerRegistrationInfo> unassigned = getUnassignedPlayers(activityId);

            // 计算平衡度评分
            TeamAssignmentResult.BalanceEvaluation balance = evaluateTeamBalance(activityId);
            
            // 计算综合平衡度分数（使用加权计算）
            int overallBalanceScore = 0;
            if (balance != null) {
                overallBalanceScore = TeamBalanceScoreCalculator.calculateWeightedOverallScore(balance);
                log.debug("[getCurrentTeamAssignment] {} - 活动ID: {}", 
                        TeamBalanceScoreCalculator.calculateContributionBreakdown(balance), activityId);
            }


            return TeamAssignmentResult.builder()
                    .success(true)
                    .homeTeamPlayers(homeTeam)
                    .guestTeamPlayers(guestTeam)
                    .unassignedPlayers(unassigned)
                    .balanceScore(overallBalanceScore)
                    .balanceEvaluation(balance)
                    .build();

        } catch (Exception e) {
            log.error("[getCurrentTeamAssignment] 获取队伍分配状态失败: activityId={}", activityId, e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TEMP_INTERNAL_SERVER_ERROR, "获取队伍分配状态失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeamAssignmentResult manualAssignment(@NotNull Long activityId, @NotNull Long registrationId, @NotNull Integer targetTeam) {
        log.info("[manualAssignment] 手动分队 - 活动ID: {}, 报名ID: {}, 目标队伍: {}",
                activityId, registrationId, targetTeam);

        try {
            // 1. 验证参数
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            if (targetTeam != 1 && targetTeam != 2) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_STATUS_ERROR, "目标队伍必须是1或2");
            }

            // 2. 获取球员信息
            PlayerRegistrationInfo player = buildPlayerRegistrationInfo(registrationId);
            if (player == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
            }

            // 3. 确定目标队伍ID
            Long targetTeamId = (targetTeam == 1) ? activity.getHomeTeamId() : activity.getGuestTeamId();

            // 4. 更新数据库
            RegistrationDO updateObj = new RegistrationDO();
            updateObj.setId(registrationId);
            updateObj.setTeamAssigned(targetTeamId);
            registrationMapper.updateById(updateObj);

            // 5. 重新评估队伍平衡度
            TeamAssignmentResult.BalanceEvaluation evaluation = evaluateTeamBalance(activityId);

            // 6. 构建结果
            TeamAssignmentResult result = TeamAssignmentResult.builder()
                    .success(true)
                    .balanceEvaluation(evaluation)
                    .build();

            // 7. 记录历史
            saveAssignmentHistory(activityId, result);

            log.info("[manualAssignment] 手动分队完成 - 活动ID: {}, 报名ID: {}, 目标队伍: {}",
                    activityId, registrationId, targetTeam);

            return result;

        } catch (Exception e) {
            log.error("[manualAssignment] 手动分队异常 - 活动ID: {}, 报名ID: {}", activityId, registrationId, e);
            return TeamAssignmentResult.failure("手动分队处理异常: " + e.getMessage());
        }
    }

    @Override
    public boolean needsReassignment(@NotNull Long activityId) {
        try {
            TeamAssignmentResult.BalanceEvaluation evaluation = evaluateTeamBalance(activityId);
            if (evaluation == null) {
                return false;
            }

            // 平衡度阈值检查（使用加权计算）
            int overallScore = TeamBalanceScoreCalculator.calculateWeightedOverallScore(evaluation);

            // 记录详细的平衡度信息和权重计算详情
            log.info("[needsReassignment] 活动ID: {}, 平衡度详情: 能力值={}, 身高={}, 位置={}, 好友组={}, 胜率={}, 加权综合评分={}",
                    activityId, evaluation.getAbilityBalance(), evaluation.getHeightBalance(),
                    evaluation.getPositionBalance(), evaluation.getFriendGroupBalance(),
                    evaluation.getWinRateBalance(), overallScore);
            
            log.debug("[needsReassignment] {} - 活动ID: {}", 
                    TeamBalanceScoreCalculator.calculateContributionBreakdown(evaluation), activityId);

            // 如果整体平衡度低于70分，建议重新分配
            boolean needsRebalance = overallScore < 70;
            
            // 特殊情况：如果某个关键维度（能力值、身高）严重不平衡，也需要重平衡
            if (!needsRebalance) {
                if (evaluation.getAbilityBalance() < 50 || evaluation.getHeightBalance() < 50) {
                    needsRebalance = true;
                    log.info("[needsReassignment] 活动ID: {}, 关键维度严重不平衡，触发重平衡", activityId);
                }
            }
            
            return needsRebalance;

        } catch (Exception e) {
            log.error("[needsReassignment] 检查是否需要重新分队异常 - 活动ID: {}", activityId, e);
            return false;
        }
    }

    @Override
    public List<TeamAssignmentResult> getAssignmentHistory(@NotNull Long activityId) {
        return assignmentHistory.getOrDefault(activityId, new ArrayList<>());
    }

    // =============== 私有辅助方法 ===============

    /**
     * 验证活动和报名记录信息
     */
    private ActivityDO validateActivityAndPlayer(Long activityId, Long registrationId) {
        ActivityDO activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
        }

        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_NOT_EXISTS);
        }

        if (!registration.getActivityId().equals(activityId)) {
            throw new IllegalArgumentException("报名记录与活动不匹配");
        }

        return activity;
    }

    /**
     * 构建球员报名信息对象
     */
    private PlayerRegistrationInfo buildPlayerRegistrationInfo(Long registrationId) {
        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            log.warn("[buildPlayerRegistrationInfo] 报名记录不存在, registrationId: {}", registrationId);
            return null;
        }

        // 获取球员详细信息 - 不捕获异常，让其向上传播
        PlayerDO player = playerService.getPlayerByUserId(registration.getUserId());

        // 获取好友组信息（如果有）
        FriendGroupDO friendGroup = null;
        if (registration.getFriendGroupId() != null) {
            try {
                friendGroup = friendGroupMapper.selectById(registration.getFriendGroupId());
            } catch (Exception e) {
                log.warn("[buildPlayerRegistrationInfo] 获取好友组信息失败, friendGroupId: {}",
                        registration.getFriendGroupId(), e);
                // 好友组信息获取失败不影响主流程
                friendGroup = null;
            }
        }

        return new PlayerRegistrationInfo(registration, player, friendGroup);
    }

    /**
     * 获取指定队伍的当前球员列表
     */
    private List<PlayerRegistrationInfo> getCurrentTeamPlayers(Long activityId, Long teamId) {
        List<RegistrationDO> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getTeamAssigned, teamId)
                        .in(RegistrationDO::getStatus, Arrays.asList(
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus()
                        ))
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus) // 未退款
                                .or()
                                .notIn(RegistrationDO::getRefundStatus, Arrays.asList(
                                        RefundStatusEnum.REFUNDING.getStatus(), // 排除退款中
                                        RefundStatusEnum.FULL_REFUNDED.getStatus() // 排除已退款
                                ))
                        )
        );

        return registrations.stream()
                .map(this::convertToPlayerRegistrationInfo)
                .collect(Collectors.toList());
    }

    /**
     * 🔧 优化：使用统一的状态查询服务获取所有有效的报名球员（已支付或组局成功，且非候补）
     */
    private List<PlayerRegistrationInfo> getAllValidPlayers(Long activityId) {
        List<RegistrationDO> registrations = registrationStatusQueryService.getTeamAssignableRegistrations(activityId);

        return registrations.stream()
                .map(this::convertToPlayerRegistrationInfo)
                .filter(Objects::nonNull) // 过滤转换失败的记录
                .collect(Collectors.toList());
    }

    /**
     * 🔧 优化：获取未分配队伍的球员（非候补）
     */
    private List<PlayerRegistrationInfo> getUnassignedPlayers(Long activityId) {
        // 使用统一的状态查询服务获取可分队的报名记录，然后过滤出未分配的
        List<RegistrationDO> allAssignableRegistrations = registrationStatusQueryService.getTeamAssignableRegistrations(activityId);

        // 过滤出未分配队伍的球员
        List<RegistrationDO> unassignedRegistrations = allAssignableRegistrations.stream()
                .filter(registration -> registration.getTeamAssigned() == null)
                .collect(Collectors.toList());

        return unassignedRegistrations.stream()
                .map(this::convertToPlayerRegistrationInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 🔧 优化：使用统一的状态查询服务获取候补球员列表
     */
    private List<PlayerRegistrationInfo> getWaitlistPlayers(Long activityId) {
        List<RegistrationDO> registrations = registrationStatusQueryService.getWaitlistRegistrations(activityId);

        return registrations.stream()
                .map(this::convertToPlayerRegistrationInfo)
                .filter(Objects::nonNull) // 过滤掉转换失败的记录
                .collect(Collectors.toList());
    }

    /**
     * 获取退出后剩余的球员（非候补）
     */
    private List<PlayerRegistrationInfo> getRemainingPlayersAfterWithdrawal(Long activityId, List<Long> withdrawnRegistrationIds) {
        List<RegistrationDO> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .in(RegistrationDO::getStatus, Arrays.asList(
                                RegistrationStatusEnum.PAID.getStatus(),
                                RegistrationStatusEnum.SUCCESSFUL.getStatus()
                                // 移除 WAIT_LIST 状态：候补提升后，候补用户已经变为 PAID/SUCCESSFUL 状态
                        ))
                        // 关键修复：排除候补球员，候补球员不参与重新平衡
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getIsWaitlist)
                                .or()
                                .eq(RegistrationDO::getIsWaitlist, false)
                        )
                        .and(wrapper -> wrapper
                                .isNull(RegistrationDO::getRefundStatus) // 未退款
                                .or()
                                .notIn(RegistrationDO::getRefundStatus, Arrays.asList(
                                        RefundStatusEnum.REFUNDING.getStatus(), // 排除退款中
                                        RefundStatusEnum.FULL_REFUNDED.getStatus() // 排除已退款
                                ))
                        )
                        .notIn(RegistrationDO::getId, withdrawnRegistrationIds)
        );

        return registrations.stream()
                .map(this::convertToPlayerRegistrationInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换为PlayerRegistrationInfo对象
     */
    private PlayerRegistrationInfo convertToPlayerRegistrationInfo(RegistrationDO registration) {
        try {
            // 获取球员信息
            PlayerDO player = playerService.getPlayerByUserId(registration.getUserId());

            // 获取好友组信息（如果有）
            FriendGroupDO friendGroup = null;
            if (registration.getFriendGroupId() != null) {
                try {
                    friendGroup = friendGroupMapper.selectById(registration.getFriendGroupId());
                } catch (Exception e) {
                    log.warn("[convertToPlayerRegistrationInfo] 获取好友组信息失败, friendGroupId: {}",
                            registration.getFriendGroupId(), e);
                    friendGroup = null;
                }
            }

            return new PlayerRegistrationInfo(registration, player, friendGroup);
        } catch (Exception e) {
            log.warn("[convertToPlayerRegistrationInfo] 获取球员信息失败, registrationId: {}, userId: {}",
                    registration.getId(), registration.getUserId(), e);
            // 对于批量操作，返回只包含注册信息的对象以便继续处理
            return new PlayerRegistrationInfo(registration, null, null);
        }
    }

    /**
     * 创建队伍状态快照
     */
    private TeamAssignmentResultBO.TeamSnapshotBO createTeamSnapshot(
            List<PlayerRegistrationInfo> homeTeam,
            List<PlayerRegistrationInfo> guestTeam,
            List<PlayerRegistrationInfo> unassigned) {
        return TeamAssignmentResultBO.TeamSnapshotBO.builder()
                .homeTeamPlayers(new ArrayList<>(homeTeam))
                .guestTeamPlayers(new ArrayList<>(guestTeam))
                .unassignedPlayers(new ArrayList<>(unassigned))
                .snapshotTime(LocalDateTime.now())
                .build();
    }

    /**
     * 批量更新注册记录的队伍分配
     */
    private void updateRegistrationTeamAssignment(List<RegistrationDO> updatedRegistrations) {
        if (updatedRegistrations != null && !updatedRegistrations.isEmpty()) {
            for (RegistrationDO registration : updatedRegistrations) {
                // 使用LambdaUpdateWrapper只更新team_assigned字段，避免乐观锁版本问题
                registrationMapper.update(null,
                        new LambdaUpdateWrapper<RegistrationDO>()
                                .eq(RegistrationDO::getId, registration.getId())
                                .set(RegistrationDO::getTeamAssigned, registration.getTeamAssigned())
                );
            }
            log.info("[updateRegistrationTeamAssignment] 成功更新{}条注册记录的分队信息", updatedRegistrations.size());
        }
    }

    /**
     * 保存分队历史记录
     */
    private void saveAssignmentHistory(Long activityId, TeamAssignmentResult result) {
        List<TeamAssignmentResult> history = assignmentHistory.computeIfAbsent(activityId, k -> new ArrayList<>());
        history.add(result);

        // 限制历史记录数量，避免内存泄漏
        if (history.size() > 100) {
            history.remove(0);
        }
    }

    /**
     * 发布球员分配事件
     */
    private void publishPlayerAssignedEvent(Long activityId, Long registrationId, TeamAssignmentResult result) {
        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration != null) {
            PlayerTeamAssignedEvent event = new PlayerTeamAssignedEvent(
                    registration.getId(),
                    activityId,
                    registration.getUserId(),
                    result
            );
            eventPublisher.publishEvent(event);
        }
    }

    /**
     * 发布批量分队完成事件
     */
    private void publishBatchAssignmentEvent(Long activityId, TeamAssignmentResult result) {
        // 提取球员ID列表
        List<Long> homeTeamPlayerIds = result.getHomeTeamPlayers() != null ?
                result.getHomeTeamPlayers().stream()
                        .map(player -> player.getRegistration().getId())
                        .collect(Collectors.toList()) : new ArrayList<>();

        List<Long> guestTeamPlayerIds = result.getGuestTeamPlayers() != null ?
                result.getGuestTeamPlayers().stream()
                        .map(player -> player.getRegistration().getId())
                        .collect(Collectors.toList()) : new ArrayList<>();

        List<Long> unassignedPlayerIds = result.getUnassignedPlayers() != null ?
                result.getUnassignedPlayers().stream()
                        .map(player -> player.getRegistration().getId())
                        .collect(Collectors.toList()) : new ArrayList<>();

        // 合并所有分配的球员ID
        List<Long> assignedPlayerIds = new ArrayList<>();
        assignedPlayerIds.addAll(homeTeamPlayerIds);
        assignedPlayerIds.addAll(guestTeamPlayerIds);

        BatchTeamAssignmentEvent event = new BatchTeamAssignmentEvent(
                activityId,
                assignedPlayerIds,
                homeTeamPlayerIds,
                guestTeamPlayerIds,
                unassignedPlayerIds
        );
        eventPublisher.publishEvent(event);

        log.info("[publishBatchAssignmentEvent] 发布批量分队事件, activityId: {}, 分配球员数: {}",
                activityId, assignedPlayerIds.size());
    }

    /**
     * 发布候补晋升事件
     */
    private void publishWaitlistPromotionEvent(Long activityId, Long promotedPlayerId, TeamAssignmentResult result) {
        WaitlistPromotionEvent event = new WaitlistPromotionEvent(
                activityId,
                promotedPlayerId,
                result.getPromotedPlayerTeam(),
                result.isSuccess()
        );
        eventPublisher.publishEvent(event);

        log.info("[publishWaitlistPromotionEvent] 发布候补晋升事件, activityId: {}, promotedPlayerId: {}, targetTeam: {}",
                activityId, promotedPlayerId, result.getPromotedPlayerTeam());
    }

    /**
     * 执行安全的重平衡操作，避免无限递归
     *
     * @param activityId 活动ID
     * @param maxRetries 最大重试次数
     * @return 重平衡结果
     */
    private TeamAssignmentResult performSafeRebalance(Long activityId, int maxRetries) {
        log.info("[performSafeRebalance] 开始安全重平衡 - 活动ID: {}, 最大重试次数: {}", activityId, maxRetries);

        for (int i = 0; i < maxRetries; i++) {
            try {
                // 获取当前所有有效球员重新进行分队
                List<PlayerRegistrationInfo> allPlayers = getAllValidPlayers(activityId);
                if (allPlayers.size() < 2) {
                    log.warn("[performSafeRebalance] 球员数量不足，无法进行重平衡 - 活动ID: {}, 球员数量: {}",
                            activityId, allPlayers.size());
                    return null;
                }

                ActivityDO activity = activityMapper.selectById(activityId);
                if (activity == null) {
                    log.error("[performSafeRebalance] 活动不存在 - 活动ID: {}", activityId);
                    return null;
                }

                // 执行批量分队算法（不调用assignAllPlayers避免事务问题）
                long startTime = System.currentTimeMillis();
                TeamAssignmentResult rebalanceResult = algorithmManager.assignAllPlayers(activity, allPlayers);
                long executionTime = System.currentTimeMillis() - startTime;

                if (rebalanceResult != null && rebalanceResult.isSuccess()) {
                    // 更新数据库
                    updateRegistrationTeamAssignment(rebalanceResult.getUpdatedRegistrations());

                    // 记录监控数据
                    monitorService.recordAssignmentExecution(activity, "SAFE_REBALANCE", rebalanceResult,
                            executionTime, allPlayers.size());

                    // 检查重平衡后的效果
                    boolean stillNeedsRebalance = needsReassignment(activityId);
                    if (!stillNeedsRebalance) {
                        log.info("[performSafeRebalance] 重平衡成功 - 活动ID: {}, 重试次数: {}, 最终平衡分数: {}",
                                activityId, i + 1, rebalanceResult.getBalanceScore());
                        return rebalanceResult;
                    } else {
                        log.warn("[performSafeRebalance] 重平衡后仍需调整 - 活动ID: {}, 重试次数: {}, 继续重试",
                                activityId, i + 1);
                    }
                } else {
                    log.warn("[performSafeRebalance] 重平衡算法执行失败 - 活动ID: {}, 重试次数: {}", activityId, i + 1);
                }

            } catch (Exception e) {
                log.error("[performSafeRebalance] 重平衡异常 - 活动ID: {}, 重试次数: {}", activityId, i + 1, e);
            }
        }

        log.warn("[performSafeRebalance] 重平衡达到最大重试次数 - 活动ID: {}, 重试次数: {}", activityId, maxRetries);
        return null;
    }
}
