package cn.iocoder.yudao.module.operation.service.game.impl;

import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationStatusQueryService;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.api.player.PlayerApi;
import cn.iocoder.yudao.module.operation.api.player.dto.PlayerRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 比赛球员同步服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GamePlayerSyncServiceImpl implements GamePlayerSyncService {

    @Resource
    private ActivityMapper activityMapper;
    @Resource
    private PlayerMapper playerMapper;

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;

    @Resource
    private PlayerApi playerApi;

    @Resource
    private RegistrationStatusQueryService registrationStatusQueryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAllPlayersToGame(@NotNull Long activityId, @NotNull Long gameId) {
        log.info("[syncAllPlayersToGame] 开始同步活动 {} 的所有球员到比赛 {}", activityId, gameId);

        try {
            // 1. 🔧 优化：使用统一的状态查询服务获取所有已报名成功的记录，排除候补球员
            List<RegistrationDO> successfulRegistrations = registrationStatusQueryService.getFormalRegistrations(activityId);

            if (successfulRegistrations.isEmpty()) {
                log.warn("[syncAllPlayersToGame] 活动 {} 没有已报名成功的非候补记录", activityId);
                return;
            }

            log.info("[syncAllPlayersToGame] 活动 {} 找到 {} 个非候补的已报名成功记录",
                    activityId, successfulRegistrations.size());

            // 2. 清理比赛中已存在的球员记录（防止重复）
            playerGameRelatedMapper.delete(
                new LambdaQueryWrapper<PlayerGameRelatedDO>()
                    .eq(PlayerGameRelatedDO::getGameId, gameId)
            );

            // 3. 批量创建球员比赛记录
            List<PlayerGameRelatedDO> playerGameRecords = successfulRegistrations.stream()
                .map(registration -> createPlayerGameRecord(gameId, registration))
                .collect(Collectors.toList());

            // 4. 批量插入
            for (PlayerGameRelatedDO record : playerGameRecords) {
                playerGameRelatedMapper.insert(record);
            }

            log.info("[syncAllPlayersToGame] 成功同步 {} 名球员到比赛 {}", playerGameRecords.size(), gameId);

        } catch (Exception e) {
            log.error("[syncAllPlayersToGame] 同步球员到比赛失败: activityId={}, gameId={}", activityId, gameId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPlayerToGameIfExists(@NotNull RegistrationDO registration) {
        // 1. 检查活动是否已创建比赛
        ActivityDO activity = activityMapper.selectById(registration.getActivityId());
        if (activity == null || activity.getGameId() == null) {
            log.debug("[addPlayerToGameIfExists] 活动 {} 尚未创建比赛，跳过球员同步", registration.getActivityId());
            return;
        }

        Long gameId = activity.getGameId();

        // 2. 检查球员是否已存在于比赛中
        PlayerGameRelatedDO existingRecord = playerGameRelatedMapper.selectOne(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
                .eq(PlayerGameRelatedDO::getPlayerId, registration.getPlayerId())
        );

        if (existingRecord != null) {
            log.debug("[addPlayerToGameIfExists] 球员 {} 已存在于比赛 {} 中，更新记录", registration.getPlayerId(), gameId);
            // 更新现有记录
            updatePlayerGameRecord(existingRecord, registration);
            playerGameRelatedMapper.updateById(existingRecord);
        } else {
            log.info("[addPlayerToGameIfExists] 添加球员 {} 到比赛 {}", registration.getPlayerId(), gameId);
            // 创建新记录
            PlayerGameRelatedDO newRecord = createPlayerGameRecord(gameId, registration);
            playerGameRelatedMapper.insert(newRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removePlayerFromGame(@NotNull RegistrationDO registration) {
        // 1. 检查活动是否已创建比赛
        ActivityDO activity = activityMapper.selectById(registration.getActivityId());
        if (activity == null || activity.getGameId() == null) {
            log.debug("[removePlayerFromGame] 活动 {} 尚未创建比赛，跳过球员移除", registration.getActivityId());
            return;
        }

        Long gameId = activity.getGameId();

        // 2. 从比赛中移除球员
        int deletedCount = playerGameRelatedMapper.delete(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
                .eq(PlayerGameRelatedDO::getPlayerId, registration.getPlayerId())
        );

        log.info("[removePlayerFromGame] 从比赛 {} 中移除球员 {}，删除记录数: {}",
                gameId, registration.getPlayerId(), deletedCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int repairGamePlayerConsistency(@NotNull Long activityId) {
        log.info("[repairGamePlayerConsistency] 开始修复活动 {} 的比赛球员一致性", activityId);

        // 1. 获取活动信息
        ActivityDO activity = activityMapper.selectById(activityId);
        if (activity == null || activity.getGameId() == null) {
            log.warn("[repairGamePlayerConsistency] 活动 {} 不存在或未创建比赛", activityId);
            return 0;
        }

        Long gameId = activity.getGameId();

        // 2. 🔧 优化：使用统一的状态查询服务获取报名记录中的球员
        List<RegistrationDO> successfulRegistrations = registrationStatusQueryService.getFormalRegistrations(activityId);

        Set<Long> registeredPlayerIds = successfulRegistrations.stream()
            .map(RegistrationDO::getPlayerId)
            .collect(Collectors.toSet());

        // 3. 获取比赛记录中的球员
        List<PlayerGameRelatedDO> gamePlayerRecords = playerGameRelatedMapper.selectList(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
        );

        Set<Long> gamePlayerIds = gamePlayerRecords.stream()
            .map(PlayerGameRelatedDO::getPlayerId)
            .collect(Collectors.toSet());

        int repairedCount = 0;

        // 4. 添加缺失的球员到比赛
        for (RegistrationDO registration : successfulRegistrations) {
            if (!gamePlayerIds.contains(registration.getPlayerId())) {
                PlayerGameRelatedDO newRecord = createPlayerGameRecord(gameId, registration);
                playerGameRelatedMapper.insert(newRecord);
                repairedCount++;
                log.info("[repairGamePlayerConsistency] 添加缺失球员 {} 到比赛 {}", registration.getPlayerId(), gameId);
            }
        }

        // 5. 移除多余的球员从比赛
        for (PlayerGameRelatedDO gameRecord : gamePlayerRecords) {
            if (!registeredPlayerIds.contains(gameRecord.getPlayerId())) {
                playerGameRelatedMapper.deleteById(gameRecord.getId());
                repairedCount++;
                log.info("[repairGamePlayerConsistency] 移除多余球员 {} 从比赛 {}", gameRecord.getPlayerId(), gameId);
            }
        }

        // 6. 更新现有球员的队伍分配
        Map<Long, RegistrationDO> registrationMap = successfulRegistrations.stream()
            .collect(Collectors.toMap(RegistrationDO::getPlayerId, r -> r));

        for (PlayerGameRelatedDO gameRecord : gamePlayerRecords) {
            RegistrationDO registration = registrationMap.get(gameRecord.getPlayerId());
            if (registration != null) {
                Long effectiveTeamId = getEffectiveTeamId(registration);
                if (!Objects.equals(gameRecord.getTeamId(), effectiveTeamId)) {
                    updatePlayerGameRecord(gameRecord, registration);
                    playerGameRelatedMapper.updateById(gameRecord);
                    repairedCount++;
                    log.info("[repairGamePlayerConsistency] 更新球员 {} 的队伍分配: {} -> {}",
                            gameRecord.getPlayerId(), gameRecord.getTeamId(), effectiveTeamId);
                }
            }
        }

        log.info("[repairGamePlayerConsistency] 完成修复活动 {} 的比赛球员一致性，修复记录数: {}", activityId, repairedCount);
        return repairedCount;
    }

    @Override
    public List<PlayerGameRelatedDO> getGamePlayers(@NotNull Long gameId) {
        return playerGameRelatedMapper.selectList(
            new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
                .orderByAsc(PlayerGameRelatedDO::getTeamId)
                .orderByAsc(PlayerGameRelatedDO::getNumber)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdatePlayerTeamAssignment(@NotNull List<RegistrationDO> registrations) {
        if (registrations.isEmpty()) {
            return;
        }

        // 按活动分组
        Map<Long, List<RegistrationDO>> activityGroups = registrations.stream()
            .collect(Collectors.groupingBy(RegistrationDO::getActivityId));

        for (Map.Entry<Long, List<RegistrationDO>> entry : activityGroups.entrySet()) {
            Long activityId = entry.getKey();
            List<RegistrationDO> activityRegistrations = entry.getValue();

            // 获取活动的比赛ID
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null || activity.getGameId() == null) {
                continue;
            }

            Long gameId = activity.getGameId();

            // 批量更新球员队伍分配
            for (RegistrationDO registration : activityRegistrations) {
                PlayerGameRelatedDO gameRecord = playerGameRelatedMapper.selectOne(
                    new LambdaQueryWrapper<PlayerGameRelatedDO>()
                        .eq(PlayerGameRelatedDO::getGameId, gameId)
                        .eq(PlayerGameRelatedDO::getPlayerId, registration.getPlayerId())
                );

                if (gameRecord != null) {
                    Long effectiveTeamId = getEffectiveTeamId(registration);
                    if (!Objects.equals(gameRecord.getTeamId(), effectiveTeamId)) {
                        gameRecord.setTeamId(effectiveTeamId);
                        playerGameRelatedMapper.updateById(gameRecord);
                        log.info("[batchUpdatePlayerTeamAssignment] 更新球员 {} 的队伍分配: {}",
                                registration.getPlayerId(), effectiveTeamId);
                    }
                }
            }
        }
    }

    /**
     * 创建球员比赛记录
     */
    private PlayerGameRelatedDO createPlayerGameRecord(Long gameId, RegistrationDO registration) {
        // 获取球员详细信息
        PlayerDO player = null;
        try {
            player = playerMapper.getPlayerByMemberId(registration.getUserId());
        } catch (Exception e) {
            log.warn("[createPlayerGameRecord] 获取球员 {} 信息失败: {}", registration.getPlayerId(), e.getMessage());
        }

        PlayerGameRelatedDO record = new PlayerGameRelatedDO();
        record.setGameId(gameId);
        record.setPlayerId(registration.getPlayerId());
        record.setUserId(registration.getUserId());
        if (player != null) {
            record.setNumber(player.getNumber());
            record.setPosition(player.getPosition());
            record.setRatings(player.getRatings());
        }

        // 根据活动类型选择正确的球队字段
        Long teamId = getEffectiveTeamId(registration);
        record.setTeamId(teamId);
        record.setAttend(1); // 默认出席
        return record;
    }

    /**
     * 更新球员比赛记录
     */
    private void updatePlayerGameRecord(PlayerGameRelatedDO gameRecord, RegistrationDO registration) {
        // 根据活动类型选择正确的球队字段
        Long teamId = getEffectiveTeamId(registration);
        gameRecord.setTeamId(teamId);
        gameRecord.setUserId(registration.getUserId());

        // 更新球员最新信息
        try {
            PlayerRespDTO player = playerApi.getPlayerByUserId(registration.getUserId());
            if (player != null) {
                // 注意：这里需要根据实际的PlayerRespDTO字段进行调整
                // gameRecord.setNumber(player.getNumber());
                // gameRecord.setPosition(player.getPosition());
                gameRecord.setRatings(player.getRatings());
            }
        } catch (Exception e) {
            log.warn("[updatePlayerGameRecord] 获取球员 {} 最新信息失败: {}", registration.getPlayerId(), e.getMessage());
        }
    }

    /**
     * 根据活动类型获取有效的球队ID
     *
     * @param registration 报名记录
     * @return 有效的球队ID
     */
    private Long getEffectiveTeamId(RegistrationDO registration) {
        // 根据活动类型选择正确的球队字段
        if (registration.getActivityType() == null) {
            log.warn("[getEffectiveTeamId] 报名记录 {} 的活动类型为空", registration.getId());
            return registration.getTeamAssigned(); // 默认使用team_assigned
        }

        ActivityTypeEnum activityType = ActivityTypeEnum.getByType(registration.getActivityType());
        if (activityType == null) {
            log.warn("[getEffectiveTeamId] 未知的活动类型: {}", registration.getActivityType());
            return registration.getTeamAssigned(); // 默认使用team_assigned
        }

        switch (activityType) {
            case RANKING: // 排位赛 - 使用系统分配的team_assigned
                return registration.getTeamAssigned();
            case FRIENDLY: // 友谊赛 - 使用用户选择的team_id
            case LEAGUE: // 联赛 - 使用用户选择的team_id
                return registration.getTeamId();
            default:
                log.warn("[getEffectiveTeamId] 未处理的活动类型: {}", activityType);
                return registration.getTeamAssigned(); // 默认使用team_assigned
        }
    }
}