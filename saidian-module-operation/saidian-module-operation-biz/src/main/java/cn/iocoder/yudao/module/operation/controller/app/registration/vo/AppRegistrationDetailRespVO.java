package cn.iocoder.yudao.module.operation.controller.app.registration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "App - 报名详情 Response VO")
@Data
@ToString(callSuper = true)
public class AppRegistrationDetailRespVO {

    @Schema(description = "报名ID", required = true, example = "1024")
    private Long id;

    @Schema(description = "用户ID", required = true, example = "1024")
    private Long userId;

    @Schema(description = "球员ID", required = true, example = "1024")
    private Long playerId;

    @Schema(description = "活动ID", required = true, example = "2048")
    private Long activityId;

    @Schema(description = "活动名称", required = true, example = "周末排位赛")
    private String activityName;

    @Schema(description = "活动类型: 1-排位赛, 2-友谊赛, 3-联赛", required = true, example = "1")
    private Integer activityType;

    @Schema(description = "报名状态: 1-待支付, 2-已支付, 3-报名成功, 4-已取消, 5-已完成", required = true, example = "2")
    private Integer status;

    @Schema(description = "报名时间", required = true)
    private LocalDateTime registrationTime;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "支付状态: 1-未支付, 2-已支付", required = true, example = "2")
    private Integer paymentStatus;

    @Schema(description = "支付订单ID", required = true, example = "3072")
    private Long payOrderId;

    @Schema(description = "应付金额（分）", required = true, example = "1000")
    private Integer shouldPayPrice;

    @Schema(description = "实付金额（分）", example = "800")
    private Integer actualPayPrice;

    @Schema(description = "退款状态: 0-未退款, 1-部分退款, 2-全额退款, 3-退款中", required = true, example = "0")
    private Integer refundStatus;

    @Schema(description = "退款总金额（分）", required = true, example = "0")
    private Integer totalRefundPrice;

    @Schema(description = "取消原因", example = "活动人数不足，无法组局")
    private String cancelReason;

    @Schema(description = "活动开始时间", required = true)
    private LocalDateTime activityStartTime;

    @Schema(description = "活动结束时间", required = true)
    private LocalDateTime activityEndTime;

    @Schema(description = "活动地点", example = "XX篮球馆")
    private String activityLocation;

    @Schema(description = "球队ID (友谊赛/联赛)", example = "3072")
    private Long teamId;

    @Schema(description = "球队名称 (友谊赛/联赛)", example = "猛虎队")
    private String teamName;

    @Schema(description = "好友组ID (排位赛好友组队)", example = "4096")
    private Long friendGroupId;

    @Schema(description = "是否为好友组队发起人", example = "true")
    private Boolean isGroupLeader;

    @Schema(description = "支付方式 (友谊赛): 1-球队整体支付, 2-球员个人支付AA", example = "2")
    private Integer paymentType;

    @Schema(description = "排位赛分配队伍: 1-主队, 2-客队", example = "1")
    private Integer teamAssigned;

} 