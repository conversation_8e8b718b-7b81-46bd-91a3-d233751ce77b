package cn.iocoder.yudao.module.operation.convert.registration;

import cn.iocoder.yudao.module.operation.controller.app.activity.vo.AppActivityRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationDetailRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationPageItemRespVO;
import cn.iocoder.yudao.module.operation.api.registration.dto.AppRegistrationPageItemRespDTO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.friendgroup.FriendGroupDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 活动报名 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RegistrationConvert {

    RegistrationConvert INSTANCE = Mappers.getMapper(RegistrationConvert.class);

    static AppActivityRespVO.PlayerSignupInfoVO buildPlayerInfo(RegistrationDO reg, PlayerService playerService) {
        AppActivityRespVO.PlayerSignupInfoVO playerInfo = new AppActivityRespVO.PlayerSignupInfoVO();
        playerInfo.setId(reg.getPlayerId());
        playerInfo.setUserId(reg.getUserId()); // 添加用户ID，用于识别房主

        if (reg.getPlayerId() != null) {
            PlayerDO player = playerService.getPlayer(reg.getPlayerId());
            if (player != null) {
                playerInfo.setNickname(player.getName());
                playerInfo.setAvatar(player.getAvatar());
                playerInfo.setPosition(player.getPosition());
                playerInfo.setScore(player.getRatings());
            }
        }

        return playerInfo;
    }

    /**
     * 将 App 创建请求 VO 转换为 DO
     * <p>
     * 注意：userId, playerId, activityType, status, paymentStatus, shouldPayPrice, registrationTime
     * 等字段需要在 ServiceImpl 中手动设置。
     *
     * @param bean AppRegistrationCreateReqVO
     * @return RegistrationDO
     */
    @Mappings({
            @Mapping(target = "id", ignore = true), // id 在插入时生成
            @Mapping(target = "userId", ignore = true), // userId 由 Service 层设置
            @Mapping(target = "playerId", ignore = true), // playerId 由 Service 层设置
            @Mapping(target = "activityType", ignore = true), // activityType 由 Service 层设置
            @Mapping(target = "registrationTime", ignore = true), // 由 Service 层设置
            @Mapping(target = "teamId", source = "teamId"), // 假设 VO 中有 teamId，用于友谊赛/联赛
            @Mapping(target = "friendGroupId", ignore = true), // 好友组队时由系统创建或关联
            @Mapping(target = "isGroupLeader", source = "isGroupLeader"), // 从 VO 获取
            @Mapping(target = "paymentType", source = "paymentType"), // 假设 VO 中有 paymentType，用于友谊赛
            @Mapping(target = "status", ignore = true), // 由 Service 层设置
            @Mapping(target = "paymentStatus", ignore = true), // 由 Service 层设置
            @Mapping(target = "payOrderId", ignore = true), // 由 Service 层设置
            @Mapping(target = "payTime", ignore = true),
            @Mapping(target = "shouldPayPrice", ignore = true), // 由 Service 层计算设置
            @Mapping(target = "actualPayPrice", ignore = true), // 由 Service 层设置
            @Mapping(target = "couponId", ignore = true),
            @Mapping(target = "couponDiscountPrice", ignore = true),
            @Mapping(target = "refundStatus", ignore = true), // 由 Service 层设置
            @Mapping(target = "totalRefundPrice", ignore = true), // 由 Service 层设置
            @Mapping(target = "cancelReason", ignore = true),
            @Mapping(target = "teamAssigned", ignore = true)
    })
    RegistrationDO convert(AppRegistrationCreateReqVO bean);

    /**
     * 转换报名记录为列表项VO
     *
     * @param registration 报名记录
     * @param activity     关联的活动信息
     * @return 列表项VO
     */
    default AppRegistrationPageItemRespVO convert(RegistrationDO registration, ActivityDO activity) {
        if (registration == null) {
            return null;
        }

        AppRegistrationPageItemRespVO vo = new AppRegistrationPageItemRespVO();
        vo.setId(registration.getId());
        vo.setActivityId(registration.getActivityId());
        vo.setUserId(registration.getUserId());
        vo.setPlayerId(registration.getPlayerId());
        vo.setStatus(registration.getStatus());
        vo.setRegistrationTime(registration.getRegistrationTime());
        vo.setPaymentStatus(registration.getPaymentStatus());

        // 添加活动信息
        if (activity != null) {
            vo.setActivityName(activity.getName());
            vo.setActivityStartTime(activity.getStartTime());
            vo.setActivityLocation(activity.getLocation());
            vo.setActivityCover(activity.getPicUrl());
            vo.setActivityType(activity.getType());
        }

        return vo;
    }

    /**
     * 转换为详情VO
     *
     * @param registration 报名记录
     * @param activity     活动信息
     * @param friendGroup  好友组信息（如果适用）
     * @return 详情VO
     */
    default AppRegistrationDetailRespVO convertDetail(RegistrationDO registration, ActivityDO activity, FriendGroupDO friendGroup) {
        if (registration == null) {
            return null;
        }

        AppRegistrationDetailRespVO vo = new AppRegistrationDetailRespVO();
        vo.setId(registration.getId());
        vo.setActivityId(registration.getActivityId());
        vo.setUserId(registration.getUserId());
        vo.setPlayerId(registration.getPlayerId());
        vo.setStatus(registration.getStatus());
        vo.setRegistrationTime(registration.getRegistrationTime());
        vo.setPaymentStatus(registration.getPaymentStatus());
        vo.setPayTime(registration.getPayTime());
        vo.setShouldPayPrice(registration.getShouldPayPrice());
        vo.setActualPayPrice(registration.getActualPayPrice());

        // 设置退款信息
        vo.setRefundStatus(registration.getRefundStatus());
        vo.setTotalRefundPrice(registration.getTotalRefundPrice());

        // 活动信息
        if (activity != null) {
            vo.setActivityName(activity.getName());
            vo.setActivityStartTime(activity.getStartTime());
            vo.setActivityEndTime(activity.getEndTime());
            vo.setActivityLocation(activity.getLocation());
            vo.setActivityType(activity.getType());
        }

        // 好友组信息
        if (friendGroup != null) {
            vo.setFriendGroupId(friendGroup.getId());
            vo.setIsGroupLeader(registration.getIsGroupLeader());
            // 可以添加更多好友组相关信息
        }

        return vo;
    }

    /**
     * 转换为API DTO
     *
     * @param registration 报名记录
     * @param activity     活动信息
     * @return API DTO
     */
    default AppRegistrationPageItemRespDTO convertToPageItemRespDTO(RegistrationDO registration, ActivityDO activity) {
        if (registration == null) {
            return null;
        }

        AppRegistrationPageItemRespDTO dto = new AppRegistrationPageItemRespDTO();
        dto.setId(registration.getId());
        dto.setUserId(registration.getUserId());
        dto.setActivityId(registration.getActivityId());
        dto.setRegistrationTime(registration.getRegistrationTime());
        dto.setStatus(registration.getStatus());
        dto.setPaymentStatus(registration.getPaymentStatus());
        dto.setPayTime(registration.getPayTime());
        dto.setShouldPayPrice(registration.getShouldPayPrice());
        dto.setActualPayPrice(registration.getActualPayPrice());
        dto.setPayOrderId(registration.getPayOrderId());

        // 活动信息
        if (activity != null) {
            dto.setActivityName(activity.getName());
            dto.setActivityPicUrl(activity.getPicUrl());
            dto.setActivityType(activity.getType());
            dto.setActivityStartTime(activity.getStartTime());
            dto.setLocation(activity.getLocation());
        }

        return dto;
    }

}