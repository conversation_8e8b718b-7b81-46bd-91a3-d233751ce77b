package cn.iocoder.yudao.module.operation.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 🔧 增强版报名超时处理定时任务
 *
 * 用于处理超时未支付的报名记录：
 * 1. 查询支付订单已关闭但报名状态仍为待支付的记录
 * 2. 将这些报名记录状态更新为已取消，支付状态更新为关闭
 * 3. 如果是候补球员，从候补队列中移除
 * 4. 如果有候补球员等待，自动提升候补
 * 5. 更新活动的报名统计信息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RegistrationTimeoutJob implements JobHandler {

    @Resource
    private RegistrationService registrationService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        int count = registrationService.cancelTimeoutRegistrations();
        return String.format("处理超时未支付报名记录数量：%d", count);
    }
}