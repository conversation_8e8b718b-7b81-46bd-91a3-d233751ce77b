package cn.iocoder.yudao.module.operation.service.daily;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.operation.controller.admin.daily.vo.DailyActivityCreateByTemplateReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.daily.vo.DailyActivityTemplatePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.daily.vo.DailyActivityTemplateSaveReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.DailyActivityTemplateDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 日常活动模板 Service 接口
 *
 * <AUTHOR>
 */
public interface DailyActivityTemplateService {

    /**
     * 创建日常活动模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDailyActivityTemplate(@Valid DailyActivityTemplateSaveReqVO createReqVO);

    /**
     * 更新日常活动模板
     *
     * @param updateReqVO 更新信息
     */
    void updateDailyActivityTemplate(@Valid DailyActivityTemplateSaveReqVO updateReqVO);

    /**
     * 删除日常活动模板
     *
     * @param id 编号
     */
    void deleteDailyActivityTemplate(Long id);

    /**
     * 获得日常活动模板
     *
     * @param id 编号
     * @return 日常活动模板
     */
    DailyActivityTemplateDO getDailyActivityTemplate(Long id);

    /**
     * 获得日常活动模板分页
     *
     * @param pageReqVO 分页查询
     * @return 日常活动模板分页
     */
    PageResult<DailyActivityTemplateDO> getDailyActivityTemplatePage(DailyActivityTemplatePageReqVO pageReqVO);

    Long createDailyActivityByTemplate(DailyActivityCreateByTemplateReqVO createReqVO);

    List<DailyActivityTemplateDO> getAllDailyActivityTemplate();
}