package cn.iocoder.yudao.module.operation.controller.admin.message;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.message.vo.MessagePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.message.vo.MessageRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.message.vo.MessageSaveReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.message.MessageDO;
import cn.iocoder.yudao.module.operation.service.message.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 消息通知")
@RestController
@RequestMapping("/operation/message")
@Validated
public class MessageController {

    @Resource
    private MessageService messageService;

    @PostMapping("/create")
    @Operation(summary = "创建消息通知")
    @PreAuthorize("@ss.hasPermission('operation:message:create')")
    public CommonResult<Long> createMessage(@Valid @RequestBody MessageSaveReqVO createReqVO) {
        return success(messageService.createMessage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新消息通知")
    @PreAuthorize("@ss.hasPermission('operation:message:update')")
    public CommonResult<Boolean> updateMessage(@Valid @RequestBody MessageSaveReqVO updateReqVO) {
        messageService.updateMessage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除消息通知")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('operation:message:delete')")
    public CommonResult<Boolean> deleteMessage(@RequestParam("id") Long id) {
        messageService.deleteMessage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得消息通知")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:message:query')")
    public CommonResult<MessageRespVO> getMessage(@RequestParam("id") Long id) {
        MessageDO message = messageService.getMessage(id);
        return success(BeanUtils.toBean(message, MessageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得消息通知分页")
    @PreAuthorize("@ss.hasPermission('operation:message:query')")
    public CommonResult<PageResult<MessageRespVO>> getMessagePage(@Valid MessagePageReqVO pageReqVO) {
        PageResult<MessageDO> pageResult = messageService.getMessagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MessageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出消息通知 Excel")
    @PreAuthorize("@ss.hasPermission('operation:message:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMessageExcel(@Valid MessagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MessageDO> list = messageService.getMessagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "消息通知.xls", "数据", MessageRespVO.class,
                        BeanUtils.toBean(list, MessageRespVO.class));
    }

}