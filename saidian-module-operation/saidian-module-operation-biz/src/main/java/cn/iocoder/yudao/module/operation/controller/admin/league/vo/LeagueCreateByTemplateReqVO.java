package cn.iocoder.yudao.module.operation.controller.admin.league.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 模板创建联赛Request VO")
@Data
public class LeagueCreateByTemplateReqVO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17545")
    private Long id;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "报名截止时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报名截止时间不能为空")
    private LocalDateTime registrationEndTime;
}
