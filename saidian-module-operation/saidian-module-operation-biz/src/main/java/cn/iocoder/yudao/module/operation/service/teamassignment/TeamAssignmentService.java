package cn.iocoder.yudao.module.operation.service.teamassignment;

import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分队服务接口
 * 
 * 提供完整的分队功能，包括实时分队、批量分队、重新平衡、候补晋升等。
 * 支持多种分队算法和平衡策略，确保队伍分配的公平性和合理性。
 *
 * <AUTHOR>
 */
public interface TeamAssignmentService {

    /**
     * 实时分队 - 用户支付成功后立即分配队伍
     * 
     * 当用户支付成功后，系统会立即调用此方法为新球员分配合适的队伍。
     * 分配时会考虑当前队伍平衡度、球员能力值、位置分布等因素。
     *
     * @param activityId 活动ID
     * @param registrationId 新加入的报名记录ID
     * @return 分队结果，包含分配的队伍信息和平衡度评分
     */
    TeamAssignmentResult assignPlayerToTeam(@NotNull Long activityId, @NotNull Long registrationId);

    /**
     * 批量分队 - 对所有已报名球员进行重新分队
     * 
     * 通常在活动开始前或需要重新平衡队伍时调用。
     * 会对所有已支付的球员进行全局最优分配。
     * 
     * @param activityId 活动ID
     * @return 分队结果，包含主队和客队分配情况
     */
    TeamAssignmentResult assignAllPlayers(@NotNull Long activityId);

    /**
     * 重新平衡队伍 - 球员退出后调整队伍平衡
     * 
     * 当有球员退出活动时，系统需要重新调整队伍配置以保持平衡。
     * 可能会从候补名单中提升球员或重新分配现有球员。
     * 
     * @param activityId 活动ID
     * @param withdrawnRegistrationIds 退出的报名ID列表
     * @return 重新平衡后的分队结果
     */
    TeamAssignmentResult rebalanceAfterWithdrawal(@NotNull Long activityId, @NotNull List<Long> withdrawnRegistrationIds);

    /**
     * 候补晋升 - 从候补名单中选择球员填补空缺
     * 
     * 当正式球员退出且队伍需要补充人员时，从候补名单中选择最合适的球员。
     * 选择标准包括球员能力值、队伍平衡度改善程度等。
     * 
     * @param activityId 活动ID
     * @param vacantSlots 空缺位置数量
     * @return 候补晋升结果
     */
    TeamAssignmentResult promoteFromWaitlist(@NotNull Long activityId, int vacantSlots);

    /**
     * 评估队伍平衡度
     * 
     * 对当前的队伍分配进行全面评估，提供详细的平衡度报告。
     * 包括能力值平衡、位置分布、好友组处理等各维度评分。
     * 
     * @param activityId 活动ID
     * @return 队伍平衡度评估结果
     */
    TeamAssignmentResult.BalanceEvaluation evaluateTeamBalance(@NotNull Long activityId);

    /**
     * 获取活动的当前分队状况
     * 
     * 查询指定活动的当前分队情况，包括主队、客队球员列表和统计信息。
     * 
     * @param activityId 活动ID
     * @return 当前分队状况
     */
    TeamAssignmentResult getCurrentTeamAssignment(@NotNull Long activityId);

    /**
     * 手动调整队伍分配
     * 
     * 允许管理员手动调整球员的队伍分配，用于特殊情况处理。
     * 调整后会重新计算队伍平衡度。
     * 
     * @param activityId 活动ID
     * @param registrationId 要调整的报名记录ID
     * @param targetTeam 目标队伍（1-主队，2-客队）
     * @return 调整后的分队结果
     */
    TeamAssignmentResult manualAssignment(@NotNull Long activityId, @NotNull Long registrationId, @NotNull Integer targetTeam);

    /**
     * 检查是否需要重新分队
     * 
     * 检查当前队伍配置是否需要重新调整，考虑因素包括：
     * - 队伍平衡度是否过低
     * - 是否有新的好友组需要处理
     * - 球员能力分布是否合理
     * 
     * @param activityId 活动ID
     * @return 是否需要重新分队
     */
    boolean needsReassignment(@NotNull Long activityId);

    /**
     * 获取活动的所有分队历史记录
     * 
     * 查询指定活动的所有分队操作历史，用于追踪和审计。
     * 
     * @param activityId 活动ID
     * @return 分队历史记录列表
     */
    List<TeamAssignmentResult> getAssignmentHistory(@NotNull Long activityId);
} 