package cn.iocoder.yudao.module.operation.service.waitlist.impl;

import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.registration.event.RegistrationPromotedEvent;
import cn.iocoder.yudao.module.operation.service.waitlist.WaitlistService;
import cn.iocoder.yudao.module.operation.service.registration.RegistrationStatusQueryService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 候补队列服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WaitlistServiceImpl implements WaitlistService {

    @Resource
    private RegistrationMapper registrationMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private RegistrationStatusQueryService registrationStatusQueryService;

    @Override
    public boolean isActivityFull(ActivityDO activity) {
        // 获取活动的实际容量限制
        Integer maxCapacity = determineMaxCapacity(activity);
        if (maxCapacity == null || maxCapacity <= 0) {
            // 无限制或配置错误，默认不满
            return false;
        }

        // 查询当前有效的报名数量（排除退款中和已取消的）
        Long currentCount = getActiveRegistrationCount(activity);

        log.info("[isActivityFull] 活动 {} 当前报名人数: {}, 最大容量: {}",
                activity.getId(), currentCount, maxCapacity);

        return currentCount >= maxCapacity;
    }

    /**
     * 🔧 优化：使用统一的状态查询服务获取活动的有效报名数量（排除候补和退款中的）
     */
    private Long getActiveRegistrationCount(ActivityDO activity) {
        return registrationStatusQueryService.countActiveRegistrations(activity.getId());
    }

    /**
     * 根据活动类型确定最大容量
     */
    private Integer determineMaxCapacity(ActivityDO activity) {
        // 根据活动类型返回相应的容量限制
        switch (activity.getType()) {
            case 1: // 排位赛
                return activity.getMaxPlayersPerGame();
            case 2: // 友谊赛
                // 友谊赛的容量可能是每队最大人数 * 队伍数
                if (activity.getMaxPlayersPerTeamFriendly() != null && activity.getMinTeamsFriendly() != null) {
                    return activity.getMaxPlayersPerTeamFriendly() * activity.getMinTeamsFriendly();
                }
                return null;
            case 3: // 联赛
                // 联赛可能限制球队数 * 每队人数
                if (activity.getMinTeamsLeague() != null && activity.getMinPlayersPerTeamLeague() != null) {
                    return activity.getMinTeamsLeague() * activity.getMinPlayersPerTeamLeague();
                }
                return null;
            default:
                return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addToWaitlist(RegistrationDO registration, ActivityDO activity) {
        // 🔧 优化：获取当前候补队列最大位置
        RegistrationDO lastWaitlistRegistration = registrationMapper.selectOne(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activity.getId())
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .orderByDesc(RegistrationDO::getWaitlistPosition)
                        .last("LIMIT 1")
        );
        Integer maxPosition = lastWaitlistRegistration != null ? lastWaitlistRegistration.getWaitlistPosition() : null;

        // 如果没有候补记录，从1开始
        int newPosition = maxPosition == null ? 1 : maxPosition + 1;

        // 更新报名记录为候补状态
        registration.setIsWaitlist(true);
        registration.setWaitlistPosition(newPosition);
        registrationMapper.updateById(registration);

        log.info("[addToWaitlist] 报名 {} 加入活动 {} 候补队列，位置: {}",
                registration.getId(), activity.getId(), newPosition);

        return newPosition;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RegistrationDO> promoteFromWaitlist(ActivityDO activity) {
        // 获取活动的实际容量限制
        Integer maxCapacity = determineMaxCapacity(activity);
        if (maxCapacity == null || maxCapacity <= 0) {
            log.warn("[promoteFromWaitlist] 活动 {} 无有效容量限制", activity.getId());
            return new ArrayList<>();
        }

        // 获取当前有效的非候补报名数量（排除退款中和已取消的）
        Long currentCount = getActiveRegistrationCount(activity);

        // 计算可以提升的名额
        int availableSlots = maxCapacity - currentCount.intValue();
        if (availableSlots <= 0) {
            log.info("[promoteFromWaitlist] 活动 {} 无可用名额，不提升候补", activity.getId());
            return new ArrayList<>();
        }

        // 🔧 优化：使用统一的状态查询服务获取可以提升的候补名单
        List<RegistrationDO> waitlistToPromote = registrationStatusQueryService.getPromotableWaitlistRegistrations(
                activity.getId(), availableSlots);

        // 提升候补名单
        for (RegistrationDO registration : waitlistToPromote) {
            registration.setIsWaitlist(false);
            registration.setWaitlistPosition(null);
            registrationMapper.updateById(registration);

            // 发布候补提升事件
            eventPublisher.publishEvent(new RegistrationPromotedEvent(this, registration.getId()));

            log.info("[promoteFromWaitlist] 候补报名 {} 提升为正式报名", registration.getId());
        }

        // 如果有提升，需要重排候补队列
        if (!waitlistToPromote.isEmpty()) {
            reorderWaitlist(activity.getId(), 1);
        }

        return waitlistToPromote;
    }

    @Override
    public List<RegistrationDO> getWaitlistForActivity(Long activityId) {
        // 🔧 优化：使用统一的状态查询服务获取候补列表
        return registrationStatusQueryService.getWaitlistRegistrations(activityId);
    }

    @Override
    public int getWaitlistPosition(Long registrationId) {
        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null || !Boolean.TRUE.equals(registration.getIsWaitlist())) {
            return -1;
        }
        return registration.getWaitlistPosition();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reorderWaitlist(Long activityId, int startPosition) {
        // 获取需要重排序的候补记录
        List<RegistrationDO> waitlist = registrationMapper.selectList(
                new LambdaQueryWrapper<RegistrationDO>()
                        .eq(RegistrationDO::getActivityId, activityId)
                        .eq(RegistrationDO::getIsWaitlist, true)
                        .ge(RegistrationDO::getWaitlistPosition, startPosition)
                        .orderByAsc(RegistrationDO::getWaitlistPosition)
        );

        if (waitlist.isEmpty()) {
            log.info("[reorderWaitlist] 活动 {} 无需重排序候补队列", activityId);
            return;
        }

        // 从指定位置开始重新编号
        int position = startPosition;
        for (RegistrationDO registration : waitlist) {
            registration.setWaitlistPosition(position++);
            registrationMapper.updateById(registration);
        }

        log.info("[reorderWaitlist] 活动 {} 候补队列已重排序，共 {} 条记录",
                activityId, waitlist.size());
    }
}