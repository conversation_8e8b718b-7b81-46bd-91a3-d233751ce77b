package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class BasicStatistics {
    @Schema(description = "比赛id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25889")
    @NotNull(message = "比赛id不能为空")
    private Long gameId;

    @Schema(description = "上场时间（分钟）")
    private Integer playingTime;

    @Schema(description = "得分")
    private Integer points;

    @Schema(description = "助攻数")
    private Integer assists;

    @Schema(description = "抢断数")
    private Integer steals;

    @Schema(description = "盖帽数")
    private Integer blocks;

    @Schema(description = "失误数")
    private Integer turnovers;

    @Schema(description = "犯规数")
    private Integer fouls;

    @Schema(description = "两分出手")
    private Integer twoPointAttempts;

    @Schema(description = "两分命中")
    private Integer twoPointMakes;

    @Schema(description = "三分出手")
    private Integer threePointAttempts;

    @Schema(description = "三分命中")
    private Integer threePointMakes;

    @Schema(description = "罚球出手")
    private Integer freeThrowAttempts;

    @Schema(description = "罚球命中")
    private Integer freeThrowMakes;

    @Schema(description = "前场篮板球")
    private Integer offensiveRebounds;

    @Schema(description = "后场篮板球")
    private Integer defensiveRebounds;

    @Schema(description = "效率值")
    private Integer efficiency;

    @Schema(description = "正负值")
    private Integer plusMinus;

    @Schema(description = "投篮命中率")
    private String tsRate;


}
