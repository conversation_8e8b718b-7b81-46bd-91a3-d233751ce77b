package cn.iocoder.yudao.module.operation.service.state.impl;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.state.RegistrationState;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 候补状态处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WaitListState implements RegistrationState {

    @Resource
    private RegistrationMapper registrationMapper;
    
    @Resource
    private ActivityRefundService activityRefundService;

    @Override
    public void handlePaySuccess(RegistrationDO registration, PayOrderNotifyReqDTO notifyReqDTO) {
        log.warn("[handlePaySuccess][WaitListState] registrationId({}) 状态已是候补中，忽略支付回调。", registration.getId());
        // 候补状态下，支付已完成，忽略后续的支付回调
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(RegistrationDO registration, Long operatorId, String cancelReason) {
        log.info("[cancel][WaitListState] registrationId({}) 候补状态下取消，开始处理退赛申请，原因: {}", registration.getId(), cancelReason);
        
        String finalCancelReason = StringUtils.hasText(cancelReason) ? cancelReason : "用户主动退赛";
        
        // 候补状态下允许退赛，通常是全额退款
        try {
            activityRefundService.processRefund(registration.getId(), RefundScenario.USER_CANCEL, operatorId, finalCancelReason);
            log.info("[cancel][WaitListState] registrationId({}) 候补状态下发起退款成功，等待退款完成后更新状态", registration.getId());
            
            // 🔧 修复：事件发布已移到退款服务内部处理，确保事务一致性
            // log.info("[cancel][WaitListState] registrationId({}) 退款处理成功，相关事件将在退款完成后发布", registration.getId());
            
        } catch (Exception e) {
            log.error("[cancel][WaitListState] registrationId({}) 候补状态下发起退款失败", registration.getId(), e);
            throw e;
        }
    }

    @Override
    public void applyRefund(RegistrationDO registration, String reason) {
        log.info("[applyRefund][WaitListState] registrationId({}) 候补状态下申请退款，原因: {}", registration.getId(), reason);
        
        // 候补状态下允许申请退款
        cancel(registration, registration.getUserId(), reason);
    }

    @Override
    public void complete(RegistrationDO registration) {
        log.info("[complete][WaitListState] registrationId({}) 活动完成", registration.getId());
        
        // 更新为完成状态
        RegistrationDO updateObj = new RegistrationDO();
        updateObj.setId(registration.getId());
        updateObj.setStatus(RegistrationStatusEnum.COMPLETED.getStatus());
        
        int updateCount = registrationMapper.updateById(updateObj);
        if (updateCount == 0) {
            log.error("[complete][WaitListState] registrationId({}) 更新报名记录状态为完成失败", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_STATUS_UPDATE_FAILED);
        }
        
        log.info("[complete][WaitListState] registrationId({}) 更新为完成状态成功", registration.getId());
    }
} 