package cn.iocoder.yudao.module.operation.processor.impl;

import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationLogTypeEnum;
import cn.iocoder.yudao.module.operation.processor.AbstractRefundProcessor;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculator;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculatorFactory;
import cn.iocoder.yudao.module.promotion.api.coupon.CouponApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 差额退款处理器 - 处理AA支付场景下的差额退款
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DifferenceRefundProcessor extends AbstractRefundProcessor {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private RefundCalculatorFactory refundCalculatorFactory;

    @Override
    protected void doValidateRefund(RegistrationDO registration, Long operatorId) {
        // 差额退款通常由系统发起，不需要额外的校验
        // 通常在组局成功后，系统根据实际人数计算差额并发起退款
        log.info("[doValidateRefund] 差额退款校验通过，报名ID: {}", registration.getId());
    }

    @Override
    protected ActivityDO getActivity(Long activityId) {
        return activityMapper.selectById(activityId);
    }

    @Override
    protected Integer calculateAmount(ActivityDO activity, RegistrationDO registration) {
        // 获取对应的退款计算器
        RefundCalculator calculator = refundCalculatorFactory.getCalculator(activity.getType());
        
        if (calculator == null) {
            log.error("[calculateAmount] 未找到退款计算器: activityType={}, activityId={}", 
                    activity.getType(), activity.getId());
            return 0;
        }
        
        // 调用退款计算器计算差额退款金额
        Integer refundAmount = calculator.calculateDifferenceRefundAmount(activity, registration);
        
        log.info("[calculateAmount] 差额退款计算: activityId={}, registrationId={}, refundAmount={}", 
                activity.getId(), registration.getId(), refundAmount);
        
        return refundAmount != null ? refundAmount : 0;
    }
    
    @Override
    protected void afterRefund(RegistrationDO registration, ActivityDO activity, Integer refundAmount) {
        // 差额退款不需要退还优惠券，因为用户报名仍然有效
        log.info("[afterRefund] 差额退款后续处理: registrationId={}, activityId={}, refundAmount={}", 
                registration.getId(), activity.getId(), refundAmount);
    }

    @Override
    protected String getRefundReason() {
        return "活动报名差额退款";
    }

    @Override
    protected RegistrationLogTypeEnum getLogType() {
        return RegistrationLogTypeEnum.DIFFERENCE_REFUND;
    }

    @Override
    public RefundScenario getSupportedScenario() {
        return RefundScenario.DIFFERENCE;
    }
} 