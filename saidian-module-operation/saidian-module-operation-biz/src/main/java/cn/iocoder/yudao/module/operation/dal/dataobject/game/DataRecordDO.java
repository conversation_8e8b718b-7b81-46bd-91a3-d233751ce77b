package cn.iocoder.yudao.module.operation.dal.dataobject.game;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 篮球数据记录 DO
 *
 * <AUTHOR>
 */
@TableName("sd_data_record")
@KeySequence("sd_data_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataRecordDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 比赛id
     */
    private Long gameId;
    /**
     * 球员id（对于比赛流程记录可为空）
     */
    private Long playerId;
    /**
     * 球队id（对于比赛流程记录可为空）
     */
    private Long teamId;
    /**
     * 节次
     */
    private Integer section;
    /**
     * 操作类型
     */
    private Integer actionType;
    /**
     * 节次内的事件顺序编号
     */
    private Integer sequenceNumber;
    /**
     * 是否命中(投篮类型使用)
     */
    private Boolean isHit;
    /**
     * 位置X坐标(投篮类型使用)
     */
    private Double positionX;
    /**
     * 位置Y坐标(投篮类型使用)
     */
    private Double positionY;
    /**
     * 操作时间
     */
    private LocalDateTime actionTime;
    /**
     * 相关球员id(换人操作使用)
     */
    private Long relatedPlayerId;
    /**
     * 换人时间(换人操作使用)
     */
    private LocalDateTime substituteTime;
    /**
     * 暂停开始时间(计算需要扣除的上场时间)
     */
    private LocalDateTime pauseStartTime;
    /**
     * 暂停结束时间(计算需要扣除的上场时间)
     */
    private LocalDateTime pauseEndTime;
    /**
     * 暂停时间(计算需要扣除的上场时间)
     */
    private LocalDateTime pauseTime;
    /**
     * 暂停时长(秒)
     */
    private Integer pauseDuration;

}