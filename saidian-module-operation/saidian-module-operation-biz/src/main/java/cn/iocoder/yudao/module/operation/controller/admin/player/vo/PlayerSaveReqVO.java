package cn.iocoder.yudao.module.operation.controller.admin.player.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 球员新增/修改 Request VO")
@Data
public class PlayerSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20951")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12902")
    private Long memberUserId;

    @Schema(description = "姓名", example = "芋艿")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "身高")
    private Double height;

    @Schema(description = "体重")
    private Integer weight;

    @Schema(description = "能力值")
    private Integer ratings;

    @Schema(description = "信用度")
    private Integer creditScore;

    @Schema(description = "球衣号码")
    private Integer number;

    @Schema(description = "性别：1、男 | 2、女")
    private Integer sex;

    @Schema(description = "经验值")
    private Integer experience;

    @Schema(description = "球员位置")
    private Integer position;
}