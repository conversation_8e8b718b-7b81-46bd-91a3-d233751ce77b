package cn.iocoder.yudao.module.operation.service.activity.impl;

import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.service.activity.ActivityQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 活动查询 Service 实现类
 * 专门用于提供活动查询功能，避免循环依赖
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ActivityQueryServiceImpl implements ActivityQueryService {

    @Resource
    private ActivityMapper activityMapper;

    @Override
    public ActivityDO getActivity(@NotNull Long id) {
        ActivityDO activity = activityMapper.selectById(id);
        if (activity == null || Boolean.TRUE.equals(activity.getDeleted())) {
            return null;
        }
        return activity;
    }

    @Override
    public ActivityDO getActivityByGameId(@NotNull Long gameId) {
        // 这里需要实现根据gameId查询activity的逻辑
        // 暂时返回null，需要根据实际的数据库表结构来实现
        log.warn("[getActivityByGameId] 方法待实现，gameId: {}", gameId);
        return null;
    }
} 