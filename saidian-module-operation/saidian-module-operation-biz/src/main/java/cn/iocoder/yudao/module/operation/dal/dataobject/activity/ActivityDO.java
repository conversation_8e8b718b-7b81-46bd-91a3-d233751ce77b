package cn.iocoder.yudao.module.operation.dal.dataobject.activity;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 活动定义表 DO
 *
 * <AUTHOR> // TODO 请修改
 */
@TableName("sd_activity")
@KeySequence("sd_activity_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDO extends BaseDO {

    /**
     * 活动ID
     */
    @TableId
    private Long id;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 活动类型: 1-排位赛, 2-友谊赛, 3-联赛
     * 枚举 {@link cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum}
     */
    private Integer type;
    /**
     * 游戏类型: 1-全场5人制, 2-半场4人制 (GameTypeEnum), 可默认为1
     * 枚举 {@link cn.iocoder.yudao.module.operation.enums.GameTypeEnum}
     */
    private Integer gameType;
    /**
     * 关联的 sd_game ID (如果活动基于某场具体比赛)
     */
    private Long gameId;
    /**
     * 活动/报名开始时间
     */
    private LocalDateTime startTime;
    /**
     * 活动/报名结束时间
     */
    private LocalDateTime endTime;
    /**
     * 报名截止时间 (组局时间点)
     */
    private LocalDateTime registrationDeadline;
    /**
     * 活动地点
     */
    private String location;
    /**
     * 活动封面图片地址
     */
    private String picUrl;
    /**
     * 总费用(分, 排位赛/友谊赛用)
     */
    private Integer totalFee;
    /**
     * 每场最低人数(排位赛)
     */
    private Integer minPlayersPerGame;
    /**
     * 每场最高人数(排位赛)
     */
    private Integer maxPlayersPerGame;
    /**
     * 友谊赛最低成团队伍数
     */
    private Integer minTeamsFriendly;
    /**
     * 友谊赛每队最低人数
     */
    private Integer minPlayersPerTeamFriendly;
    /**
     * 友谊赛每队最高人数
     */
    private Integer maxPlayersPerTeamFriendly;
    /**
     * 联赛每人报名费(分)
     */
    private Integer leagueFeePerPlayer;
    /**
     * 联赛最低成团队伍数
     */
    private Integer minTeamsLeague;
    /**
     * 联赛每队最低人数
     */
    private Integer minPlayersPerTeamLeague;
    /**
     * 主队ID (排位赛用, 关联 sd_team.id)
     */
    private Long homeTeamId;
    /**
     * 客队ID (排位赛用, 关联 sd_team.id)
     */
    private Long guestTeamId;
    /**
     * 是否支持好友组队 (主要用于排位赛)
     */
    private Boolean friendGroupSupported;
    /**
     * 每个好友组最大人数 (排位赛用, 一般为3人)
     */
    private Integer maxPlayersPerFriendGroup;
    /**
     * 好友组队邀请有效时长(分钟)
     */
    private Integer friendGroupInviteDurationMinutes;
    /**
     * 活动状态: 1-未开始, 2-报名中, 3-组局中, 4-进行中, 5-已结束, 6-已取消
     * 枚举 {@link cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum}
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

} 