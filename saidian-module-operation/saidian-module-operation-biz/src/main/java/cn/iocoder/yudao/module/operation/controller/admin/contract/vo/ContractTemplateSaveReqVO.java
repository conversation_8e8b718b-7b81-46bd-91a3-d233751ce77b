package cn.iocoder.yudao.module.operation.controller.admin.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 合同邀请新增/修改 Request VO")
@Data
public class ContractTemplateSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24585")
    private Long id;

    @Schema(description = "合同内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "合同内容不能为空")
    private String content;

    @Schema(description = "合同类型：1、球队合同", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "合同类型：1、球队合同不能为空")
    private Integer type;

}