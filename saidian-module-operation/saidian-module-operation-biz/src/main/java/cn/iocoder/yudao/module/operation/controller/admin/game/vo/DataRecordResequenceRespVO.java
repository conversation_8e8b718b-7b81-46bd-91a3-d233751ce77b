package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 重新排序节次内记录响应 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataRecordResequenceRespVO {

    @Schema(description = "影响的记录数")
    private Integer affectedRecords;

    @Schema(description = "第一个序号")
    private Integer firstSequence;

    @Schema(description = "最后一个序号")
    private Integer lastSequence;
} 