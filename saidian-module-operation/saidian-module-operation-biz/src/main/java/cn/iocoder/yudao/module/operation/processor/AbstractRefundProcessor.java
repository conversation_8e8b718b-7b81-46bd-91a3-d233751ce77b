package cn.iocoder.yudao.module.operation.processor;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationLogDO;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationLogMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationLogTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RefundStatusEnum;
import cn.iocoder.yudao.module.pay.api.refund.PayRefundApi;
import cn.iocoder.yudao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 退款处理器抽象类 - 模板方法模式
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractRefundProcessor {

    @Resource
    protected RegistrationMapper registrationMapper;

    @Resource
    protected RegistrationLogMapper registrationLogMapper;

    @Resource
    protected PayRefundApi payRefundApi;

    @Resource
    protected TransactionTemplate transactionTemplate;

    /**
     * 退款模板方法 - 定义标准退款流程
     *
     * @param registrationId 报名记录ID
     * @param operatorId 操作人ID (0表示系统)
     * @return 是否处理成功
     */
    public boolean processRefund(Long registrationId, Long operatorId) {
        // 1. 查询报名记录
        RegistrationDO registration = registrationMapper.selectById(registrationId);
        if (registration == null) {
            log.error("[processRefund] 报名记录不存在: {}", registrationId);
            return false;
        }

        try {
            // 2. 校验退款条件
            validateRefund(registration, operatorId);

            // 3. 查询活动信息
            ActivityDO activity = getActivity(registration.getActivityId());
            if (activity == null) {
                log.error("[processRefund] 活动不存在: {}", registration.getActivityId());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ACTIVITY_NOT_EXISTS);
            }

            // 4. 计算退款金额
            Integer refundAmount = calculateAmount(activity, registration);
            if (refundAmount <= 0) {
                log.info("[processRefund] 报名记录({})退款金额为0，跳过退款流程", registrationId);
                return true;
            }

            // 5. 更新报名记录状态和执行退款
            return transactionTemplate.execute(status -> {
                try {
                    // 5.1 更新报名记录状态
                    beforeRefund(registration, refundAmount);

                    // 5.2 调用支付API发起退款
                    Long refundId = callPayRefundApi(registration, refundAmount);

                    // 5.3 记录退款日志
                    recordRefundLog(registration, operatorId, refundAmount, refundId);

                    // 5.4 执行后续处理
                    afterRefund(registration, activity, refundAmount);

                    return true;
                } catch (Exception e) {
                    log.error("[processRefund] 处理退款异常: {}", registrationId, e);
                    status.setRollbackOnly();
                    return false;
                }
            });
        } catch (Exception e) {
            log.error("[processRefund] 退款流程异常: {}", registrationId, e);
            return false;
        }
    }

    /**
     * 校验退款条件
     */
    protected void validateRefund(RegistrationDO registration, Long operatorId) {
        // 基本状态校验 - 不能重复退款
        if (registration.getRefundStatus() != null && 
                registration.getRefundStatus() >= RefundStatusEnum.FULL_REFUNDED.getStatus()) {
            log.error("[validateRefund] 报名记录({})已经全额退款，不能重复退款", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.REFUND_ALREADY_PROCESSED);
        }

        // 检查是否有支付订单
        if (registration.getPayOrderId() == null || registration.getPayOrderId() <= 0) {
            log.error("[validateRefund] 报名记录({})没有关联支付订单，无法退款", registration.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAYMENT_ORDER_NOT_FOUND);
        }

        // 子类提供特定场景的校验逻辑
        doValidateRefund(registration, operatorId);
    }

    /**
     * 子类提供特定场景的校验逻辑
     */
    protected abstract void doValidateRefund(RegistrationDO registration, Long operatorId);

    /**
     * 获取活动信息 - 子类可覆盖此方法提供实现
     */
    protected ActivityDO getActivity(Long activityId) {
        // 默认实现假设返回null，需要子类实现
        return null;
    }

    /**
     * 计算退款金额 - 子类必须实现
     */
    protected abstract Integer calculateAmount(ActivityDO activity, RegistrationDO registration);

    /**
     * 退款前处理 - 更新报名记录状态
     */
    protected void beforeRefund(RegistrationDO registration, Integer refundAmount) {
        // 更新报名记录状态
        RegistrationDO update = new RegistrationDO();
        update.setId(registration.getId());
        
        // 退款状态设置（全额或部分）
        if (refundAmount.equals(registration.getActualPayPrice())) {
            update.setRefundStatus(RefundStatusEnum.FULL_REFUNDED.getStatus());
            
            // 如果是全额退款，可能需要取消报名
            if (registration.getStatus() != RegistrationStatusEnum.CANCELLED.getStatus()) {
                update.setStatus(RegistrationStatusEnum.CANCELLED.getStatus());
            }
        } else {
            update.setRefundStatus(RefundStatusEnum.PARTIAL_REFUNDED.getStatus());
        }
        
        // 累计退款金额
        Integer totalRefundPrice = registration.getTotalRefundPrice();
        totalRefundPrice = totalRefundPrice == null ? 0 : totalRefundPrice;
        update.setTotalRefundPrice(totalRefundPrice + refundAmount);

        registrationMapper.updateById(update);
    }

    /**
     * 调用支付API发起退款
     */
    protected Long callPayRefundApi(RegistrationDO registration, Integer refundAmount) {
        PayRefundCreateReqDTO payRefundCreateReqDTO = new PayRefundCreateReqDTO();
        
        // 设置应用标识 - 正常应该从配置中获取
        payRefundCreateReqDTO.setAppKey("default-app");
        // 设置用户IP
        payRefundCreateReqDTO.setUserIp("127.0.0.1");
        // 设置订单信息
        payRefundCreateReqDTO.setMerchantOrderId(String.valueOf(registration.getId()));
        payRefundCreateReqDTO.setMerchantRefundId("refund_" + registration.getId() + "_" + System.currentTimeMillis());
        // 设置退款金额
        payRefundCreateReqDTO.setPrice(refundAmount);
        // 设置退款原因
        payRefundCreateReqDTO.setReason(getRefundReason());

        // 调用支付模块API - 创建退款单并返回退款单ID
        return payRefundApi.createRefund(payRefundCreateReqDTO);
    }

    /**
     * 获取退款原因 - 子类可覆盖此方法
     */
    protected String getRefundReason() {
        return "活动报名退款";
    }

    /**
     * 记录退款操作日志
     */
    protected void recordRefundLog(RegistrationDO registration, Long operatorId, Integer refundAmount, Long refundId) {
        RegistrationLogDO logDO = new RegistrationLogDO();
        logDO.setRegistrationId(registration.getId());
        logDO.setUserId(operatorId != null ? operatorId : 0L);
        logDO.setLogType(getLogType().getType());
        logDO.setContent(String.format("退款金额: %d分, 退款单号: %d", refundAmount, refundId));
        logDO.setBeforeStatus(registration.getStatus());
        logDO.setAfterStatus(registration.getStatus()); // 可能不变更状态
        logDO.setCreateTime(LocalDateTime.now());
        
        registrationLogMapper.insert(logDO);
    }
    
    /**
     * 获取日志类型 - 子类可以覆盖此方法
     * @return 日志类型枚举
     */
    protected RegistrationLogTypeEnum getLogType() {
        return RegistrationLogTypeEnum.REFUND_SUCCESS;
    }

    /**
     * 退款后处理 - 子类可覆盖此方法
     */
    protected void afterRefund(RegistrationDO registration, ActivityDO activity, Integer refundAmount) {
        // 默认空实现，子类可以覆盖
    }

    /**
     * 获取处理器支持的退款场景
     */
    public abstract RefundScenario getSupportedScenario();
} 