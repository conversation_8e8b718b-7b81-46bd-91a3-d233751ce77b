package cn.iocoder.yudao.module.operation.service.discount.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "用户积分信息 DTO (用于优惠计算上下文)")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true) // Added for fluent setters like .setUserId().setAvailablePoints()
public class UserPointsInfoDTO {

    @Schema(description = "用户ID", example = "1024")
    private Long userId;

    @Schema(description = "可用积分", example = "500")
    private Integer availablePoints;

    // Add other fields if needed by discount logic, e.g., points-to-cash rate, max deductible, etc.
    // For now, keeping it simple to match original MemberUserPointsSummaryRespDTO structure used.

    // 按照 AppRegistrationSettlementRespVO.UserPointsSummaryVO 的结构补充字段
    private Integer pointsToCashRate; // 积分兑换现金的比例（例如，1 表示1积分抵扣1分钱）
    private Integer maxPointsUsableForOrder; // 每单最多可使用的积分数
    private Integer maxPointsDiscountAmount; // 每单通过积分可抵扣的最大金额（分）
} 