package cn.iocoder.yudao.module.operation.service.state.impl;

import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.operation.service.state.RegistrationState;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 活动完成状态处理
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CompletedState implements RegistrationState {

    @Override
    public void handlePaySuccess(RegistrationDO registration, PayOrderNotifyReqDTO notifyReqDTO) {
        log.warn("[handlePaySuccess][CompletedState] registrationId({}) 状态已是完成，忽略支付回调。", registration.getId());
        // 已经是完成状态，忽略支付回调
    }

    @Override
    public void cancel(RegistrationDO registration, Long operatorId, String cancelReason) {
        log.warn("[cancel][CompletedState] registrationId({}) 报名已完成，无法取消", registration.getId());
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_CANCEL, "活动已完成，无法取消报名");
    }

    @Override
    public void applyRefund(RegistrationDO registration, String reason) {
        log.warn("[applyRefund][CompletedState] registrationId({}) 活动已完成，无法申请退款", registration.getId());
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.REGISTRATION_CANNOT_REFUND, "活动已完成，无法申请退款");
    }

    @Override
    public void complete(RegistrationDO registration) {
        log.warn("[complete][CompletedState] registrationId({}) 活动已经完成，无需重复操作", registration.getId());
        // 已经是完成状态，不做处理
    }
} 