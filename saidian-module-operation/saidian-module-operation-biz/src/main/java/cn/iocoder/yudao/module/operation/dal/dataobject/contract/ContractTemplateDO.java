package cn.iocoder.yudao.module.operation.dal.dataobject.contract;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.operation.enums.MessageTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 合同邀请 DO
 *
 * <AUTHOR>
 */
@TableName("sd_contract_template")
@KeySequence("sd_contract_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractTemplateDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 合同内容
     */
    private String content;
    /**
     * TEAM_CONTRACT(1, "合同邀请"),
     * APPLY_CONTRACT(2, "入队申请"),
     * <p>
     * 枚举 {@link MessageTypeEnum 对应的类}
     */
    private Integer type;

}