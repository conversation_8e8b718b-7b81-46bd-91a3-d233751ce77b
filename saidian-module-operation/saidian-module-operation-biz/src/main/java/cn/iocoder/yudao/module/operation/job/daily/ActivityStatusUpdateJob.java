package cn.iocoder.yudao.module.operation.job.daily;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.operation.service.daily.DailyActivityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 活动状态更新 Job
 *
 * <AUTHOR>
 */
@Component
public class ActivityStatusUpdateJob implements JobHandler {

    @Resource
    private DailyActivityService dailyActivityService;

    @Override
    public String execute(String param) throws Exception {

        int count = dailyActivityService.updateDailyActivityStatus();

        return StrUtil.format("更新活动状态，共更新 {} 个", count);
    }
}
