package cn.iocoder.yudao.module.operation.service.registration;

import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;

import java.util.List;

/**
 * 报名超时处理服务接口
 * 
 * 🎯 目标：解决事务自调用问题，将超时处理逻辑独立到专门的服务中
 * 
 * 📋 职责：
 * 1. 处理创建时间超时的报名记录
 * 2. 批量处理候补提升
 * 3. 提供事务控制的超时处理方法
 * 
 * <AUTHOR>
 */
public interface RegistrationTimeoutService {

    /**
     * 处理创建时间超时的报名记录（30分钟）
     * 
     * @return 处理的记录数量
     */
    int processTimeoutRegistrations();

    /**
     * 批量处理候补提升
     * 
     * @return 提升的候补球员数量
     */
    int batchPromoteWaitlist();

    /**
     * 更新单个报名记录为超时状态
     * 
     * @param registration 报名记录
     * @param reason 超时原因
     */
    void updateRegistrationToTimeout(RegistrationDO registration, String reason);

    /**
     * 重排候补队列
     * 
     * @param activityId 活动ID
     * @param fromPosition 从指定位置开始重排
     */
    void reorderWaitlistFromPosition(Long activityId, Integer fromPosition);
}
