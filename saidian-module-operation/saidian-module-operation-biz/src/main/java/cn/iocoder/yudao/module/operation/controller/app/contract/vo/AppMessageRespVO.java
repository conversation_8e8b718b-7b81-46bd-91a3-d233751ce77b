package cn.iocoder.yudao.module.operation.controller.app.contract.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerInfoRespVO;
import cn.iocoder.yudao.module.operation.controller.app.team.vo.AppTeamRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AppMessageRespVO {
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24585")
    private Long messageId;

    @Schema(description = "发送者ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20951")
    private Long senderId;

    private AppTeamRespVO team;

    private AppPlayerInfoRespVO player;

    @Schema(description = "合同内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    @Schema(description = "合同类型：1、球队合同", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("sd_contract_type")
    private Integer type;

    @DictFormat("sd_contract_status")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
}
