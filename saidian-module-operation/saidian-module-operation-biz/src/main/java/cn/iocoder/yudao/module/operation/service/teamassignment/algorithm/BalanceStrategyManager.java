package cn.iocoder.yudao.module.operation.service.teamassignment.algorithm;

import cn.iocoder.yudao.module.operation.constant.OperationConstants;
import cn.iocoder.yudao.module.operation.enums.PlayerLevelEnum;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;
import cn.iocoder.yudao.module.operation.service.teamassignment.algorithm.strategy.BalanceStrategy;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import cn.iocoder.yudao.module.operation.util.LevelBalanceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平衡策略管理器
 * 
 * 负责管理和协调所有的平衡策略，提供统一的平衡度计算和评估接口。
 * 支持多维度的平衡评估，包括能力值、身高、位置、好友组等。
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BalanceStrategyManager {

    /**
     * 所有平衡策略列表
     */
    private final List<BalanceStrategy> strategies;

    /**
     * 策略映射表，按类型索引
     */
    private final Map<String, BalanceStrategy> strategyMap;

    @Autowired
    public BalanceStrategyManager(List<BalanceStrategy> strategies) {
        this.strategies = strategies;
        this.strategyMap = new HashMap<>();
        
        // 初始化策略映射表
        for (BalanceStrategy strategy : strategies) {
            strategyMap.put(strategy.getStrategyType(), strategy);
            log.info("初始化平衡策略: {} (权重: {})", strategy.getStrategyType(), strategy.getWeight());
        }
        
        // 验证权重总和
        validateWeights();
    }

    /**
     * 计算综合平衡度分数
     * 
     * @param homeTeamPlayers 主队球员列表
     * @param guestTeamPlayers 客队球员列表
     * @return 综合平衡度评分（0-100）
     */
    public int calculateOverallBalanceScore(@NotNull List<PlayerRegistrationInfo> homeTeamPlayers,
                                          @NotNull List<PlayerRegistrationInfo> guestTeamPlayers) {
        
        if (strategies.isEmpty()) {
            log.warn("没有注册的平衡策略，返回默认评分");
            return OperationConstants.DefaultScores.DEFAULT_BALANCE_SCORE;
        }

        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;

        for (BalanceStrategy strategy : strategies) {
            try {
                int strategyScore = strategy.calculateBalanceScore(homeTeamPlayers, guestTeamPlayers);
                double weight = strategy.getWeight();
                
                totalWeightedScore += strategyScore * weight;
                totalWeight += weight;
                
                log.debug("策略 {} 评分: {}, 权重: {}", strategy.getStrategyType(), strategyScore, weight);
                
            } catch (Exception e) {
                log.error("策略 {} 计算评分时发生错误", strategy.getStrategyType(), e);
                // 继续处理其他策略
            }
        }

        if (totalWeight == 0) {
            log.warn("总权重为0，返回默认评分");
            return OperationConstants.DefaultScores.DEFAULT_BALANCE_SCORE;
        }

        int overallScore = (int) Math.round(totalWeightedScore / totalWeight);
        log.debug("综合平衡度评分 {}", overallScore);
        
        return Math.max(OperationConstants.DefaultScores.MIN_SCORE, 
                       Math.min(OperationConstants.DefaultScores.MAX_SCORE, overallScore));
    }

    /**
     * 获取详细的平衡度评估
     * 
     * @param homeTeamPlayers 主队球员列表
     * @param guestTeamPlayers 客队球员列表
     * @return 详细的平衡度评估
     */
    public TeamAssignmentResult.BalanceEvaluation getDetailedBalanceEvaluation(
            @NotNull List<PlayerRegistrationInfo> homeTeamPlayers,
            @NotNull List<PlayerRegistrationInfo> guestTeamPlayers) {
        
        TeamAssignmentResult.BalanceEvaluation.BalanceEvaluationBuilder builder = 
                TeamAssignmentResult.BalanceEvaluation.builder();

        // 计算各种维度的平衡度
        for (BalanceStrategy strategy : strategies) {
            try {
                int score = strategy.calculateBalanceScore(homeTeamPlayers, guestTeamPlayers);
                
                switch (strategy.getStrategyType()) {
                    case OperationConstants.StrategyTypes.ABILITY_BALANCE:
                        builder.abilityBalance(score);
                        break;
                    case OperationConstants.StrategyTypes.HEIGHT_BALANCE:
                        builder.heightBalance(score);
                        break;
                    case OperationConstants.StrategyTypes.POSITION_BALANCE:
                        builder.positionBalance(score);
                        break;
                    case OperationConstants.StrategyTypes.FRIEND_GROUP_BALANCE:
                        builder.friendGroupBalance(score);
                        break;
                    case OperationConstants.StrategyTypes.WIN_RATE_BALANCE:
                        builder.winRateBalance(score);
                        break;
                    default:
                        log.warn("未知的策略类型 {}", strategy.getStrategyType());
                }
                
            } catch (Exception e) {
                log.error("策略 {} 获取详细评估时发生错误", strategy.getStrategyType(), e);
            }
        }

        // 计算队伍统计信息
        TeamAssignmentResult.TeamStats homeStats = calculateTeamStats(homeTeamPlayers);
        TeamAssignmentResult.TeamStats guestStats = calculateTeamStats(guestTeamPlayers);
        
        builder.homeTeamStats(homeStats);
        builder.guestTeamStats(guestStats);

        return builder.build();
    }


    /**
     * 验证权重总和
     */
    private void validateWeights() {
        double totalWeight = strategies.stream()
                .mapToDouble(BalanceStrategy::getWeight)
                .sum();
        
        log.info("所有策略权重总和: {}", totalWeight);
        
        if (Math.abs(totalWeight - OperationConstants.Precision.STANDARD_WEIGHT_SUM) > OperationConstants.Precision.WEIGHT_TOLERANCE) {
            log.warn("策略权重总和不等于{}，当前总和: {}", OperationConstants.Precision.STANDARD_WEIGHT_SUM, totalWeight);
        }
    }

    /**
     * 计算队伍统计信息
     */
    private TeamAssignmentResult.TeamStats calculateTeamStats(List<PlayerRegistrationInfo> players) {
        if (players.isEmpty()) {
            return TeamAssignmentResult.TeamStats.builder()
                    .playerCount(0)
                    .averageAbility(0.0)
                    .averageHeight(0.0)
                    .averageWinRate(0.0)
                    .positionDistribution(new HashMap<>())
                    .friendGroupCount(0)
                    .sLevelPlayerCount(0)
                    .aLevelPlayerCount(0)
                    .build();
        }

        // 计算平均能力值
        double avgAbility = players.stream()
                .mapToDouble(p -> p.getPlayer() != null && p.getPlayer().getRatings() != null ? 
                           p.getPlayer().getRatings().doubleValue() : OperationConstants.PlayerAbility.DEFAULT_ABILITY)
                .average()
                .orElse(OperationConstants.PlayerAbility.DEFAULT_ABILITY);

        // 计算平均身高
        double avgHeight = players.stream()
                .mapToDouble(p -> p.getPlayer() != null && p.getPlayer().getHeight() != null ? 
                           p.getPlayer().getHeight() : OperationConstants.PlayerAbility.DEFAULT_HEIGHT)
                .average()
                .orElse(OperationConstants.PlayerAbility.DEFAULT_HEIGHT);

        // 使用统一的等级分布计算（修复硬编码问题）
        Map<PlayerLevelEnum, Integer> levelCounts = LevelBalanceUtils.getLevelDistribution(players);

        return TeamAssignmentResult.TeamStats.builder()
                .playerCount(players.size())
                .averageAbility(avgAbility)
                .averageHeight(avgHeight)
                .averageWinRate(OperationConstants.DefaultScores.DEFAULT_WIN_RATE)
                .positionDistribution(new HashMap<>())
                .friendGroupCount(0)
                .sLevelPlayerCount(levelCounts.getOrDefault(PlayerLevelEnum.S_LEVEL, 0))
                .aLevelPlayerCount(levelCounts.getOrDefault(PlayerLevelEnum.A_LEVEL, 0))
                .bLevelPlayerCount(levelCounts.getOrDefault(PlayerLevelEnum.B_LEVEL, 0))
                .cLevelPlayerCount(levelCounts.getOrDefault(PlayerLevelEnum.C_LEVEL, 0))
                .dLevelPlayerCount(levelCounts.getOrDefault(PlayerLevelEnum.D_LEVEL, 0))
                .build();
    }

} 
