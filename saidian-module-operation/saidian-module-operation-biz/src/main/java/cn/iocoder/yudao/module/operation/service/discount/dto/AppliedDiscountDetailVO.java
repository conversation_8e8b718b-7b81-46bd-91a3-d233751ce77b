package cn.iocoder.yudao.module.operation.service.discount.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 应用的优惠详情 Response VO") // TODO: 这个描述可能需要根据实际用途调整，是后台还是App端
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppliedDiscountDetailVO {

    @Schema(description = "优惠类型", required = true, example = "COUPON") // 例如：COUPON, POINTS, VIP_DISCOUNT
    private String discountType;

    @Schema(description = "优惠描述", required = true, example = "优惠券满100减10")
    private String description;

    @Schema(description = "减免金额，单位分", required = true, example = "1000")
    private Integer amountDeducted;

    @Schema(description = "相关引用ID (例如优惠券实例ID, 积分活动ID等)", example = "12345")
    private String referenceId; // 使用 String 类型以兼容各种ID格式

    // 可以根据需要添加更多字段，例如：
    // @Schema(description = "优惠券名称", example = "新手专享券")
    // private String couponName;
    // @Schema(description = "折扣百分比 (如果是折扣券)", example = "88") // 88 代表 88%
    // private Integer discountPercentage;
} 