package cn.iocoder.yudao.module.operation.service.game;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.*;
import cn.iocoder.yudao.module.operation.controller.app.daily.vo.AppDailyActivityRegisterPlayerRespVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameScheduleCardResponseVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameSchedulePageReqVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameScheduleScrollReqVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerRankRespVO;
import cn.iocoder.yudao.module.operation.controller.app.team.TeamGameHistoryVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.DailyActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.league.LeagueDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.service.daily.bo.PlayerAssignBO;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameResultBO;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameStatisticsBO;
import cn.iocoder.yudao.module.operation.service.league.bo.LeagueRegistrationTeamBO;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;

import javax.validation.Valid;
import java.util.Set;
import java.util.List;
import java.util.Map;

/**
 * 比赛 Service 接口
 *
 * <AUTHOR>
 */
public interface GameService {

    /**
     * 创建比赛
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGame(@Valid GameSaveReqVO createReqVO);

    /**
     * 从活动创建比赛 (内部使用)
     * 排位赛和友谊赛都使用此方法，因为主客队信息都已在activity中设置好
     *
     * @param activity 活动信息
     * @return 比赛编号
     */
    Long createGameFromActivity(@Valid ActivityDO activity);

    /**
     * 更新比赛的主客队信息 (内部使用)
     *
     * @param gameId 比赛ID
     * @param homeTeamId 主队ID
     * @param guestTeamId 客队ID
     */
    void updateGameTeams(Long gameId, Long homeTeamId, Long guestTeamId);

    /**
     * 将友谊赛球员添加到比赛中 (内部使用)
     *
     * @param gameId 比赛ID
     * @param registrations 报名记录列表
     * @return 添加的球员数量
     */
    int addPlayersToFriendlyGame(Long gameId, List<RegistrationDO> registrations);

    /**
     * 更新比赛
     *
     * @param updateReqVO 更新信息
     */
    void updateGame(@Valid GameSaveReqVO updateReqVO);

    /**
     * 删除比赛
     *
     * @param id 编号
     */
    void deleteGame(Long id);

    /**
     * 获得比赛
     *
     * @param id 编号
     * @return 比赛
     */
    GameDO getGame(Long id);

    /**
     * 获得比赛分页
     *
     * @param pageReqVO 分页查询
     * @return 比赛分页
     */
    PageResult<GameRespVO> getGamePage(GamePageReqVO pageReqVO);

    /**
     * 根据活动ID获得比赛
     *
     * @param dailyActivityDO 活动ID
     * @return 比赛
     */
    GameDO getGameByActivityId(DailyActivityDO dailyActivityDO);

    void updateGameStatusById(Long gameId, Integer status);

    void updateGame(GameDO gameDo);

    void updateGameAttend(GameDetailSaveReqVO updateReqVO);

    void changeTeam(Long gameId, Long playerId, Long teamId);

    List<PlayerGameResultBO> getAllGameOfPlayer(Long playerId);

    PlayerGameStatisticsBO getGameStatisticsOfPlayer(Long playerId);

    PageResult<AppGameScheduleCardResponseVO> schedulePage(AppGameSchedulePageReqVO reqVO);

    Long addPlayerToGame(Long gameId, Long assignedTeamId, PlayerDO playerDO);

    void removePlayerFromGameByUserId(Long gameId, Long userId);

    List<AppDailyActivityRegisterPlayerRespVO> getPlayersByGameIdAndTeamId(Long gameId, Long teamId);

    List<PlayerAssignBO> getPlayerAssignBOByGameId(Long gameId);

    void removePlayerFromGameByPlayerGameRelatedId(Long playerGameRelatedId);

    PageResult<AppPlayerRankRespVO> getWinRateRankPage(PageParam pageVO);

    PageResult<AppPlayerRankRespVO> getWinCountRankPage(PageParam pageVO);

    void addPlayerToGame(GamePlayerReqVO gamePlayerReqVO);

    void deletePlayerFromGame(Long playerId, Long id);

    /**
     * 获得所有玩家的比赛统计信息
     * Map<playerId, PlayerGameStatisticsBO>
     */
    Map<Long, PlayerGameStatisticsBO> getGameStatisticsOfAllPlayer();

    PageResult<AppGameScheduleCardResponseVO> getTeamSchedulePage(@Valid AppGameSchedulePageReqVO teamId);

    Map<String, TeamGameHistoryVO> getGameHistoryOfAllTeams(List<Long> teamIds);

    GameDO createGame(List<LeagueRegistrationTeamBO> successTeams, LeagueDO league);

    PageResult<AppGameScheduleCardResponseVO> scheduleScroll(AppGameScheduleScrollReqVO reqVO);

    AppGameScheduleCardResponseVO getNextGameSchedule(Long userId);

    PageResult<AppGameScheduleCardResponseVO> scheduleScrollByPlayerId(AppGameScheduleScrollReqVO reqVO);

    // =============== 批量操作方法 ===============

    /**
     * 批量添加球员到比赛
     * 
     * @param gameId 比赛ID
     * @param playerInfos 球员信息列表（包含球员和分配的队伍）
     * @return 成功添加的球员数量
     */
    int addPlayersToGameBatch(Long gameId, List<PlayerRegistrationInfo> playerInfos);

    /**
     * 批量更新比赛中球员的队伍分配
     * 用于队伍重新平衡后同步Game中的球员队伍
     * 
     * @param gameId 比赛ID
     * @param teamAssignmentMap playerId -> teamId的映射
     * @return 成功更新的球员数量
     */
    int updatePlayerTeamsBatch(Long gameId, Map<Long, Long> teamAssignmentMap);

    /**
     * 批量从比赛中移除球员
     * 
     * @param gameId 比赛ID
     * @param playerIds 要移除的球员ID列表
     * @return 成功移除的球员数量
     */
    int removePlayersFromGameBatch(Long gameId, List<Long> playerIds);

    /**
     * 检查球员是否已在比赛中
     * 
     * @param gameId 比赛ID
     * @param playerId 球员ID
     * @return 存在返回true，不存在返回false
     */
    boolean isPlayerInGame(Long gameId, Long playerId);

    /**
     * 获取比赛中的所有球员ID
     * 
     * @param gameId 比赛ID
     * @return 球员ID集合
     */
    Set<Long> getPlayerIdsInGame(Long gameId);
}