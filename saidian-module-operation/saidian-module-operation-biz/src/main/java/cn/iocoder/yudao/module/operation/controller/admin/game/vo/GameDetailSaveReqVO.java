package cn.iocoder.yudao.module.operation.controller.admin.game.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GameDetailSaveReqVO {
    @Schema(description = "比赛id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "比赛id不能为空")
    private Long id;

    @Schema(description = "主队出席情况", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "主队出席情况")
    private List<PlayerAttendedReqVO> homePlayers;

    @Schema(description = "客队出席情况", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "客队出席情况")
    private List<PlayerAttendedReqVO> guestPlayers;

    @Data
    public static class PlayerAttendedReqVO {
        @NotNull(message = "球员比赛关联ID不能为空")
        private Long playerGameRelatedId;

        @Schema(description = "队员id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
        @NotNull(message = "队员id不能为空")
        private Long playerId;

        @Schema(description = "球员号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
        private Integer number;

        @Schema(description = "是否出席", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
        @NotNull(message = "是否出席不能为空")
        private Integer attend;
    }
}
