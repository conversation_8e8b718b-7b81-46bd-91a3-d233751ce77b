package cn.iocoder.yudao.module.operation.service.daily;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.app.daily.vo.AppDailyActivityRegisterPlayerRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.PlayerRegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.daily.PlayerRegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.PlayerRegistrationStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 比赛个人报名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlayerRegistrationServiceImpl implements PlayerRegistrationService {
    @Resource
    private PlayerRegistrationMapper playerRegistrationMapper;

    @Override
    public List<PlayerRegistrationDO> getRegistrationByActivityId(Long dailyActivityId) {
        return playerRegistrationMapper.selectList(new LambdaQueryWrapper<PlayerRegistrationDO>().eq(PlayerRegistrationDO::getDailyActivityId, dailyActivityId)
                .orderByAsc(PlayerRegistrationDO::getCreateTime));
    }

    @Override
    public int createPlayerRegistration(PlayerRegistrationDO playerRegistrationDO) {
        return playerRegistrationMapper.insert(playerRegistrationDO);
    }

    @Override
    public Map<Long, List<PlayerRegistrationDO>> getRegistrationPlayerCountPerActivityIds(List<Long> dailyActivityIds) {
        if (CollectionUtils.isEmpty(dailyActivityIds)) {
            return new HashMap<>();
        }
        List<PlayerRegistrationDO> playerRegistrationDOS = playerRegistrationMapper.selectList(new LambdaQueryWrapperX<PlayerRegistrationDO>()
                .in(PlayerRegistrationDO::getDailyActivityId, dailyActivityIds));

        return playerRegistrationDOS.stream().collect(HashMap::new, (map, playerRegistrationDO) -> {
            map.computeIfAbsent(playerRegistrationDO.getDailyActivityId(), k -> new ArrayList<>()).add(playerRegistrationDO);
        }, HashMap::putAll);
    }

    @Override
    public PlayerRegistrationDO getRegistrationByActivityIdAndUserId(Long dailyActivityId, Long userId) {
        List<PlayerRegistrationDO> playerRegistrationDOS = playerRegistrationMapper.selectList(new LambdaQueryWrapperX<PlayerRegistrationDO>()
                .eq(PlayerRegistrationDO::getDailyActivityId, dailyActivityId)
                .eq(PlayerRegistrationDO::getUserId, userId));
        if (CollectionUtils.isEmpty(playerRegistrationDOS)) {
            return null;
        }
        return playerRegistrationDOS.get(0);
    }


    @Override
    public void updateBatch(List<PlayerRegistrationDO> newPlayerRegistrations) {
        playerRegistrationMapper.updateBatch(newPlayerRegistrations);
    }

    @Override
    public List<AppDailyActivityRegisterPlayerRespVO> getRegistrationByTeamId(Long teamId) {
        return playerRegistrationMapper.getRegistrationByTeamId(teamId);
    }

    @Override
    public boolean cancelRegistration(Long userId, Long dailyActivityId, Long orderId) {
        return playerRegistrationMapper.delete(new LambdaQueryWrapper<PlayerRegistrationDO>()
                .eq(PlayerRegistrationDO::getUserId, userId)
                .eq(PlayerRegistrationDO::getDailyActivityId, dailyActivityId)
                .eq(PlayerRegistrationDO::getOrderId, orderId)) != 0;
    }

    @Override
    public PageResult<PlayerRegistrationDO> getRegistrationPageByUserId(Long userId, PageParam reqVO) {
        return playerRegistrationMapper.selectPage(reqVO, new LambdaQueryWrapperX<PlayerRegistrationDO>()
                .eq(PlayerRegistrationDO::getUserId, userId)
                .orderByDesc(PlayerRegistrationDO::getCreateTime));
    }

    @Override
    public Boolean updatePlayerRegistrationStatus(Long userId, Long dailyActivityId, Long orderId, PlayerRegistrationStatusEnum status) {
        return playerRegistrationMapper.update(PlayerRegistrationDO.builder().status(status.getStatus()).build(), new LambdaQueryWrapper<PlayerRegistrationDO>()
                .eq(PlayerRegistrationDO::getUserId, userId)
                .eq(PlayerRegistrationDO::getDailyActivityId, dailyActivityId)
                .eq(PlayerRegistrationDO::getOrderId, orderId)) > 0;
    }

    @Override
    public PlayerRegistrationDO getRegistrationByActivityIdAndUserIdAndOrderId(Long userId, Long dailyActivityId, Long orderId) {
        return playerRegistrationMapper.selectOne(new LambdaQueryWrapper<PlayerRegistrationDO>()
                .eq(PlayerRegistrationDO::getUserId, userId)
                .eq(PlayerRegistrationDO::getDailyActivityId, dailyActivityId)
                .eq(PlayerRegistrationDO::getOrderId, orderId));
    }

    @Override
    public Boolean cancelRegistrationById(Long id) {
        return playerRegistrationMapper.deleteById(id) != 0;
    }

}