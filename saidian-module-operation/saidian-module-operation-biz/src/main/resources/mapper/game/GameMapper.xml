<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectGameByPlayerId"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameResultBO">

        SELECT g.id                AS gameId,
               r.team_id           AS teamId,
               r.player_id         AS playerId,
               r.attend            AS attend,
               g.home_team_id      AS homeTeamId,
               g.home_team_points  AS homeTeamPoints,
               g.guest_team_id     AS guestTeamId,
               g.guest_team_points AS guestTeamPoints
        FROM (SELECT *
              FROM sd_game
              WHERE deleted = 0
                AND `status` = 4) g
                 JOIN
             (SELECT *
              FROM sd_player_game_related
              WHERE deleted = 0
                AND player_id = #{playerId})
                 AS r ON r.game_id = g.id
    </select>
    <select id="selectGameSchedulePageOfPlayer"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.GameScheduleBO">
        SELECT
        r.game_id as `gameId`,
        r.attend as `attend`,
        g.home_team_id as `homeTeamId`,
        g.home_team_points as `homeTeamPoints`,
        g.guest_team_id as `guestTeamId`,
        g.guest_team_points as `guestTeamPoints`,
        g.start_time as `startTime`,
        g.`type` as `type`,
        g.`status` as `status`,
        g.`pic_url` as `picUrl`

        FROM (SELECT *
        FROM sd_game
        WHERE deleted = 0 and `status` != 6 and `status` != 5) g
        JOIN
        (SELECT
        DISTINCT game_id,attend
        FROM sd_player_game_related
        WHERE deleted = 0
        <if test="reqVO.playerId != null and reqVO.playerId !=''">
            AND player_id = #{reqVO.playerId}
        </if>
        <if test="reqVO.status != null and reqVO.status !=''">
            AND status = #{status},
        </if>
        ) AS r ON r.game_id = g.id
        ORDER BY g.start_time DESC

    </select>
    <select id="getPlayersByGameIdAndTeamId"
            resultType="cn.iocoder.yudao.module.operation.controller.app.daily.vo.AppDailyActivityRegisterPlayerRespVO">
        SELECT sd_player.id,
               sd_player.name,
               sd_player.avatar,
               sd_player.number,
               sd_player.ratings,
               sd_player.experience,
               sd_player.position
        FROM sd_player
        WHERE id IN (SELECT player_id
                     FROM sd_player_game_related
                     WHERE team_id = #{teamId}
                       and game_id = #{gameId}
                       and deleted = 0);

    </select>
    <select id="getWinRateRankPage"
            resultType="cn.iocoder.yudao.module.operation.dal.mysql.game.AppPlayerRankRespBO">
        SELECT
            p.member_user_id AS user_id,
            p.id AS player_id,
            p.avatar AS avatar,
            p.name AS `name`,
            IFNULL(wins.wins, 0) AS wins,
            IFNULL(total.total_games, 0) AS total_games,
            CASE
                WHEN IFNULL(total.total_games, 0) = 0 THEN '0.00'
                ELSE
                    FORMAT(IFNULL(wins.wins, 0) / total.total_games * 100, 2)
                END AS win_rate
        FROM
            sd_player p
                LEFT JOIN (
                SELECT
                    r.player_id,
                    COUNT(*) AS wins
                FROM
                    sd_player_game_related r
                        JOIN
                    sd_game g ON r.game_id = g.id
                WHERE
                    g.deleted=0 AND   r.deleted = b'0' AND  -- 仅计算未删除的球员比赛记录
                    g.status = 4 AND      -- 仅统计已结束的比赛
                    ((r.team_id = g.home_team_id AND g.home_team_points > g.guest_team_points) OR
                     (r.team_id = g.guest_team_id AND g.guest_team_points > g.home_team_points))
                GROUP BY
                    r.player_id
            ) wins ON p.id = wins.player_id

                LEFT JOIN (
                SELECT
                    r.player_id,
                    COUNT(*) AS total_games
                FROM
                    sd_player_game_related r
                        JOIN
                    sd_game g ON r.game_id = g.id
                WHERE
                    r.deleted = b'0' AND  -- 仅计算未删除的比赛记录
                    g.status = 4          -- 仅统计已结束的比赛
                GROUP BY
                    r.player_id
            ) total ON p.id = total.player_id
        WHERE
            p.deleted = b'0'  -- 仅查询未删除的球员
        ORDER BY
            win_rate DESC;
    </select>
    <select id="getWinCountRankPage"
            resultType="cn.iocoder.yudao.module.operation.dal.mysql.game.AppPlayerRankRespBO">
        SELECT
            p.member_user_id AS user_id,
            p.id AS player_id,
            p.avatar AS avatar,
            p.name AS `name`,
            IFNULL(wins.wins, 0) AS wins
        FROM
            sd_player p
                LEFT JOIN (
                SELECT
                    r.player_id,
                    COUNT(*) AS wins
                FROM
                    sd_player_game_related r
                        JOIN
                    sd_game g ON r.game_id = g.id
                WHERE
                    g.deleted=0 AND   r.deleted = b'0' AND  -- 仅计算未删除的球员比赛记录
                    g.status = 4 AND      -- 仅统计已结束的比赛
                    ((r.team_id = g.home_team_id AND g.home_team_points > g.guest_team_points) OR
                     (r.team_id = g.guest_team_id AND g.guest_team_points > g.home_team_points))
                GROUP BY
                    r.player_id
            ) wins ON p.id = wins.player_id

        ORDER BY
            wins.wins DESC;
    </select>
    <select id="selectGameOfAllPlayer"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameResultBO">
        SELECT g.id                AS gameId,
               r.team_id           AS teamId,
               r.player_id         AS playerId,
               r.attend            AS attend,
               g.home_team_id      AS homeTeamId,
               g.home_team_points  AS homeTeamPoints,
               g.guest_team_id     AS guestTeamId,
               g.guest_team_points AS guestTeamPoints
        FROM (SELECT *
              FROM sd_game
              WHERE deleted = 0
                AND `status` = 4) g
                 JOIN
             (SELECT *
              FROM sd_player_game_related
              WHERE deleted = 0)
                 AS r ON r.game_id = g.id
    </select>
    <select id="selectGameSchedulePage"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.GameScheduleBO">
        SELECT g.id           as `gameId`,
               g.home_team_id      as `homeTeamId`,
               g.home_team_points  as `homeTeamPoints`,
               g.guest_team_id     as `guestTeamId`,
               g.guest_team_points as `guestTeamPoints`,
               g.start_time        as `startTime`,
               g.`type`            as `type`,
               g.`status`          as `status`,
               g.`pic_url`         as `picUrl`
        FROM sd_game g
        WHERE deleted = 0
          and `status` != 6 and `status` != 5
        ORDER BY g.start_time DESC
    </select>
    <select id="selectGameSchedulePageOfTeam"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.GameScheduleBO">

        SELECT g.id           as `gameId`,
               g.home_team_id      as `homeTeamId`,
               g.home_team_points  as `homeTeamPoints`,
               g.guest_team_id     as `guestTeamId`,
               g.guest_team_points as `guestTeamPoints`,
               g.start_time        as `startTime`,
               g.`type`            as `type`,
               g.`status`          as `status`,
               g.`pic_url`         as `picUrl`
        FROM sd_game g
        WHERE deleted = 0
          and `status` != 6 and `status` != 5
          and (g.home_team_id = #{reqVO.teamId} or g.guest_team_id = #{reqVO.teamId})
        ORDER BY g.start_time DESC
    </select>
    <select id="getNextGameSchedule" resultType="cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO">
        SELECT g.id         as id,
               g.start_time as startTime
        FROM sd_game g
                 JOIN sd_player_game_related p ON g.id = p.game_id
        WHERE g.deleted = 0
          and p.deleted = 0
          AND g.`status` = 2
          AND p.user_id = #{userId}
          AND g.start_time > #{time}
        ORDER BY start_time ASC LIMIT 1

    </select>
    <select id="scrollGameSchedulePageOfPlayer"
            resultType="cn.iocoder.yudao.module.operation.service.game.bo.GameScheduleBO">

        SELECT
        r.game_id as `gameId`,
        r.attend as `attend`,
        g.home_team_id as `homeTeamId`,
        g.home_team_points as `homeTeamPoints`,
        g.guest_team_id as `guestTeamId`,
        g.guest_team_points as `guestTeamPoints`,
        g.start_time as `startTime`,
        g.`type` as `type`,
        g.`status` as `status`,
        g.`pic_url` as `picUrl`

        FROM (SELECT *
        FROM sd_game
        WHERE deleted = 0 and `status` != 6 and `status` != 5 and start_time >= #{reqVO.cursorTime}) g
        JOIN
        (SELECT
        DISTINCT game_id,attend
        FROM sd_player_game_related
        WHERE deleted = 0
        <if test="reqVO.playerId != null and reqVO.playerId !=''">
            AND player_id = #{reqVO.playerId}
        </if>
        ) AS r ON r.game_id = g.id
        ORDER BY g.start_time ASC

    </select>

    <!-- 新增：更新比赛的赛季ID -->
    <update id="updateSeasonId">
        UPDATE sd_game 
        SET season_id = #{seasonId}
        WHERE id = #{gameId} AND deleted = 0
    </update>

    <!-- 新增：统计赛季的比赛数量 -->
    <select id="countBySeasonId" resultType="long">
        SELECT COUNT(*)
        FROM sd_game
        WHERE season_id = #{seasonId} AND deleted = 0
    </select>

    <!-- 获取赛季中参与的球员数量 -->
    <select id="countPlayersBySeasonId" resultType="long">
        SELECT COUNT(DISTINCT pgr.player_id)
        FROM sd_player_game_related pgr
        INNER JOIN sd_game g ON g.id = pgr.game_id
        WHERE g.season_id = #{seasonId}
        AND g.deleted = 0
        AND pgr.deleted = 0
    </select>

    <!-- 获取比赛的所有参赛球员 -->
    <select id="selectPlayersRelatedByGameId" resultType="cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerGameRelatedRespVO">
        SELECT 
            pgr.player_id as playerId,
            p.name,
            p.avatar,
            p.position,
            pgr.team_id as teamId,
            pgr.attend,
            pgr.number
        FROM sd_player_game_related pgr
        INNER JOIN sd_player p ON p.id = pgr.player_id
        WHERE pgr.game_id = #{gameId}
        AND pgr.deleted = 0
        AND p.deleted = 0
        ORDER BY pgr.team_id, p.name
    </select>

</mapper>