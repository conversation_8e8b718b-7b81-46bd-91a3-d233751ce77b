package cn.iocoder.yudao.module.operation.service.strategy.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * FeeCalculationUtils 单元测试
 * 验证重构后的费用计算工具类功能完整性
 */
@DisplayName("费用计算工具类测试")
class FeeCalculationUtilsTest {

    private FeeCalculationUtils feeCalculationUtils;

    @BeforeEach
    void setUp() {
        feeCalculationUtils = new FeeCalculationUtils();
    }

    @Test
    @DisplayName("计算人均费用 - 正常情况")
    void testCalculatePerPlayerFee_Normal() {
        // 总费用10000分，10人
        Integer result = feeCalculationUtils.calculatePerPlayerFee(10000, 10);
        assertEquals(1000, result);
    }

    @Test
    @DisplayName("计算人均费用 - 不能整除情况")
    void testCalculatePerPlayerFee_WithRemainder() {
        // 总费用10001分，10人，应该四舍五入
        Integer result = feeCalculationUtils.calculatePerPlayerFee(10001, 10);
        assertEquals(1000, result); // 1000.1 四舍五入到1000
    }

    @Test
    @DisplayName("计算人均费用 - 总费用为null")
    void testCalculatePerPlayerFee_NullTotalFee() {
        Integer result = feeCalculationUtils.calculatePerPlayerFee(null, 10);
        assertEquals(0, result); // 方法返回0而不是抛异常
    }

    @Test
    @DisplayName("计算人均费用 - 人数为0")
    void testCalculatePerPlayerFee_ZeroPlayerCount() {
        Integer result = feeCalculationUtils.calculatePerPlayerFee(10000, 0);
        assertEquals(0, result); // 方法返回0而不是抛异常
    }

    @Test
    @DisplayName("计算半队费用 - 正常情况")
    void testCalculateHalfTeamFee_Normal() {
        Integer result = feeCalculationUtils.calculateHalfTeamFee(10000);
        assertEquals(5000, result);
    }

    @Test
    @DisplayName("计算半队费用 - 奇数总费用")
    void testCalculateHalfTeamFee_OddAmount() {
        Integer result = feeCalculationUtils.calculateHalfTeamFee(10001);
        assertEquals(5000, result); // 5000.5 四舍五入到5000
    }

    @Test
    @DisplayName("计算半队费用 - 总费用为null")
    void testCalculateHalfTeamFee_NullTotalFee() {
        Integer result = feeCalculationUtils.calculateHalfTeamFee(null);
        assertEquals(0, result); // 方法返回0而不是抛异常
    }

    @Test
    @DisplayName("应用折扣 - 正常情况")
    void testApplyDiscount_Normal() {
        // 原价1000，折扣比例0.8（8折）
        Integer result = feeCalculationUtils.applyDiscount(1000, 0.8);
        assertEquals(800, result);
    }

    @Test
    @DisplayName("应用折扣 - 无折扣")
    void testApplyDiscount_NoDiscount() {
        Integer result = feeCalculationUtils.applyDiscount(1000, 1.0);
        assertEquals(1000, result);
    }

    @Test
    @DisplayName("应用折扣 - 折扣比例为null")
    void testApplyDiscount_NullDiscountRatio() {
        Integer result = feeCalculationUtils.applyDiscount(1000, null);
        assertEquals(1000, result); // 返回原始金额而不是抛异常
    }

    @Test
    @DisplayName("应用折扣 - 无效折扣比例")
    void testApplyDiscount_InvalidDiscountRatio() {
        // 负数折扣比例
        Integer result = feeCalculationUtils.applyDiscount(1000, -0.1);
        assertEquals(1000, result); // 返回原始金额
        
        // 超过1的折扣比例
        result = feeCalculationUtils.applyDiscount(1000, 1.1);
        assertEquals(1000, result); // 返回原始金额
    }

    @Test
    @DisplayName("计算退款差额 - 正常情况")
    void testCalculateRefundDifference_Normal() {
        // 初始费用1200，实际费用1000，差额200
        Integer result = feeCalculationUtils.calculateRefundDifference(1200, 1000, 1.0);
        assertEquals(200, result);
    }

    @Test
    @DisplayName("计算退款差额 - 实际费用更高")
    void testCalculateRefundDifference_ActualFeeHigher() {
        // 初始费用1000，实际费用1200，应该返回0
        Integer result = feeCalculationUtils.calculateRefundDifference(1000, 1200, 1.0);
        assertEquals(0, result);
    }

    @Test
    @DisplayName("计算退款差额 - 有折扣")
    void testCalculateRefundDifference_WithDiscount() {
        // 初始费用1200，实际费用1000，折扣0.8，差额=200*0.8=160
        Integer result = feeCalculationUtils.calculateRefundDifference(1200, 1000, 0.8);
        assertEquals(160, result);
    }

    @Test
    @DisplayName("计算退款差额 - 参数为null")
    void testCalculateRefundDifference_NullParams() {
        Integer result = feeCalculationUtils.calculateRefundDifference(null, 1000, 1.0);
        assertEquals(0, result);
        
        result = feeCalculationUtils.calculateRefundDifference(1200, null, 1.0);
        assertEquals(0, result);
    }

    @Test
    @DisplayName("费用参数验证 - 正常情况")
    void testIsValidFeeParameters_Normal() {
        boolean result = feeCalculationUtils.isValidFeeParameters(1000, 10);
        assertTrue(result);
    }

    @Test
    @DisplayName("费用参数验证 - 无效参数")
    void testIsValidFeeParameters_Invalid() {
        // null 参数
        assertFalse(feeCalculationUtils.isValidFeeParameters(null, 10));
        assertFalse(feeCalculationUtils.isValidFeeParameters(1000, null));
        
        // 负数费用
        assertFalse(feeCalculationUtils.isValidFeeParameters(-100, 10));
        
        // 0人数
        assertFalse(feeCalculationUtils.isValidFeeParameters(1000, 0));
        
        // 负数人数
        assertFalse(feeCalculationUtils.isValidFeeParameters(1000, -5));
    }

    @Test
    @DisplayName("计算优惠比例 - 正常情况")
    void testCalculateDiscountRatio_Normal() {
        // 应付1000，实付800，优惠比例0.8
        Double result = feeCalculationUtils.calculateDiscountRatio(1000, 800);
        assertEquals(0.8, result, 0.001);
    }

    @Test
    @DisplayName("计算优惠比例 - 免费")
    void testCalculateDiscountRatio_Free() {
        Double result = feeCalculationUtils.calculateDiscountRatio(1000, 0);
        assertEquals(0.0, result);
    }

    @Test
    @DisplayName("计算优惠比例 - 无优惠")
    void testCalculateDiscountRatio_NoDiscount() {
        Double result = feeCalculationUtils.calculateDiscountRatio(1000, 1000);
        assertEquals(1.0, result);
    }

    @Test
    @DisplayName("计算优惠比例 - 无效参数")
    void testCalculateDiscountRatio_InvalidParams() {
        // null或无效参数应返回1.0（无优惠）
        assertEquals(1.0, feeCalculationUtils.calculateDiscountRatio(null, 800));
        assertEquals(1.0, feeCalculationUtils.calculateDiscountRatio(1000, null));
        assertEquals(1.0, feeCalculationUtils.calculateDiscountRatio(0, 800));
        assertEquals(1.0, feeCalculationUtils.calculateDiscountRatio(1000, -100));
    }
} 