package cn.iocoder.yudao.module.operation.job;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.service.refund.ActivityRefundService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class ActivityGroupingJobTest extends BaseMockitoUnitTest {

    @InjectMocks
    private ActivityGroupingJob activityGroupingJob;

    @Mock
    private ActivityMapper activityMapper;
    @Mock
    private RegistrationMapper registrationMapper;
    @Mock
    private ActivityRefundService activityRefundService;

    @Test
    @DisplayName("执行任务 - 无待处理活动")
    void execute_noActivitiesToProcess() throws Exception {
        // Arrange
        // 目标：测试当没有符合条件的活动（即状态为报名中且报名时间已截止）时，Job执行是否正确处理。
        // 关键准备数据：模拟 activityMapper 在查询时返回一个空列表。
        when(activityMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Act
        // 执行操作：调用 activityGroupingJob 的 execute 方法。
        String result = activityGroupingJob.execute(null);

        // Assert
        // 核心断言：
        // 1. 验证返回的执行结果字符串中，成功和失败处理的活动数量均为0。
        assertTrue(result.contains("成功处理: 0 个"));
        assertTrue(result.contains("失败处理(人数不足): 0 个"));
        // 2. 验证没有调用 registrationMapper 的查询方法（因为没有活动需要处理报名）。
        verify(registrationMapper, never()).selectList(any(), any(), any(), any());
        // 3. 验证没有调用 activityRefundService 的退款方法。
        verify(activityRefundService, never()).processRefund(anyLong(), any(), anyLong(), anyString());
        // 4. 验证没有调用 activityMapper 的更新方法（因为没有活动状态需要变更）。
        verify(activityMapper, never()).updateById(any(ActivityDO.class));
    }

    @Test
    @DisplayName("执行任务 - 排位赛 - 人数不足 (组局失败)")
    void execute_ranking_groupingFailed() throws Exception {
        // Arrange
        // 目标：测试排位赛活动因报名人数未达到最小要求而导致组局失败的场景。
        Long activityId = 1L;
        // 关键准备数据：
        // 1. 创建一个排位赛类型的活动，状态为报名中，设置了最小人数要求。
        ActivityDO activity = new ActivityDO()
                .setId(activityId)
                .setType(ActivityTypeEnum.RANKING.getType())
                .setStatus(ActivityStatusEnum.REGISTRATION.getStatus())
                .setMinPlayersPerGame(10);

        // 2. 创建一个报名列表，其中已支付的报名人数（5人）少于活动要求的最小人数（10人）。
        List<RegistrationDO> paidRegistrations = new ArrayList<>();
        for (long i = 1; i <= 5; i++) { // 只有 5 人报名
            paidRegistrations.add(new RegistrationDO().setId(i).setActivityId(activityId).setStatus(RegistrationStatusEnum.PAID.getStatus()));
        }

        // 3. 模拟 activityMapper 返回上述活动。
        when(activityMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.singletonList(activity));
        // 4. 模拟 registrationMapper 返回上述已支付的报名列表。
        when(registrationMapper.selectList(
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(activityId),
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(RegistrationStatusEnum.PAID.getStatus())
        )).thenReturn(paidRegistrations);
        // 5. 模拟 activityMapper 和 registrationMapper 的更新操作成功。
        when(activityMapper.updateById(any(ActivityDO.class))).thenReturn(1); // Mock update activity success
        when(registrationMapper.updateById(any(RegistrationDO.class))).thenReturn(1); // Mock update registration success

        // Act
        // 执行操作：调用 activityGroupingJob 的 execute 方法。
        String result = activityGroupingJob.execute(null);

        // Assert
        // 核心断言：
        // 1. 验证返回的执行结果字符串中，成功处理为0，失败处理（人数不足）为1。
        assertTrue(result.contains("成功处理: 0 个"));
        assertTrue(result.contains("失败处理(人数不足): 1 个"));

        // 2. 验证活动状态被更新为 CANCELLED (已取消)。
        ArgumentCaptor<ActivityDO> activityUpdateCaptor = ArgumentCaptor.forClass(ActivityDO.class);
        verify(activityMapper).updateById(activityUpdateCaptor.capture());
        assertEquals(activityId, activityUpdateCaptor.getValue().getId());
        assertEquals(ActivityStatusEnum.CANCELLED.getStatus(), activityUpdateCaptor.getValue().getStatus());

        // 3. 验证所有5个报名记录的状态被更新为 CANCELLED，并记录了取消原因。
        ArgumentCaptor<RegistrationDO> regUpdateCaptor = ArgumentCaptor.forClass(RegistrationDO.class);
        verify(registrationMapper, times(5)).updateById(regUpdateCaptor.capture());
        List<RegistrationDO> updatedRegs = regUpdateCaptor.getAllValues();
        assertEquals(5, updatedRegs.size());
        updatedRegs.forEach(reg -> {
            assertEquals(RegistrationStatusEnum.CANCELLED.getStatus(), reg.getStatus());
            assertEquals("活动人数不足，组局失败", reg.getCancelReason());
        });

        // 4. 验证为所有5个报名记录都发起了退款流程，退款原因为"活动组局失败"，场景为 GROUPING_FAILED。
        ArgumentCaptor<Long> refundRegIdCaptor = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<String> refundReasonCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<RefundScenario> refundScenarioCaptor = ArgumentCaptor.forClass(RefundScenario.class);
        verify(activityRefundService, times(5)).processRefund(
                refundRegIdCaptor.capture(),
                refundScenarioCaptor.capture(),
                anyLong(),
                refundReasonCaptor.capture()
        );
        List<Long> refundedRegIds = refundRegIdCaptor.getAllValues();
        assertEquals(5, refundedRegIds.size());
        for(long i=1; i<=5; i++) {
             assertTrue(refundedRegIds.contains(i));
        }
        refundReasonCaptor.getAllValues().forEach(reason -> assertEquals("活动组局失败", reason));
        refundScenarioCaptor.getAllValues().forEach(scenario -> assertEquals(RefundScenario.GROUPING_FAILED, scenario));
    }

    @Test
    @DisplayName("执行任务 - 排位赛 - 人数足够 (组局成功)")
    void execute_ranking_groupingSuccess() throws Exception {
        // Arrange
        // 目标：测试排位赛活动因报名人数达到或超过最小要求而导致组局成功的场景。
        Long activityId = 2L;
        // 关键准备数据：
        // 1. 创建一个排位赛类型的活动，状态为报名中，设置了最小人数要求。
        ActivityDO activity = new ActivityDO()
                .setId(activityId)
                .setType(ActivityTypeEnum.RANKING.getType())
                .setStatus(ActivityStatusEnum.REGISTRATION.getStatus())
                .setMinPlayersPerGame(10);

        // 2. 创建一个报名列表，其中已支付的报名人数（12人）多于活动要求的最小人数（10人）。
        List<RegistrationDO> paidRegistrations = new ArrayList<>();
        for (long i = 1; i <= 12; i++) { // 12 人报名
            paidRegistrations.add(new RegistrationDO().setId(i).setActivityId(activityId).setStatus(RegistrationStatusEnum.PAID.getStatus()));
        }

        // 3. 模拟 activityMapper 返回上述活动。
        when(activityMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.singletonList(activity));
        // 4. 模拟 registrationMapper 返回上述已支付的报名列表。
        when(registrationMapper.selectList(
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(activityId),
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(RegistrationStatusEnum.PAID.getStatus())
        )).thenReturn(paidRegistrations);
        // 5. 模拟 activityMapper 的更新操作成功。
        when(activityMapper.updateById(any(ActivityDO.class))).thenReturn(1); // Mock update activity success

        // Act
        // 执行操作：调用 activityGroupingJob 的 execute 方法。
        String result = activityGroupingJob.execute(null);

        // Assert
        // 核心断言：
        // 1. 验证返回的执行结果字符串中，成功处理为1，失败处理为0。
        assertTrue(result.contains("成功处理: 1 个"));
        assertTrue(result.contains("失败处理(人数不足): 0 个"));

        // 2. 验证活动状态被更新为 GROUPING_SUCCESSFUL (组局成功)。
        ArgumentCaptor<ActivityDO> activityUpdateCaptor = ArgumentCaptor.forClass(ActivityDO.class);
        verify(activityMapper).updateById(activityUpdateCaptor.capture());
        assertEquals(activityId, activityUpdateCaptor.getValue().getId());
        assertEquals(ActivityStatusEnum.GROUPING_SUCCESSFUL.getStatus(), activityUpdateCaptor.getValue().getStatus());

        // 3. 验证没有发起退款流程，也没有更新报名记录的状态为已取消（因为组局成功）。
        verify(activityRefundService, never()).processRefund(anyLong(), any(), anyLong(), anyString());
        verify(registrationMapper, never()).updateById(any(RegistrationDO.class));
    }

    @Test
    @DisplayName("执行任务 - 非排位赛活动 - 跳过处理")
    void execute_skipNonRankingActivity() throws Exception {
        // Arrange
        // 目标：测试当活动类型不是排位赛时，Job是否会跳过该活动的处理。
        Long activityId = 3L;
        // 关键准备数据：创建一个友谊赛类型的活动。
        ActivityDO activity = new ActivityDO()
                .setId(activityId)
                .setType(ActivityTypeEnum.FRIENDLY.getType()) // Friendly match
                .setStatus(ActivityStatusEnum.REGISTRATION.getStatus());

        // 模拟 activityMapper 返回上述活动。
        when(activityMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.singletonList(activity));

        // Act
        // 执行操作：调用 activityGroupingJob 的 execute 方法。
        String result = activityGroupingJob.execute(null);

        // Assert
        // 核心断言：
        // 1. 验证返回的执行结果字符串中，成功和失败处理的活动数量均为0 (因为活动被跳过)。
        // Result might vary depending on how it counts skipped ones,
        // but key is that no core logic was executed
        assertTrue(result.contains("成功处理: 0 个"));
        assertTrue(result.contains("失败处理(人数不足): 0 个"));
        // 2. 验证没有调用 registrationMapper 的查询方法。
        verify(registrationMapper, never()).selectList(any(), any(), any(), any());
        // 3. 验证没有调用 activityMapper 的更新方法。
        verify(activityMapper, never()).updateById(any(ActivityDO.class));
        // 4. 验证没有调用 activityRefundService 的退款方法。
        verify(activityRefundService, never()).processRefund(anyLong(), any(), anyLong(), anyString());
    }
    
    @Test
    @DisplayName("执行任务 - 排位赛 - 最低人数配置错误")
    void execute_ranking_invalidMinPlayersConfig() throws Exception {
         // Arrange
        // 目标：测试当排位赛活动的最小人数配置为0或负数（无效配置）时，Job是否能正确处理，避免后续逻辑执行。
        Long activityId = 4L;
        // 关键准备数据：创建一个排位赛活动，并将其最小人数设置为0。
        ActivityDO activity = new ActivityDO()
                .setId(activityId)
                .setType(ActivityTypeEnum.RANKING.getType())
                .setStatus(ActivityStatusEnum.REGISTRATION.getStatus())
                .setMinPlayersPerGame(0); // Invalid config

        // 准备一个模拟的报名列表，即使配置错误，也假设查询了报名人数。
        List<RegistrationDO> paidRegistrations = Collections.singletonList(new RegistrationDO().setId(1L)); // Have one registration

        // 模拟 activityMapper 返回配置错误的活动。
        when(activityMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.singletonList(activity));
        // 模拟 registrationMapper 返回报名列表。
        // Mock the registration count query even though it might not be strictly needed if config check fails first,
        // but it makes the setup complete.
        when(registrationMapper.selectList(
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(activityId),
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(RegistrationStatusEnum.PAID.getStatus())
        )).thenReturn(paidRegistrations);

        // Act
        // 执行操作：调用 activityGroupingJob 的 execute 方法。
        String result = activityGroupingJob.execute(null);

        // Assert
        // 核心断言：
        // 1. 验证返回的执行结果字符串中，成功和失败处理均为0，因为配置错误导致处理中止。
        // Expecting 0 processed successfully or failed due to player count, because config error stopped it.
        assertTrue(result.contains("成功处理: 0 个"));
        assertTrue(result.contains("失败处理(人数不足): 0 个"));

        // 2. 验证 registrationMapper 的查询方法被调用（用于获取报名人数）。
        verify(registrationMapper).selectList(
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(activityId),
                any(com.baomidou.mybatisplus.core.toolkit.support.SFunction.class), eq(RegistrationStatusEnum.PAID.getStatus())
        ); // Verify count was checked
        // 3. 验证由于配置错误，没有执行活动更新、退款或报名记录更新的操作。
        verify(activityMapper, never()).updateById(any(ActivityDO.class)); 
        verify(activityRefundService, never()).processRefund(anyLong(), any(), anyLong(), anyString());
        verify(registrationMapper, never()).updateById(any(RegistrationDO.class)); 
    }
} 