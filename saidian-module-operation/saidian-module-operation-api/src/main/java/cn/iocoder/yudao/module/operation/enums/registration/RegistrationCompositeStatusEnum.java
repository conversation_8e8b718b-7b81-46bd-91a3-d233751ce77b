package cn.iocoder.yudao.module.operation.enums.registration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报名综合状态枚举
 * 
 * 🎯 目标：将报名状态、支付状态、退款状态、候补状态组合成语义化的综合状态
 * 
 * 📋 优势：
 * 1. 减少复杂的状态组合判断
 * 2. 提供语义化的状态描述
 * 3. 便于前端显示和业务逻辑处理
 * 4. 统一状态判断标准
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RegistrationCompositeStatusEnum {

    // ==================== 正常流程状态 ====================
    
    /**
     * 待支付
     * 条件：status=PENDING_PAYMENT, paymentStatus=UNPAID
     */
    PENDING_PAYMENT(1, "待支付", "用户已创建报名，等待支付"),
    
    /**
     * 已支付（正式）
     * 条件：status=PAID, paymentStatus=PAID, isWaitlist=false, refundStatus=null
     */
    PAID_FORMAL(2, "已支付", "已支付，等待组局确认"),
    
    /**
     * 已支付（候补）
     * 条件：status=PAID, paymentStatus=PAID, isWaitlist=true, refundStatus=null
     */
    PAID_WAITLIST(3, "候补中", "已支付，在候补队列中等待"),
    
    /**
     * 报名成功（正式）
     * 条件：status=SUCCESSFUL, paymentStatus=PAID, isWaitlist=false, refundStatus=null
     */
    SUCCESSFUL_FORMAL(4, "报名成功", "活动组局成功，确定参与"),
    
    /**
     * 已完成
     * 条件：status=COMPLETED, paymentStatus=PAID, refundStatus=null
     */
    COMPLETED(5, "已完成", "活动已结束"),

    // ==================== 退款相关状态 ====================
    
    /**
     * 退款中
     * 条件：refundStatus=REFUNDING
     */
    REFUNDING(10, "退款中", "正在处理退款"),
    
    /**
     * 部分退款
     * 条件：refundStatus=PARTIAL_REFUNDED
     */
    PARTIAL_REFUNDED(11, "部分退款", "已部分退款"),
    
    /**
     * 全额退款
     * 条件：refundStatus=FULL_REFUNDED 或 paymentStatus=REFUND
     */
    FULL_REFUNDED(12, "全额退款", "已全额退款"),
    
    /**
     * 退款失败
     * 条件：refundStatus=FAILURE
     */
    REFUND_FAILED(13, "退款失败", "退款处理失败"),

    // ==================== 取消相关状态 ====================
    
    /**
     * 用户取消
     * 条件：status=CANCELLED
     */
    USER_CANCELLED(20, "已取消", "用户主动取消报名"),
    
    /**
     * 活动取消
     * 条件：status=ACTIVITY_CANCELLED
     */
    ACTIVITY_CANCELLED(21, "活动取消", "活动被取消"),
    
    /**
     * 支付超时
     * 条件：status=PENDING_PAYMENT, paymentStatus=CLOSED
     */
    PAYMENT_TIMEOUT(22, "支付超时", "支付超时自动取消"),

    // ==================== 异常状态 ====================
    
    /**
     * 状态异常
     * 条件：无法匹配上述任何状态组合
     */
    UNKNOWN(99, "状态异常", "状态组合异常，需要人工处理");

    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据报名记录的各个状态字段计算综合状态
     * 
     * @param registrationStatus 报名状态
     * @param paymentStatus 支付状态
     * @param refundStatus 退款状态
     * @param isWaitlist 是否候补
     * @return 综合状态
     */
    public static RegistrationCompositeStatusEnum calculateCompositeStatus(
            Integer registrationStatus, 
            Integer paymentStatus, 
            Integer refundStatus, 
            Boolean isWaitlist) {
        
        // 优先检查退款状态
        if (refundStatus != null) {
            if (RefundStatusEnum.REFUNDING.getStatus().equals(refundStatus)) {
                return REFUNDING;
            }
            if (RefundStatusEnum.FULL_REFUNDED.getStatus().equals(refundStatus)) {
                return FULL_REFUNDED;
            }
            if (RefundStatusEnum.PARTIAL_REFUNDED.getStatus().equals(refundStatus)) {
                return PARTIAL_REFUNDED;
            }
            if (RefundStatusEnum.FAILURE.getStatus().equals(refundStatus)) {
                return REFUND_FAILED;
            }
        }
        
        // 检查支付状态
        if (PayStatusEnum.REFUND.getStatus().equals(paymentStatus)) {
            return FULL_REFUNDED;
        }
        if (PayStatusEnum.CLOSED.getStatus().equals(paymentStatus)) {
            return PAYMENT_TIMEOUT;
        }
        
        // 检查报名状态
        if (registrationStatus == null) {
            return UNKNOWN;
        }
        
        if (RegistrationStatusEnum.PENDING_PAYMENT.getStatus().equals(registrationStatus)) {
            return PENDING_PAYMENT;
        }
        
        if (RegistrationStatusEnum.CANCELLED.getStatus().equals(registrationStatus)) {
            return USER_CANCELLED;
        }
        
        if (RegistrationStatusEnum.ACTIVITY_CANCELLED.getStatus().equals(registrationStatus)) {
            return ACTIVITY_CANCELLED;
        }
        
        if (RegistrationStatusEnum.COMPLETED.getStatus().equals(registrationStatus)) {
            return COMPLETED;
        }
        
        // 检查已支付状态
        if (RegistrationStatusEnum.PAID.getStatus().equals(registrationStatus) &&
            PayStatusEnum.PAID.getStatus().equals(paymentStatus)) {
            return Boolean.TRUE.equals(isWaitlist) ? PAID_WAITLIST : PAID_FORMAL;
        }
        
        // 检查报名成功状态
        if (RegistrationStatusEnum.SUCCESSFUL.getStatus().equals(registrationStatus) &&
            PayStatusEnum.PAID.getStatus().equals(paymentStatus)) {
            return SUCCESSFUL_FORMAL;
        }
        
        // 无法匹配的状态组合
        return UNKNOWN;
    }

    /**
     * 判断是否为有效的报名状态（可参与活动）
     */
    public boolean isActive() {
        return this == PAID_FORMAL || this == SUCCESSFUL_FORMAL || this == COMPLETED;
    }

    /**
     * 判断是否为候补状态
     */
    public boolean isWaitlist() {
        return this == PAID_WAITLIST;
    }

    /**
     * 判断是否可以退款
     */
    public boolean canRefund() {
        return this == PAID_FORMAL || this == PAID_WAITLIST || 
               this == SUCCESSFUL_FORMAL || this == COMPLETED ||
               this == PARTIAL_REFUNDED;
    }

    /**
     * 判断是否正在退款中
     */
    public boolean isRefunding() {
        return this == REFUNDING;
    }

    /**
     * 判断是否已退款
     */
    public boolean isRefunded() {
        return this == FULL_REFUNDED || this == PARTIAL_REFUNDED;
    }

    /**
     * 判断是否为最终状态（不会再变化）
     */
    public boolean isFinalStatus() {
        return this == FULL_REFUNDED || this == USER_CANCELLED || 
               this == ACTIVITY_CANCELLED || this == PAYMENT_TIMEOUT ||
               this == COMPLETED;
    }

    /**
     * 判断是否需要在App端显示
     */
    public boolean shouldDisplayInApp() {
        // 用户主动取消的不显示，其他都显示
        return this != USER_CANCELLED;
    }

    /**
     * 判断是否可以参与分队
     */
    public boolean canAssignTeam() {
        return this == PAID_FORMAL || this == SUCCESSFUL_FORMAL;
    }
}
