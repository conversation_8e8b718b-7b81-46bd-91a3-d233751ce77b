package cn.iocoder.yudao.module.operation.enums.registration; // Target package in api module

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 报名状态枚举
 * 
 * 状态流转说明：
 * 1. PENDING_PAYMENT → PAID (支付成功)
 * 2. PAID → SUCCESSFUL (组局成功，活动确定举行)
 * 3. PAID → CANCELLED (用户主动取消)
 * 4. PAID → ACTIVITY_CANCELLED (活动层面取消：组局失败、管理员取消等)
 * 5. SUCCESSFUL → COMPLETED (活动结束)
 * 6. PAID → WAIT_LIST (人数已满，进入候补)
 * 
 * 关键业务规则：
 * - 只有PAID状态允许用户主动退赛
 * - SUCCESSFUL状态表示活动确定举行，不允许退赛
 * - ACTIVITY_CANCELLED状态用于区分活动层面的取消，需要在App端显示报名记录
 * - 差额退款基于PAID→SUCCESSFUL状态转换时计算
 * 
 * 参考 activity_registration_design.md
 */
@Getter
@AllArgsConstructor
public enum RegistrationStatusEnum implements IntArrayValuable {

    /**
     * 待支付
     * 用户刚创建报名记录，尚未完成支付
     */
    PENDING_PAYMENT(1, "待支付"),
    
    /**
     * 已支付
     * 用户已完成支付，但活动尚未组局确认
     * 特点：
     * - 排位赛：已分队但活动可能因人数不足取消
     * - 友谊赛：等待足够队伍报名
     * - 联赛：等待足够队伍参与
     * - 允许用户主动退赛
     * - 活动层面取消时自动进入ACTIVITY_CANCELLED状态
     */
    PAID(2, "已支付"),
    
    /**
     * 报名成功
     * 活动组局成功，用户报名最终确认
     * 特点：
     * - 活动确定举行，不会因人数不足取消
     * - 不允许用户主动退赛（业务规则）
     * - 可能触发差额退款（基于最终参与人数）
     * - 是数据统计的重要分界点
     * 
     * 对应活动状态：GROUPING_SUCCESS
     */
    SUCCESSFUL(3, "报名成功"),
    
    /**
     * 已取消
     * 仅用于用户主动取消的情况：
     * - 未支付超时自动取消
     * - 用户主动取消（PAID状态下）
     * 
     * 特点：不在App端显示，因为用户自己知道取消原因
     */
    CANCELLED(4, "已取消"),
    
    /**
     * 已完成
     * 活动正常结束后的最终状态
     */
    COMPLETED(5, "已完成"),
    
    /**
     * 候补中
     * 用户已支付但活动人数已满，等待空位
     * 特点：
     * - 已完成支付，资金冻结
     * - 有其他用户退赛时自动补位
     * - 补位成功后转为PAID状态
     * - 活动开始前未补位成功则自动退款
     */
    WAIT_LIST(6, "候补中"),
    
    /**
     * 活动取消
     * 活动层面的取消，非用户主动：
     * - 组局失败（人数不足）
     * - 管理员强制取消活动
     * - 活动因其他原因取消
     * 
     * 特点：
     * - 需要在App端显示报名记录，让用户了解活动取消原因
     * - 系统会自动处理退款
     * - 显示基本报名信息，帮助用户理解情况
     */
    ACTIVITY_CANCELLED(9, "活动取消");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(RegistrationStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名称
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据状态值获取状态名称
     * @param status 状态值
     * @return 状态名称
     */
    public static String getNameByStatus(Integer status) {
        if (status == null) {
            return "";
        }
        for (RegistrationStatusEnum enumValue : values()) {
            if (enumValue.getStatus().equals(status)) {
                return enumValue.getName();
            }
        }
        return "未知状态";
    }
    
    /**
     * 判断是否允许退赛
     * 业务规则：只有PAID状态允许用户主动退赛
     * @param status 当前状态
     * @return 是否允许退赛
     */
    public static boolean canWithdraw(Integer status) {
        return PAID.getStatus().equals(status);
    }
    
    /**
     * 判断是否为有效报名状态（已支付或报名成功）
     * 用于统计有效报名人数
     * @param status 当前状态
     * @return 是否为有效报名
     */
    public static boolean isValidRegistration(Integer status) {
        return PAID.getStatus().equals(status) || SUCCESSFUL.getStatus().equals(status);
    }
    
    /**
     * 判断是否为最终成功状态（活动确定举行）
     * 用于数据统计和业务判断
     * @param status 当前状态
     * @return 是否为最终成功状态
     */
    public static boolean isFinalSuccess(Integer status) {
        return SUCCESSFUL.getStatus().equals(status) || COMPLETED.getStatus().equals(status);
    }
    
    /**
     * 判断是否为需要在App端显示的报名记录
     * 活动层面取消的报名需要显示，让用户了解取消原因
     * @param status 当前状态
     * @return 是否应该显示报名记录
     */
    public static boolean shouldDisplayInApp(Integer status) {
        return !CANCELLED.getStatus().equals(status); // 只有用户主动取消的不显示
    }
    
    /**
     * 判断是否为活动层面的取消
     * @param status 当前状态
     * @return 是否为活动取消
     */
    public static boolean isActivityCancelled(Integer status) {
        return ACTIVITY_CANCELLED.getStatus().equals(status);
    }
    
    /**
     * 判断是否为取消状态（包含所有取消类型）
     * @param status 当前状态
     * @return 是否为取消状态
     */
    public static boolean isCancelledStatus(Integer status) {
        return CANCELLED.getStatus().equals(status) || ACTIVITY_CANCELLED.getStatus().equals(status);
    }
} 