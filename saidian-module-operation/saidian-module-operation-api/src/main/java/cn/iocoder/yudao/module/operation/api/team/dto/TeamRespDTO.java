package cn.iocoder.yudao.module.operation.api.team.dto;

import lombok.Data;

import java.time.LocalDate;

@Data
public class TeamRespDTO {
    /**
     * ID
     */
    private Long id;
    /**
     * 球队名称
     */
    private String name;
    /**
     * 球队简介
     */
    private String description;
    /**
     * 球队简称
     */
    private String shortName;
    /**
     * 成立时间
     */
    private LocalDate founded;
    /**
     * 城市
     */
    private String city;
    /**
     * 球队logo
     */
    private String logo;
    /**
     * 主教练id
     */
    private Long coach;
    /**
     * 球队类型：1。临时球队。2。正式球队
     */
    private Integer type;

}
