package cn.iocoder.yudao.module.operation.api.league.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LeagueRegistrationDTO {
    /**
     * 联赛ID
     */
    private Long leagueId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 账单id
     */
    private Long orderId;
    /**
     * 报名球队ID
     */
    private Long teamId;

    /**
     * 支付方式
     */
    private Integer payMode;

    private Integer payPrice;

    private Integer refundStatus;
    /**
     * 是否垫付人
     */
    private Boolean coverPayer;
}
