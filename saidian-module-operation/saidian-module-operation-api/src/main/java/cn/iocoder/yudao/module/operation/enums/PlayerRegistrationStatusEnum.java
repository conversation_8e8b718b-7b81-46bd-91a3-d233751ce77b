package cn.iocoder.yudao.module.operation.enums;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 匹配模式枚举
 */
@Getter
@AllArgsConstructor
public enum PlayerRegistrationStatusEnum {

    SUCCESS(1, "报名成功"),
    CANCELED(2, "已取消"),
    ;


    /**
     * 类型
     */
    private final Integer status;
    /**
     * 状态
     */
    private final String type;

    public static PlayerRegistrationStatusEnum getByStatus(Integer status) {
        return EnumUtil.getBy(PlayerRegistrationStatusEnum.class,
                e -> Objects.equals(status, e.getStatus()));
    }

}
