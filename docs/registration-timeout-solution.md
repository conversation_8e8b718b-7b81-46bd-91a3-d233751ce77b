# 报名超时处理解决方案

## 问题描述

当用户超时未支付时，系统存在以下问题：
1. 支付订单状态已变为 `CLOSED(30, "支付关闭")`
2. 但报名记录状态仍为 `PENDING_PAYMENT(1, "待支付")`
3. 支付状态仍为 `UNPAID(1, "未支付")`
4. 导致用户无法重新报名，因为系统认为该用户还有未完成的报名记录

## 解决方案

### 1. 核心思路

通过定时任务检查支付订单状态，当发现支付订单已关闭但报名记录仍为待支付状态时，自动将报名记录状态更新为已取消。

### 2. 实现组件

#### 2.1 定时任务类
- **文件**: `RegistrationTimeoutJob.java`
- **功能**: 定期执行超时报名记录的处理
- **执行频率**: 每10分钟执行一次

#### 2.2 服务方法
- **接口**: `RegistrationService.cancelTimeoutRegistrations()`
- **实现**: `RegistrationServiceImpl.cancelTimeoutRegistrations()`
- **功能**: 
  1. 查询所有待支付状态的报名记录
  2. 检查对应的支付订单状态
  3. 如果支付订单已关闭，则更新报名记录为已取消

#### 2.3 数据库配置
- **文件**: `registration_timeout_job.sql`
- **功能**: 在 `system_job` 表中配置定时任务

### 3. 处理流程

```mermaid
flowchart TD
    A[定时任务启动] --> B[查询待支付报名记录]
    B --> C{是否有待支付记录?}
    C -->|否| D[结束任务]
    C -->|是| E[遍历每个报名记录]
    E --> F[检查支付订单状态]
    F --> G{支付订单是否已关闭?}
    G -->|否| H[跳过该记录]
    G -->|是| I[更新报名状态为已取消]
    I --> J[记录操作日志]
    J --> K[处理下一条记录]
    H --> K
    K --> L{还有记录?}
    L -->|是| E
    L -->|否| M[返回处理结果]
```

### 4. 状态变更

#### 4.1 报名记录状态变更
- **变更前**: `status = PENDING_PAYMENT(1)`, `payment_status = UNPAID(1)`
- **变更后**: `status = CANCELLED(4)`, `payment_status = UNPAID(1)`
- **取消原因**: "支付超时自动取消"

#### 4.2 支付订单状态
- **保持不变**: 支付订单状态已由支付模块的 `expireOrder()` 方法处理为 `CLOSED(30)`

### 5. 关键代码

#### 5.1 定时任务配置
```sql
INSERT INTO `system_job` (
    `name`, `status`, `handler_name`, `cron_expression`
) VALUES (
    '报名超时处理任务', 1, 'registrationTimeoutJob', '0 */10 * * * ?'
);
```

#### 5.2 核心处理逻辑
```java
@Override
@Transactional(rollbackFor = Exception.class)
public int cancelTimeoutRegistrations() {
    // 1. 查询待支付报名记录
    List<RegistrationDO> pendingRegistrations = registrationMapper.selectList(
        new LambdaQueryWrapper<RegistrationDO>()
            .eq(RegistrationDO::getStatus, RegistrationStatusEnum.PENDING_PAYMENT.getStatus())
            .eq(RegistrationDO::getPaymentStatus, PayStatusEnum.UNPAID.getStatus())
            .isNotNull(RegistrationDO::getPayOrderId)
    );
    
    // 2. 检查支付订单状态并更新报名记录
    for (RegistrationDO registration : pendingRegistrations) {
        if (isPayOrderClosed(registration.getPayOrderId())) {
            updateRegistrationToTimeout(registration);
        }
    }
}
```

### 6. 优势

1. **自动化处理**: 无需人工干预，系统自动处理超时情况
2. **数据一致性**: 确保报名状态与支付状态保持一致
3. **用户体验**: 用户可以重新报名，不会被卡在待支付状态
4. **可监控**: 提供详细的日志记录，便于问题排查
5. **安全性**: 只处理确实已关闭的支付订单，避免误操作

### 7. 注意事项

1. **执行频率**: 设置为每10分钟执行一次，平衡及时性和系统负载
2. **事务处理**: 使用事务确保数据一致性
3. **异常处理**: 对每个记录单独处理，避免单个记录异常影响整体任务
4. **日志记录**: 详细记录处理过程，便于问题排查和监控

### 8. 部署步骤

1. **代码部署**: 部署包含新定时任务的代码
2. **数据库配置**: 执行 `registration_timeout_job.sql` 脚本
3. **任务启动**: 在管理后台启动定时任务
4. **监控验证**: 观察任务执行日志，确认正常运行

### 9. 测试验证

1. **创建测试报名**: 创建一个报名记录但不支付
2. **等待支付超时**: 等待支付订单自动过期
3. **验证状态更新**: 确认报名记录状态被正确更新为已取消
4. **验证重新报名**: 确认用户可以重新报名同一活动

## 10. 实现改进

### 10.1 报名超时处理改进

**新增处理场景**:
- 支付订单不存在的报名记录也会被取消
- 查询支付订单异常时不会误操作（保持原状态）

```java
// 改进后的检查逻辑
private PaymentOrderCheckResult checkPaymentOrderStatus(Long payOrderId) {
    if (payOrder == null) {
        return new PaymentOrderCheckResult(true, "支付订单不存在");
    }
    if (isClosed) {
        return new PaymentOrderCheckResult(true, "支付超时自动取消");
    }
    return new PaymentOrderCheckResult(false, null);
}
```

### 10.2 ActivityGroupingJob 完善

**状态流转优化** (符合设计文档):
1. `REGISTRATION` → `GROUPING` (开始组局检查)
2. `GROUPING` → `GROUPING_SUCCESS` → `IN_PROGRESS` (组局成功)
3. `GROUPING` → `GROUPING_FAILED` → `CANCELLED` (组局失败)

**新增功能**:
- 严格按照设计文档的状态流转执行
- 报名记录状态同步更新 (`PAID` → `SUCCESSFUL`)
- 完善的异常处理和状态回滚
- 详细的日志记录

```java
// 状态流转示例
// 1. 先更新为组局中
activityMapper.updateById(new ActivityDO().setId(activity.getId())
    .setStatus(ActivityStatusEnum.GROUPING.getStatus()));

// 2. 根据结果决定后续状态
if (人数不足) {
    // GROUPING → GROUPING_FAILED → CANCELLED
    handleGroupingFailure(activity, registrations);
} else {
    // GROUPING → GROUPING_SUCCESS → IN_PROGRESS  
    handleGroupingSuccess(activity);
}
```

## 改进项：事务控制与比赛记录创建

## 事务控制增强

为确保活动组局过程中的数据一致性，对 `ActivityGroupingJob` 进行了以下改进：

### 1. 事务方法分离
- `handleGroupingSuccess()` 和 `handleGroupingFailure()` 方法添加了 `@Transactional` 注解
- 使用 `rollbackFor = Exception.class` 确保任何异常都会回滚事务
- 通过 `getSelf()` 方法获取代理对象，确保事务注解生效

### 2. 异常处理增强
在 `handleGroupingSuccess()` 方法中：
- 比赛记录创建失败时抛出运行时异常，触发事务回滚
- 活动状态更新失败时抛出运行时异常，触发事务回滚
- 确保所有操作在同一事务中，避免部分成功部分失败的情况

## 比赛记录创建完成

### 实现内容
完成了 TODO 项目：调用比赛服务创建比赛记录，更新 activity.game_id

```java
// 5. 创建比赛记录
Long gameId = gameService.createRankingGameFromActivity(activity);
if (gameId != null) {
    // 更新活动的比赛ID关联
    activityMapper.updateById(new ActivityDO().setId(activity.getId()).setGameId(gameId));
    log.info("[handleGroupingSuccess][活动 id={}] 比赛记录创建成功，gameId={}", activity.getId(), gameId);
} else {
    log.error("[handleGroupingSuccess][活动 id={}] 比赛记录创建失败", activity.getId());
    throw new RuntimeException("比赛记录创建失败");
}
```

### 功能特点
1. **游戏创建**：调用 `GameService.createRankingGameFromActivity()` 创建排位赛
2. **关联更新**：更新活动表的 `game_id` 字段，建立活动与比赛的关联
3. **异常处理**：创建失败时抛出异常，触发事务回滚
4. **日志记录**：完整记录创建过程和结果

### 数据完整性保障
- 比赛记录创建和活动更新在同一事务中
- 任一步骤失败都会触发完整回滚
- 确保活动与比赛数据的一致性

## 改进后的事务流程

### 组局成功流程 (单事务)
1. 更新活动状态：GROUPING → GROUPING_SUCCESS
2. 执行分队逻辑
3. 创建比赛记录
4. 更新活动的 game_id
5. 更新报名记录状态：PAID → SUCCESSFUL  
6. 更新活动状态：GROUPING_SUCCESS → IN_PROGRESS

### 组局失败流程 (单事务)
1. 更新活动状态：GROUPING → GROUPING_FAILED
2. 更新报名记录状态：PAID → CANCELLED
3. 处理退款逻辑
4. 更新活动状态：GROUPING_FAILED → CANCELLED

所有操作在单个事务中执行，确保数据一致性和完整性。

## 新增功能：退款异步处理与比赛球员管理

### 1. 退款异步处理机制

#### 问题分析
原有的退款逻辑是同步的，组局失败时立即更新活动状态为已取消。但实际上退款是异步的，正确的流程应该是等待所有退款完成后再更新活动状态。

#### 解决方案

**新增活动状态**：
- 添加 `REFUNDING(9, "退款中")` 状态

**修改组局失败流程**：
1. 活动状态：GROUPING → GROUPING_FAILED → REFUNDING
2. 发起所有报名记录的退款处理
3. 等待退款回调完成
4. 通过定时任务检查退款完成状态
5. 所有退款完成后：REFUNDING → CANCELLED

**新增定时任务**：
- `ActivityRefundCompletionJob`：每5分钟检查退款中的活动
- 检查所有相关报名的退款状态
- 当所有退款完成（成功或失败）时，更新活动状态为已取消

### 2. 比赛球员管理

#### 问题分析
1. 组局成功后创建比赛记录，但没有添加参赛球员
2. 后续报名的用户支付成功后，也需要添加到比赛名单中

#### 解决方案

**组局成功时添加球员**：
- 在 `ActivityGroupingJob.handleGroupingSuccess()` 中添加 `addPlayersToGame()` 方法
- 根据分队结果将主队和客队球员添加到比赛的 `PlayerGameRelated` 表中

**后续报名添加球员**：
- 在 `PendingPaymentState.handlePaySuccess()` 中添加 `addPlayerToGameIfNeeded()` 方法
- 当活动状态为 `IN_PROGRESS` 且有比赛记录时，自动将新支付成功的球员添加到比赛中
- 根据报名记录的 `teamAssigned` 字段确定球员所属队伍

#### 技术实现
```java
// 组局成功时添加球员
private void addPlayersToGame(Long gameId, List<RegistrationDO> registrations, TeamAssignmentResult assignmentResult) {
    // 处理主队球员
    for (PlayerRegistrationInfo playerInfo : assignmentResult.getHomeTeamPlayers()) {
        PlayerDO player = playerInfo.getPlayer();
        Long assignedTeamId = playerInfo.getAssignedTeam();
        gameService.addPlayerToGame(gameId, assignedTeamId, player);
    }
    // 处理客队球员
    for (PlayerRegistrationInfo playerInfo : assignmentResult.getGuestTeamPlayers()) {
        PlayerDO player = playerInfo.getPlayer();
        Long assignedTeamId = playerInfo.getAssignedTeam();
        gameService.addPlayerToGame(gameId, assignedTeamId, player);
    }
}

// 后续报名添加球员
private void addPlayerToGameIfNeeded(RegistrationDO registration, ActivityDO activity) {
    if (ActivityStatusEnum.IN_PROGRESS.getStatus().equals(activity.getStatus()) && activity.getGameId() != null) {
        PlayerDO player = playerService.getPlayerByUserId(registration.getUserId());
        Long assignedTeamId = registration.getTeamAssigned();
        gameService.addPlayerToGame(activity.getGameId(), assignedTeamId, player);
    }
}
```

### 3. 数据库配置

**定时任务配置**：
```sql
INSERT INTO system_job (
    name, status, handler_name, cron_expression, ...
) VALUES (
    '活动退款完成检查任务', 1, 'activityRefundCompletionJob', '0 */5 * * * ?', ...
);
```

### 4. 业务流程图

#### 退款异步处理流程
```mermaid
graph TD
    A[组局失败] --> B[更新活动状态: GROUPING_FAILED]
    B --> C[更新活动状态: REFUNDING]
    C --> D[发起所有报名退款]
    D --> E[等待退款回调]
    E --> F[定时任务检查退款状态]
    F --> G{所有退款完成?}
    G -->|是| H[更新活动状态: CANCELLED]
    G -->|否| F
```

#### 比赛球员管理流程
```mermaid
graph TD
    A[组局成功] --> B[创建比赛记录]
    B --> C[添加分队球员到比赛]
    C --> D[活动状态: IN_PROGRESS]
    
    E[后续用户报名] --> F[支付成功]
    F --> G{活动是否进行中?}
    G -->|是| H[自动添加球员到比赛]
    G -->|否| I[正常处理支付成功]
```

### 5. 关键改进点

1. **数据一致性**：退款异步处理确保活动状态与实际退款状态一致
2. **比赛完整性**：确保比赛记录包含所有参赛球员信息
3. **用户体验**：后续报名用户无需手动操作即可参与比赛
4. **系统健壮性**：通过定时任务和异常处理确保系统稳定运行
5. **业务合规性**：严格按照退款完成状态更新活动状态，符合业务规范

## 总结

现在我们有了一个完整的解决方案：

### 🎯 解决的问题
1. **支付超时**: 用户超时未支付无法重新报名
2. **状态不一致**: 支付状态与报名状态不同步
3. **状态流转**: 活动组局流程不符合设计文档

### 💡 解决方案特点
1. **全面覆盖**: 处理支付超时、订单丢失等异常情况
2. **状态一致**: 确保活动状态、报名状态、支付状态同步
3. **设计规范**: 严格按照设计文档的状态流转逻辑
4. **可靠性**: 完善的异常处理和状态回滚机制

### 📁 涉及文件
1. `RegistrationTimeoutJob.java` - 报名超时处理
2. `ActivityGroupingJob.java` - 活动组局处理  
3. `RegistrationServiceImpl.java` - 支付检查逻辑
4. `registration_timeout_job.sql` - 定时任务配置

### ✅ 业务价值
- **用户体验**: 用户可以正常重新报名
- **数据质量**: 确保系统数据一致性
- **运营效率**: 减少人工处理异常情况
- **系统稳定**: 符合设计规范，降低维护成本

这个解决方案不仅解决了当前的问题，还完善了整个活动报名的状态管理体系，为后续功能扩展奠定了良好基础。 