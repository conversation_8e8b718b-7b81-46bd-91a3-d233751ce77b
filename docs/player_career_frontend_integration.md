# 球员生涯模块前端API集成文档

## 1. 概述

本文档记录球员生涯模块前端与后端API的集成实现，包括雷达图数据的动态获取和最佳数据的实时展示。

## 2. 接口集成

### 2.1 API接口封装

在 `saidian-app-ui/sheep/api/operation/player.js` 中新增了两个API方法：

```javascript
// 获取球员生涯概览数据（核心数据网格+雷达图）
getPlayerCareerOverview: (params) => {
  return request({
    url: '/league/player/career/overview',
    method: 'GET',
    params,
    custom: {
      showLoading: false,
      auth: true,
    },
  });
},

// 获取球员最佳数据记录
getPlayerBestStats: (params) => {
  return request({
    url: '/league/player/career/best-stats',
    method: 'GET',
    params,
    custom: {
      showLoading: false,
      auth: true,
    },
  });
},
```

### 2.2 组件参数调整

**PlayerOverview组件**支持两种属性传值方式：
- `playerId`: Number类型，球员ID
- `player-id`: Number类型，兼容kebab-case命名

```vue
<!-- 使用方式1 -->
<player-overview :playerId="1001" />

<!-- 使用方式2（当前使用） -->
<player-overview :player-id="currentPlayerInfo.playerId" />
```

## 3. 功能实现

### 3.1 雷达图数据动态获取

**触发条件**：
- 组件初始化时
- 切换赛季时
- 切换比赛类型时

**实现逻辑**：
```javascript
async function updateRadarData() {
  if (!currentPlayerId.value) {
    console.warn('playerId is required');
    return;
  }

  loading.value = true;

  try {
    const season = seasons.value[seasonIndex.value].value;
    const gameType = matchTypes.value[matchTypeIndex.value].value;
    
    const response = await PlayerApi.getPlayerCareerOverview({
      playerId: currentPlayerId.value,
      season,
      gameType
    });

    if (response.code === 0 && response.data) {
      const { radarChart } = response.data;
      
      if (radarChart && radarChart.dataPoints) {
        // 7维度数据映射到6维度雷达图
        const dimensionMap = {
          '效率': 'points',
          '得分': 'points', 
          '篮板': 'rebounds',
          '助攻': 'assists',
          '防守': 'steals',
          '失误控制': 'blocks',
          '犯规控制': 'threePointers'
        };

        radarChart.dataPoints.forEach(point => {
          const field = dimensionMap[point.dimension];
          if (field) {
            radarData[field] = point.score.toFixed(1);
          }
        });
      }
    }
  } catch (error) {
    console.error('获取生涯概览数据失败：', error);
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}
```

### 3.2 最佳数据动态获取

**触发条件**：
- 组件初始化时
- 切换赛季最佳/生涯最佳时
- 切换赛季或比赛类型时

**实现逻辑**：
```javascript
async function loadBestStats() {
  if (!currentPlayerId.value) {
    console.warn('playerId is required');
    return;
  }

  bestDataLoading.value = true;

  try {
    const season = seasons.value[seasonIndex.value].value;
    const gameType = matchTypes.value[matchTypeIndex.value].value;
    
    const response = await PlayerApi.getPlayerBestStats({
      playerId: currentPlayerId.value,
      season,
      gameType
    });

    if (response.code === 0 && response.data) {
      const { seasonBest, careerBest } = response.data;
      
      // 更新赛季最佳数据
      if (seasonBest && seasonBest.length > 0) {
        seasonBest.forEach(item => {
          // 根据statName映射到对应的数据字段
          if (item.statName === '单场最高得分') {
            bestData.current.points = item.value;
            bestData.current.pointsDate = formatDate(item.achieveDate);
          }
          // ... 其他数据项
        });
      }
      
      // 更新生涯最佳数据
      if (careerBest && careerBest.length > 0) {
        careerBest.forEach(item => {
          // 类似的映射逻辑
        });
      }
    }
  } catch (error) {
    console.error('获取最佳数据失败：', error);
  } finally {
    bestDataLoading.value = false;
  }
}
```

## 4. 数据映射关系

### 4.1 赛季/比赛类型映射

```javascript
// 赛季数据
const seasons = ref([
  { name: '25赛季', value: '2025' },
  { name: '24赛季', value: '2024' },
  { name: '23赛季', value: '2023' },
  { name: '22赛季', value: '2022' },
  { name: '21赛季', value: '2021' },
]);

// 比赛类型数据
const matchTypes = ref([
  { name: '全部比赛', value: 0 },
  { name: '排位赛', value: 1 },
  { name: '友谊赛', value: 2 },
  { name: '联赛', value: 3 },
]);
```

### 4.2 雷达图维度映射

后端返回7个维度，前端雷达图显示6个维度，映射关系如下：

| 后端维度 | 前端雷达图位置 | 说明 |
|----------|----------------|------|
| 效率 | 场均得分 | 主要映射 |
| 得分 | 场均得分 | 备选映射 |
| 篮板 | 场均篮板 | 直接映射 |
| 助攻 | 场均助攻 | 直接映射 |
| 防守 | 场均抢断 | 防守能力体现 |
| 失误控制 | 场均盖帽 | 控制能力体现 |
| 犯规控制 | 场均三分 | 技术能力体现 |

## 5. 用户体验优化

### 5.1 加载状态

- **雷达图加载**：显示半透明遮罩和加载动画
- **最佳数据加载**：防止重复请求，静默加载

### 5.2 错误处理

- **网络错误**：显示Toast提示"网络错误，请重试"
- **数据错误**：显示Toast提示"数据获取失败"
- **参数错误**：控制台警告，不影响用户使用

### 5.3 性能优化

- **防重复请求**：通过loading状态防止重复调用
- **数据缓存**：API层面的缓存策略（15分钟雷达图，1小时最佳数据）
- **按需加载**：最佳数据只在需要时加载

## 6. 测试说明

### 6.1 测试接口

```bash
# 测试雷达图数据接口
GET /league/player/career/overview?playerId=1001&season=2024&gameType=0

# 测试最佳数据接口  
GET /league/player/career/best-stats?playerId=1001&season=2024&gameType=0
```

### 6.2 测试场景

1. **正常流程测试**：
   - 组件加载时自动获取数据
   - 切换赛季时数据更新
   - 切换比赛类型时数据更新
   - 切换最佳数据类型时数据更新

2. **异常流程测试**：
   - 网络超时处理
   - 接口返回错误处理
   - 数据格式异常处理
   - playerId参数缺失处理

3. **性能测试**：
   - 快速切换赛季/类型时的防抖处理
   - 重复请求的防护
   - 加载状态的及时响应

## 7. 后续优化计划

### 7.1 短期优化
- ✅ Mock数据验证接口设计
- 🔄 真实Service业务逻辑实现
- ⏳ 增加更多错误处理场景
- ⏳ 优化数据映射逻辑

### 7.2 长期优化
- ⏳ 增加数据缓存机制
- ⏳ 支持更多雷达图维度配置
- ⏳ 添加数据统计分析功能
- ⏳ 实现离线数据支持

## 8. 总结

通过本次API集成，实现了：

1. **前后端数据联通**：雷达图和最佳数据可根据用户选择动态获取
2. **用户体验提升**：加载状态明确，错误处理完善
3. **架构设计优化**：混合接口方案平衡了性能和灵活性
4. **代码质量提升**：统一的错误处理和数据映射逻辑

下一步将重点实现后端真实的Service业务逻辑，完善整个球员生涯模块的功能闭环。 