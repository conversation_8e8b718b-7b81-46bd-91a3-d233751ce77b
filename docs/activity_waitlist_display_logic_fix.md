# 活动满员逻辑修复文档

## 问题描述

用户反馈了三个关键问题：

1. **活动卡片候补显示逻辑错误**：
   - 当前：友谊赛基于报名支付成功的球员超过最大人数来判断是否显示候补
   - 应该：基于报名成功的球队是否超过最大队伍数来判断
   - 关键：球队只有在报名人数达到最小要求时才算"报名成功"

2. **活动详情页报名按钮逻辑问题**：
   - 同样需要使用正确的球队满员判断逻辑

3. **详情页提示语显示问题**：
   - 组局时间到了且组局成功，应显示"组局成功"而不是"截止"

## 解决方案

### 1. 后端修改

#### 1.1 修改 `buildSignedUpTeams` 方法

**文件**: `ActivityServiceImpl.java`

**原始逻辑**: 简单返回所有有报名记录的球队
**修改后**: 只返回满足最小人数要求的球队

```java
private List<AppActivityRespVO.TeamSignupInfoVO> buildSignedUpTeams(ActivityDO activityDO,
                                                                    Map<Long, List<RegistrationDO>> teamRegistrationsMap) {
    // 获取活动类型对应的最小人数要求
    Integer minPlayersRequired = getMinPlayersPerTeam(activityDO);
    
    for (Map.Entry<Long, List<RegistrationDO>> entry : teamRegistrationsMap.entrySet()) {
        // 统计该球队已支付的球员数量
        long paidPlayersCount = teamRegs.stream()
                .filter(this::isActiveRegistration)
                .count();
        
        // 只有当已支付球员数量达到最小要求时，才算作报名成功的球队
        if (paidPlayersCount >= minPlayersRequired) {
            // 添加到已报名成功球队列表
        }
    }
}
```

#### 1.2 新增 `getMinPlayersPerTeam` 方法

```java
private Integer getMinPlayersPerTeam(ActivityDO activityDO) {
    if (Objects.equals(activityDO.getType(), ActivityTypeEnum.FRIENDLY.getType())) {
        // 友谊赛：默认6人
        return activityDO.getMinPlayersPerTeamFriendly() != null ? 
               activityDO.getMinPlayersPerTeamFriendly() : 6;
    } else if (Objects.equals(activityDO.getType(), ActivityTypeEnum.LEAGUE.getType())) {
        // 联赛：默认8人
        return activityDO.getMinPlayersPerTeamLeague() != null ? 
               activityDO.getMinPlayersPerTeamLeague() : 8;
    }
    return 5; // 默认值
}
```

#### 1.3 修改活动卡片相关方法

**修改 `teamCounts` 方法**：使用与 `buildSignedUpTeams` 相同的逻辑
**修改 `batchQueryTeamCounts` 方法**：批量查询时也考虑最小人数要求
**新增 `fillTeamInfoForCard` 方法**：为活动卡片提供完整的球队信息

#### 1.4 扩展活动卡片 VO

**文件**: `AppActivityCardResponseVO.java`

```java
@Schema(description = "最大球队数（友谊赛/联赛）")
private Integer maxTeams;

@Schema(description = "已报名成功的球队列表（友谊赛/联赛用）")
private java.util.List<TeamSignupInfoVO> signedUpTeams;

@Data
@Schema(description = "球队报名信息")
public static class TeamSignupInfoVO {
    @Schema(description = "球队ID")
    private Long id;
    
    @Schema(description = "球队名称")
    private String name;
    
    @Schema(description = "球队Logo")
    private String logo;
    
    @Schema(description = "当前报名人数")
    private Integer currentPlayers;
}
```

### 2. 前端修改

#### 2.1 修改活动卡片满员判断逻辑

**文件**: `s-register-card.vue`

```javascript
// 计算注册是否已满
const isRegistrationFull = computed(() => {
  const activity = props.activity;
  if (!activity) return false;

  // 根据活动类型判断满员条件
  if (activity.activityType === 1) {
    // 排位赛：按人数判断
    return (activity.currentRegisters || 0) >= (activity.maxRegisters || 20);
  } else if (activity.activityType === 2 || activity.activityType === 3) {
    // 友谊赛或联赛：按报名成功的球队数量判断
    // 后端已修改 signedUpTeams 只包含满足最小人数的球队
    const currentSuccessfulTeams = activity.signedUpTeams ? activity.signedUpTeams.length : 0;
    const maxTeams = activity.maxTeams || (activity.activityType === 2 ? 2 : 16);
    return currentSuccessfulTeams >= maxTeams;
  }
  
  // 默认按人数判断
  return (activity.currentRegisters || 0) >= (activity.maxRegisters || 20);
});
```

#### 2.2 修改倒计时状态显示

**文件**: `SignupDeadlineCard.vue`

```javascript
// 获取过期状态文本
const getExpiredStatusText = () => {
  const status = props.status;
  
  // ACTIVITY_STATUS 枚举值：
  // GROUPING_SUCCESSFUL = 8, IN_PROGRESS = 4, COMPLETED = 5
  if (status === 8) {
    return '组局成功';
  } else if (status === 4) {
    return '比赛进行中';
  } else if (status === 5) {
    return '比赛已结束';
  } else {
    return '报名已截止';
  }
};
```

**模板修改**:
```html
<template v-if="isExpired">
  <text class="expired-text">{{ getExpiredStatusText() }}</text>
</template>
```

## 核心逻辑变更

### 球队报名成功判断标准

**修改前**: 只要有球员报名就算球队报名
**修改后**: 球队必须满足最小人数要求才算报名成功

- **友谊赛**: 球队已支付球员数 >= `minPlayersPerTeamFriendly`（默认6人）
- **联赛**: 球队已支付球员数 >= `minPlayersPerTeamLeague`（默认8人）

### 活动满员判断标准

**修改前**: 
- 友谊赛：按总报名人数判断
- 联赛：按总报名人数判断

**修改后**:
- **排位赛**: 已报名球员数 >= 最大人数限制（不变）
- **友谊赛**: 报名成功球队数 >= 2
- **联赛**: 报名成功球队数 >= 最大球队数限制

### 状态显示逻辑

**新增状态判断**:
- 组局成功(8) → "组局成功"
- 比赛进行中(4) → "比赛进行中"  
- 比赛已结束(5) → "比赛已结束"
- 其他 → "报名已截止"

## 测试场景

### 1. 友谊赛测试
```
场景：创建友谊赛，设置最小人数6人
- 球队A报名3人 → 不算报名成功
- 球队B报名7人 → 算报名成功
- 活动卡片显示：1/2 球队，不显示满员
- 球队C报名6人 → 算报名成功
- 活动卡片显示：2/2 球队，显示满员
```

### 2. 联赛测试
```
场景：创建联赛，设置最小人数8人，最大16队
- 10个球队各报名5人 → 0个报名成功
- 2个球队各报名8人 → 2个报名成功
- 活动卡片显示：2/16 球队，不显示满员
```

### 3. 状态显示测试
```
场景：活动组局成功
- 倒计时到期前：显示剩余时间
- 倒计时到期后：显示"组局成功"而不是"报名已截止"
```

## 影响范围

### 后端文件
- `ActivityServiceImpl.java` - 核心逻辑修改
- `AppActivityCardResponseVO.java` - VO扩展

### 前端文件  
- `s-register-card.vue` - 卡片满员判断
- `SignupDeadlineCard.vue` - 状态显示
- `detail.vue` - 详情页满员判断（已正确，无需修改）

### 数据库影响
- 无数据库结构变更
- 依赖现有字段：`minPlayersPerTeamFriendly`, `minPlayersPerTeamLeague`

## 注意事项

1. **缓存清理**: 修改会自动清除首页推荐活动缓存
2. **向后兼容**: 保持现有API接口不变，只增强数据内容
3. **错误处理**: 获取球队信息失败时使用默认名称，不影响主流程
4. **默认值**: 友谊赛默认6人，联赛默认8人，确保系统稳定性

## 版本信息

- **修改日期**: 2025-01-11
- **版本标签**: v1.2.3-waitlist-fix
- **影响模块**: operation-biz, app-ui
- **向后兼容**: 是
- **需要重启**: 是（后端） 