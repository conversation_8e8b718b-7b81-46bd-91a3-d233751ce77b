# 球员生涯模块API设计文档

## 1. 设计方案分析

### 1.1 接口设计方案对比

经过详细分析，我们对比了三种接口设计方案：

#### 方案一：拆分成3个独立接口
- **核心数据网格接口**
- **雷达图数据接口** 
- **最佳数据记录接口**

**优点**：
- 职责单一，便于维护
- 灵活性高，可按需加载
- 错误隔离，互不影响
- 便于并行开发

**缺点**：
- 网络请求多（3次HTTP请求）
- 状态管理复杂
- 可能存在数据一致性问题
- 接口维护成本高

#### 方案二：整合成1个接口
- **生涯概览大接口**（包含所有数据）

**优点**：
- 网络请求少（1次HTTP请求）
- 数据一致性好
- 状态管理简单
- 接口维护简单

**缺点**：
- 数据冗余，传输开销大
- 灵活性差，无法按需加载
- 缓存粒度粗
- 耦合度高

#### 方案三：混合方案（最终选择）
- **核心概览接口**：核心数据网格 + 雷达图数据
- **最佳数据独立接口**：最佳数据记录

**优点**：
- 平衡性能与灵活性
- 减少关键路径的网络开销
- 保证核心数据的一致性
- 最佳数据可按需加载

**缺点**：
- 相对复杂，但在可接受范围内

### 1.2 最终方案选择理由

选择**混合方案**的核心理由：

1. **业务关联性强**：核心数据网格和雷达图数据高度相关，用户总是同时查看
2. **性能优化**：减少关键路径的网络请求次数
3. **数据一致性**：确保核心展示数据来自同一次查询
4. **按需加载**：最佳数据相对独立，可以按需加载，节省不必要的数据传输
5. **缓存策略**：可以对不同类型的数据采用不同的缓存策略

## 2. 接口设计详情

### 2.1 接口1：球员生涯概览数据

```http
GET /league/player/career/overview
```

**功能**：获取球员核心数据网格和雷达图数据

**请求参数**：
- `playerId` (Long, 必填): 球员ID
- `season` (String, 必填): 赛季，如"2024"
- `gameType` (Integer, 可选): 比赛类型，0-全部,1-排位赛,2-友谊赛,3-联赛，默认0

**响应数据结构**：
```json
{
  "code": 0,
  "data": {
    "coreStats": {
      "basicStats": [
        {
          "label": "场均得分",
          "value": "15.2",
          "unit": "分",
          "valueType": "number"
        }
      ],
      "shootingStats": [...],
      "advancedStats": [...],
      "streakStats": [...]
    },
    "radarChart": {
      "dataPoints": [
        {
          "dimension": "效率",
          "score": 75.5,
          "maxValue": 100
        }
      ],
      "overallRating": 76.3,
      "sampleGames": 25
    }
  }
}
```

**性能目标**：
- 响应时间: <100ms
- 缓存时间: 15分钟

### 2.2 接口2：球员最佳数据记录

```http
GET /league/player/career/best-stats
```

**功能**：获取球员历史最佳数据记录

**请求参数**：
- `playerId` (Long, 必填): 球员ID
- `season` (String, 必填): 赛季，如"2024"
- `gameType` (Integer, 可选): 比赛类型，0-全部,1-排位赛,2-友谊赛,3-联赛，默认0

**响应数据结构**：
```json
{
  "code": 0,
  "data": {
    "seasonBest": [
      {
        "statName": "单场最高得分",
        "value": "32",
        "unit": "分",
        "achieveDate": "2024-03-15",
        "gameId": 1001,
        "opponent": "vs 雄鹰队",
        "gameResult": "胜利"
      }
    ],
    "careerBest": [...]
  }
}
```

**性能目标**：
- 响应时间: <200ms
- 缓存时间: 1小时

## 3. Mock数据设计

### 3.1 核心数据网格Mock数据

包含4个分类的统计数据：
- **基础统计数据**：场均得分、篮板、助攻、抢断、盖帽、失误
- **命中率统计数据**：投篮命中率、三分命中率、二分命中率、罚球命中率
- **高阶统计数据**：效率值、真实命中率、使用率、净胜分
- **连胜统计数据**：当前连胜、赛季最大连胜、生涯最大连胜、胜率

### 3.2 雷达图Mock数据

7维度能力评分：
- 效率：75.5分
- 得分：68.2分
- 篮板：82.1分
- 助攻：85.9分
- 防守：72.8分
- 失误控制：78.3分
- 犯规控制：71.6分

综合能力评分：76.3分，样本比赛：25场

### 3.3 最佳数据Mock数据

分为赛季最佳和生涯最佳两个维度，每个维度包含：
- 单场最高得分、篮板、助攻、效率
- 每项记录包含具体的比赛信息：日期、对手、比赛结果

## 4. 数据结构设计

### 4.1 响应VO类

- `AppPlayerCareerOverviewRespVO`: 生涯概览响应VO
- `AppPlayerBestStatsRespVO`: 最佳数据响应VO
- `AppPlayerCareerQueryReqVO`: 查询请求VO

### 4.2 内部数据结构

- `CoreStatsData`: 核心数据网格
- `RadarChartData`: 雷达图数据
- `StatItem`: 统计项
- `RadarDataPoint`: 雷达图数据点
- `BestStatItem`: 最佳数据项

## 5. 实施计划

### 5.1 当前阶段（已完成）
- ✅ 创建响应VO类
- ✅ 在AppPlayerController中新增两个接口
- ✅ 实现Mock数据逻辑
- ✅ 添加详细的接口文档

### 5.2 下一阶段
- 🔄 前端调用新接口，验证数据结构
- 🔄 根据前端反馈调整接口设计
- 🔄 实现真实的Service业务逻辑
- 🔄 添加缓存策略和性能优化

### 5.3 后续阶段
- ⏳ 数据库表结构设计和创建
- ⏳ 数据聚合和计算逻辑
- ⏳ 能力评分算法实现
- ⏳ 性能测试和优化

## 6. 技术优势

### 6.1 架构优势
- **数据分离**：相关性强的数据合并，独立性强的数据分离
- **性能优化**：减少网络请求，提升用户体验
- **扩展性**：为未来功能扩展预留空间
- **可维护性**：清晰的接口职责和数据结构

### 6.2 实现优势
- **Mock数据**：便于前后端并行开发
- **详细文档**：降低沟通成本
- **统一规范**：遵循项目编码规范
- **渐进实施**：分阶段平滑升级

## 7. 总结

通过详细的方案分析和对比，我们选择了混合方案作为最佳解决方案。这个设计既保证了核心功能的性能和一致性，又保持了适度的灵活性。当前已完成接口的基础设计和Mock数据实现，为后续的真实业务逻辑开发奠定了良好的基础。

接下来将重点关注前端集成验证和真实Service层的业务逻辑实现，确保整个球员生涯模块能够高效、稳定地运行。 