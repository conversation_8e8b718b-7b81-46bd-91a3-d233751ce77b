-- 修复 sd_registration 表中 version 字段为 null 的问题
-- 此脚本将所有 version 为 null 的记录设置为 0（乐观锁初始值）

-- 查看当前有多少条记录的 version 为 null
SELECT COUNT(*) as null_version_count 
FROM sd_registration 
WHERE version IS NULL;

-- 修复 version 字段为 null 的记录
UPDATE sd_registration 
SET version = 0 
WHERE version IS NULL;

-- 验证修复结果
SELECT COUNT(*) as remaining_null_count 
FROM sd_registration 
WHERE version IS NULL;

-- 查看修复后的统计信息
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN version IS NULL THEN 1 END) as null_version_count,
    COUNT(CASE WHEN version = 0 THEN 1 END) as zero_version_count,
    MIN(version) as min_version,
    MAX(version) as max_version
FROM sd_registration; 