package cn.iocoder.yudao.module.operation.service.registration;

import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;

import java.util.List;

/**
 * 报名状态查询服务接口
 * 
 * 🎯 目标：将复杂的状态组合查询逻辑内聚到专门的服务中，避免在各个业务服务中重复编写复杂的状态判断逻辑
 * 
 * 📋 职责：
 * 1. 提供各种业务场景下的报名记录查询方法
 * 2. 封装复杂的状态组合判断逻辑
 * 3. 统一状态查询的入口，便于维护和修改
 * 4. 减少代码重复，提高代码质量
 * 
 * <AUTHOR>
 */
public interface RegistrationStatusQueryService {

    // ==================== 基础状态查询 ====================
    
    /**
     * 查询活动的所有有效报名记录（已支付且未取消）
     * 
     * @param activityId 活动ID
     * @return 有效报名记录列表
     */
    List<RegistrationDO> getActiveRegistrations(Long activityId);
    
    /**
     * 查询活动的所有候补报名记录
     * 
     * @param activityId 活动ID
     * @return 候补报名记录列表，按候补位置排序
     */
    List<RegistrationDO> getWaitlistRegistrations(Long activityId);
    
    /**
     * 查询活动的所有正式报名记录（非候补）
     * 
     * @param activityId 活动ID
     * @return 正式报名记录列表
     */
    List<RegistrationDO> getFormalRegistrations(Long activityId);
    
    // ==================== 退款相关查询 ====================
    
    /**
     * 查询可以进行指定场景退款的报名记录
     * 
     * @param activityId 活动ID
     * @param scenario 退款场景
     * @return 可退款的报名记录列表
     */
    List<RegistrationDO> getRefundableRegistrations(Long activityId, RefundScenario scenario);
    
    /**
     * 查询正在退款中的报名记录
     * 
     * @param activityId 活动ID
     * @return 退款中的报名记录列表
     */
    List<RegistrationDO> getRefundingRegistrations(Long activityId);
    
    /**
     * 查询已完成退款的报名记录
     * 
     * @param activityId 活动ID
     * @return 已退款的报名记录列表
     */
    List<RegistrationDO> getRefundedRegistrations(Long activityId);
    
    // ==================== 分队相关查询 ====================
    
    /**
     * 查询可参与分队的报名记录（排除候补）
     * 
     * @param activityId 活动ID
     * @return 可分队的报名记录列表
     */
    List<RegistrationDO> getTeamAssignableRegistrations(Long activityId);
    
    /**
     * 查询指定球队的有效报名记录
     * 
     * @param activityId 活动ID
     * @param teamId 球队ID
     * @return 球队报名记录列表
     */
    List<RegistrationDO> getTeamRegistrations(Long activityId, Long teamId);
    
    // ==================== 统计查询 ====================
    
    /**
     * 统计活动的有效报名人数
     * 
     * @param activityId 活动ID
     * @return 有效报名人数
     */
    long countActiveRegistrations(Long activityId);
    
    /**
     * 统计活动的候补人数
     * 
     * @param activityId 活动ID
     * @return 候补人数
     */
    long countWaitlistRegistrations(Long activityId);
    
    /**
     * 统计指定球队的有效报名人数
     * 
     * @param activityId 活动ID
     * @param teamId 球队ID
     * @return 球队报名人数
     */
    long countTeamRegistrations(Long activityId, Long teamId);
    
    // ==================== 状态判断 ====================
    
    /**
     * 判断报名记录是否为有效状态（已支付且未取消）
     * 
     * @param registration 报名记录
     * @return 是否有效
     */
    boolean isActiveRegistration(RegistrationDO registration);
    
    /**
     * 判断报名记录是否为候补状态
     * 
     * @param registration 报名记录
     * @return 是否为候补
     */
    boolean isWaitlistRegistration(RegistrationDO registration);
    
    /**
     * 判断报名记录是否可以进行指定场景的退款
     * 
     * @param registration 报名记录
     * @param scenario 退款场景
     * @return 是否可退款
     */
    boolean canRefund(RegistrationDO registration, RefundScenario scenario);
    
    /**
     * 判断报名记录是否正在退款中
     * 
     * @param registration 报名记录
     * @return 是否退款中
     */
    boolean isRefunding(RegistrationDO registration);
    
    /**
     * 判断报名记录是否已完成退款
     * 
     * @param registration 报名记录
     * @return 是否已退款
     */
    boolean isRefunded(RegistrationDO registration);
}
