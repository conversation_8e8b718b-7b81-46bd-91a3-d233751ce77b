package cn.iocoder.yudao.module.operation.service.registration;

import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationCompositeStatusEnum;

/**
 * 报名状态管理器接口
 * 
 * 🎯 目标：提供统一的状态转换和状态检查服务
 * 
 * 📋 职责：
 * 1. 状态转换逻辑的统一管理
 * 2. 状态一致性检查
 * 3. 状态转换的业务规则验证
 * 4. 状态变更的原子性操作
 * 
 * <AUTHOR>
 */
public interface RegistrationStatusManager {

    /**
     * 获取报名记录的综合状态
     * 
     * @param registration 报名记录
     * @return 综合状态
     */
    RegistrationCompositeStatusEnum getCompositeStatus(RegistrationDO registration);

    /**
     * 检查状态一致性
     * 
     * @param registration 报名记录
     * @return 状态检查结果
     */
    StatusConsistencyResult checkStatusConsistency(RegistrationDO registration);

    /**
     * 验证状态转换是否合法
     * 
     * @param currentStatus 当前综合状态
     * @param targetStatus 目标综合状态
     * @param scenario 业务场景
     * @return 是否允许转换
     */
    boolean canTransition(RegistrationCompositeStatusEnum currentStatus, 
                         RegistrationCompositeStatusEnum targetStatus, 
                         String scenario);

    /**
     * 执行状态转换
     * 
     * @param registrationId 报名记录ID
     * @param targetStatus 目标状态
     * @param scenario 业务场景
     * @param operatorId 操作人ID
     * @return 转换结果
     */
    StatusTransitionResult transitionStatus(Long registrationId, 
                                          RegistrationCompositeStatusEnum targetStatus, 
                                          String scenario, 
                                          Long operatorId);

    /**
     * 状态一致性检查结果
     */
    class StatusConsistencyResult {
        private final boolean consistent;
        private final String errorMessage;
        private final String suggestion;

        public StatusConsistencyResult(boolean consistent, String errorMessage, String suggestion) {
            this.consistent = consistent;
            this.errorMessage = errorMessage;
            this.suggestion = suggestion;
        }

        public boolean isConsistent() {
            return consistent;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getSuggestion() {
            return suggestion;
        }

        public static StatusConsistencyResult success() {
            return new StatusConsistencyResult(true, null, null);
        }

        public static StatusConsistencyResult failure(String errorMessage, String suggestion) {
            return new StatusConsistencyResult(false, errorMessage, suggestion);
        }
    }

    /**
     * 状态转换结果
     */
    class StatusTransitionResult {
        private final boolean success;
        private final String errorMessage;
        private final RegistrationCompositeStatusEnum oldStatus;
        private final RegistrationCompositeStatusEnum newStatus;

        public StatusTransitionResult(boolean success, String errorMessage, 
                                    RegistrationCompositeStatusEnum oldStatus, 
                                    RegistrationCompositeStatusEnum newStatus) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.oldStatus = oldStatus;
            this.newStatus = newStatus;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public RegistrationCompositeStatusEnum getOldStatus() {
            return oldStatus;
        }

        public RegistrationCompositeStatusEnum getNewStatus() {
            return newStatus;
        }

        public static StatusTransitionResult success(RegistrationCompositeStatusEnum oldStatus, 
                                                   RegistrationCompositeStatusEnum newStatus) {
            return new StatusTransitionResult(true, null, oldStatus, newStatus);
        }

        public static StatusTransitionResult failure(String errorMessage, 
                                                   RegistrationCompositeStatusEnum currentStatus) {
            return new StatusTransitionResult(false, errorMessage, currentStatus, currentStatus);
        }
    }
}
